import type { ConfirmOptions, Connection, Public<PERSON><PERSON>, Signer } from '@solana/web3.js';
/**
 * <PERSON>reate and initialize a new associated token account
 *
 * @param connection               Connection to use
 * @param payer                    Payer of the transaction and initialization fees
 * @param mint                     Mint for the account
 * @param owner                    Owner of the new account
 * @param confirmOptions           Options for confirming the transaction
 * @param programId                SPL Token program account
 * @param associatedTokenProgramId SPL Associated Token program account
 *
 * @return Address of the new associated token account
 */
export declare function createAssociatedTokenAccount(connection: Connection, payer: Signer, mint: <PERSON><PERSON><PERSON>, owner: <PERSON>Key, confirmOptions?: ConfirmOptions, programId?: <PERSON>Key, associatedTokenProgramId?: PublicKey): Promise<PublicKey>;
//# sourceMappingURL=createAssociatedTokenAccount.d.ts.map