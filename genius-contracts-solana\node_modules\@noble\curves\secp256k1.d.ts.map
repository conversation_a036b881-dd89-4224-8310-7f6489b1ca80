{"version": 3, "file": "secp256k1.d.ts", "sourceRoot": "", "sources": ["src/secp256k1.ts"], "names": [], "mappings": "AAUA,OAAO,EAAe,KAAK,iBAAiB,EAAE,MAAM,oBAAoB,CAAC;AACzE,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAEL,KAAK,SAAS,EACd,KAAK,SAAS,EAEf,MAAM,6BAA6B,CAAC;AACrC,OAAO,EAAyB,GAAG,EAAQ,MAAM,uBAAuB,CAAC;AACzE,OAAO,EAIL,KAAK,gBAAgB,IAAI,SAAS,EAElC,KAAK,oBAAoB,EAC1B,MAAM,2BAA2B,CAAC;AACnC,OAAO,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAC/C,OAAO,EAEL,eAAe,EAIf,eAAe,EAChB,MAAM,YAAY,CAAC;AAyDpB;;;;;;;;;;;;;GAaG;AACH,eAAO,MAAM,SAAS,EAAE,iBAGvB,CAAC;AAMF,iBAAS,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,QAAQ,EAAE,UAAU,EAAE,GAAG,UAAU,CAQtE;AAkBD;;;GAGG;AACH,iBAAS,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAS5C;AASD;;GAEG;AACH,iBAAS,mBAAmB,CAAC,SAAS,EAAE,GAAG,GAAG,UAAU,CAEvD;AAED;;;GAGG;AACH,iBAAS,WAAW,CAAC,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,GAAE,GAAqB,GAAG,UAAU,CAgBjG;AAED;;;GAGG;AACH,iBAAS,aAAa,CAAC,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,GAAG,OAAO,CAoB5E;AAED,MAAM,MAAM,WAAW,GAAG;IACxB,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,UAAU,KAAK;QAAE,SAAS,EAAE,UAAU,CAAC;QAAC,SAAS,EAAE,UAAU,CAAA;KAAE,CAAC;IAChF,YAAY,EAAE,OAAO,mBAAmB,CAAC;IACzC,IAAI,EAAE,OAAO,WAAW,CAAC;IACzB,MAAM,EAAE,OAAO,aAAa,CAAC;IAC7B,KAAK,EAAE,oBAAoB,CAAC,MAAM,CAAC,CAAC;IACpC,KAAK,EAAE;QACL,eAAe,EAAE,CAAC,IAAI,CAAC,EAAE,UAAU,KAAK,UAAU,CAAC;QACnD,YAAY,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,KAAK,UAAU,CAAC;QACvD,MAAM,EAAE,OAAO,MAAM,CAAC;QACtB,UAAU,EAAE,OAAO,UAAU,CAAC;QAE9B,wCAAwC;QACxC,gBAAgB,EAAE,CAAC,IAAI,CAAC,EAAE,UAAU,KAAK,UAAU,CAAC;QACpD,8BAA8B;QAC9B,eAAe,EAAE,OAAO,eAAe,CAAC;QACxC,8BAA8B;QAC9B,eAAe,EAAE,OAAO,eAAe,CAAC;QACxC,gCAAgC;QAChC,GAAG,EAAE,OAAO,GAAG,CAAC;KACjB,CAAC;IACF,IAAI,EAAE;QAAE,IAAI,EAAE,aAAa,CAAC;QAAC,kBAAkB,EAAE,KAAK,CAAC;QAAC,OAAO,EAAE,SAAS,CAAC,SAAS,CAAC,CAAA;KAAE,CAAC;CACzF,CAAC;AACF;;;;;;;;;;;;GAYG;AACH,eAAO,MAAM,OAAO,EAAE,WAyClB,CAAC;AA0CL,wEAAwE;AACxE,eAAO,MAAM,gBAAgB,EAAE,SAAS,CAAC,MAAM,CAgBzC,CAAC;AAEP,uFAAuF;AACvF,eAAO,MAAM,WAAW,EAAE,SAAS,CAAC,MAAM,CACT,CAAC;AAElC,uFAAuF;AACvF,eAAO,MAAM,aAAa,EAAE,SAAS,CAAC,MAAM,CACT,CAAC"}