{"name": "stream-json", "version": "1.9.1", "description": "stream-json is the micro-library of Node.js stream components for creating custom JSON processing pipelines with a minimal memory footprint. It can parse JSON files far exceeding available memory streaming individual primitives using a SAX-inspired API. Includes utilities to stream JSON database dumps.", "homepage": "http://github.com/uhop/stream-json", "bugs": "http://github.com/uhop/stream-json/issues", "main": "index.js", "directories": {"test": "tests"}, "dependencies": {"stream-chain": "^2.2.5"}, "devDependencies": {"heya-unit": "^0.3.0"}, "scripts": {"test": "node tests/tests.js", "debug": "node --inspect-brk tests/tests.js"}, "github": "http://github.com/uhop/stream-json", "repository": {"type": "git", "url": "git://github.com/uhop/stream-json.git"}, "keywords": ["scanner", "lexer", "tokenizer", "parser", "django", "stream", "streaming", "json"], "author": "<PERSON> <<EMAIL>> (http://lazutkin.com/)", "license": "BSD-3-<PERSON><PERSON>", "files": ["/*.js", "/filters", "/jsonl", "/streamers", "/utils"]}