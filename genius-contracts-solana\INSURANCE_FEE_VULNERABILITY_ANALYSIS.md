# Insurance Fee Vulnerability Analysis

## Executive Summary

**CRITICAL VULNERABILITY CONFIRMED**: The Solana Asset Manager accumulates insurance fees indefinitely without providing any mechanism to reclaim them, causing available liquidity to permanently decrease over time and potentially enabling griefing attacks.

## Vulnerability Details

### Root Cause
The vulnerability exists because:
1. `getAvailableLiquidity()` excludes `unclaimed_insurance_fee` from available liquidity calculations
2. `unclaimed_insurance_fee` is incremented on every order but **never decremented**
3. The `claim_fees` instruction only handles Base, LP, and Protocol fees - **NOT Insurance fees**
4. There's no `FeeType::Insurance` variant in the enum, making insurance fees **impossible to claim**

### Code Evidence

#### 1. FeeType Enum Missing Insurance Variant
**File**: `programs/genius/src/constant.rs` (Lines 30-37)
```rust
#[derive(Default, Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, AnchorDeserialize, AnchorSerialize)]
#[repr(u8)]
pub enum FeeType {
    #[default]
    Base = 0,
    Lp = 1,
    Protocol = 2,
    // ❌ NO Insurance = 3 variant!
}
```

#### 2. getAvailableLiquidity() Excludes Insurance Fees
**File**: `genius-actions/src/services/blockchain/vault/solana/svm-asset-manager.ts` (Lines 173-176)
```typescript
const unclaimedFees =
  BigInt(asset.unclaimedBaseFee.toString()) +
  BigInt(asset.unclaimedLpFee.toString()) +
  BigInt(asset.unclaimedProtocolFee.toString());
  // ❌ Missing: BigInt(asset.unclaimedInsuranceFee.toString())
```

#### 3. Insurance Fees Accumulate on Every Order
**File**: `programs/genius/src/instructions/create_order.rs` (Line 201)
```rust
ctx.accounts.asset.unclaimed_insurance_fee += insurance_fee;
// ❌ This field is NEVER decremented anywhere in the codebase!
```

#### 4. Claim Fees Only Handles Three Fee Types
**File**: `programs/genius/src/instructions/claim_fees.rs` (Lines 82-99)
```rust
match fee_type {
    FeeType::Base => {
        // Can claim base fees
        asset.unclaimed_base_fee -= amount;
    }
    FeeType::Lp => {
        // Can claim LP fees
        asset.unclaimed_lp_fee -= amount;
    }
    FeeType::Protocol => {
        // Can claim protocol fees
        asset.unclaimed_protocol_fee -= amount;
    }
    // ❌ NO case for Insurance fees!
}
```

#### 5. Helper Function Also Excludes Insurance Fees
**File**: `programs/genius/src/state.rs` (Lines 257-261)
```rust
impl Asset {
    pub fn get_unclaimed_fees(&self) -> u64 {
        self.unclaimed_base_fee
            + self.unclaimed_lp_fee
            + self.unclaimed_protocol_fee
        // ❌ Missing: + self.unclaimed_insurance_fee
    }
}
```

## Impact Analysis

### 1. Permanent Liquidity Loss
- Insurance fees accumulate with every order (typically 0.1% of order amount)
- These fees are permanently excluded from available liquidity calculations
- Over time, available liquidity decreases indefinitely
- Eventually could make the protocol unusable due to insufficient liquidity

### 2. Griefing Attack Vector
- Attackers can create many small orders to rapidly accumulate insurance fees
- Each order reduces available liquidity by the insurance fee amount
- No way to recover these fees, making the attack permanent
- Cost to attacker is minimal (just gas + minimum fees)

### 3. Economic Impact
For a protocol processing $1M daily volume with 0.1% insurance fees:
- Daily insurance fee accumulation: $1,000
- Monthly accumulation: $30,000
- Annual accumulation: $365,000
- **All permanently locked and unusable**

## Proof of Concept

The provided test file `tests/insurance-fee-vulnerability-poc.ts` demonstrates:

1. **Setup**: Initialize protocol with insurance fee tiers (0.1% fee)
2. **Accumulation**: Create multiple orders, each accumulating insurance fees
3. **Claiming**: Successfully claim Base, LP, and Protocol fees
4. **Verification**: Confirm insurance fees remain unclaimed and unclaimable
5. **Impact**: Show permanent reduction in available liquidity

### Expected Test Results
```
Initial unclaimed insurance fee: 0
Unclaimed insurance fee after orders: 50 (5 orders × 10 fee each)
Successfully claimed base fees: 10
Successfully claimed LP fees: 10  
Successfully claimed protocol fees: 5
Final unclaimed insurance fee: 50 ← STILL UNCLAIMED!

=== VULNERABILITY IMPACT ===
Insurance fees that cannot be reclaimed: 50
This amount is permanently excluded from available liquidity calculations
Over time, this will cause available liquidity to decrease indefinitely
Potential for griefing attacks by creating many small orders to accumulate fees
```

## Recommended Fix

Add `Insurance` variant to `FeeType` enum and handle it in `claim_fees`:

```rust
// In constant.rs
#[derive(Default, Debug, Clone, Copy, PartialEq, Eq, AnchorDeserialize, AnchorSerialize)]
#[repr(u8)]
pub enum FeeType {
    #[default]
    Base = 0,
    Lp = 1,
    Protocol = 2,
    Insurance = 3,  // ✅ Add this
}

// In claim_fees.rs
match fee_type {
    // ... existing cases ...
    FeeType::Insurance => {
        require!((orchestrator_state.claim_insurance_fee_permission == true), GeniusError::InvalidOrchestratorPermission);
        require!(asset.unclaimed_insurance_fee >= amount, GeniusError::InvalidAmount);
        asset.unclaimed_insurance_fee -= amount;  // ✅ Add this
    }
}
```

## Severity: CRITICAL

This vulnerability causes permanent loss of funds and enables griefing attacks. It should be fixed immediately before any mainnet deployment.
