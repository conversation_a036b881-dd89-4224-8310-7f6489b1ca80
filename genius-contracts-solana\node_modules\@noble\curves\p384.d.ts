/**
 * NIST secp384r1 aka p384.
 * @module
 */
/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */
import { type H2CMethod } from './abstract/hash-to-curve.ts';
import { p384 as p384n } from './nist.ts';
/** @deprecated use `import { p384 } from '@noble/curves/nist.js';` */
export declare const p384: typeof p384n;
/** @deprecated use `import { p384 } from '@noble/curves/nist.js';` */
export declare const secp384r1: typeof p384n;
/** @deprecated use `import { p384_hasher } from '@noble/curves/nist.js';` */
export declare const hashToCurve: H2CMethod<bigint>;
/** @deprecated use `import { p384_hasher } from '@noble/curves/nist.js';` */
export declare const encodeToCurve: H2CMethod<bigint>;
//# sourceMappingURL=p384.d.ts.map