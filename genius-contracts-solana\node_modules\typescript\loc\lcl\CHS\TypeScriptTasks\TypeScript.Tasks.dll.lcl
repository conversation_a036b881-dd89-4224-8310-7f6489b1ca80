﻿<?xml version="1.0" encoding="utf-8"?>
<LCX SchemaVersion="6.0" Name="E:\A\_work\326\s\VS\TypeScriptTasks\bin\Release\TypeScript.Tasks.dll" PsrId="211" FileType="1" SrcCul="en-US" TgtCul="zh-CN" xmlns="http://schemas.microsoft.com/locstudio/2006/6/lcx">
  <OwnedComments>
    <Cmt Name="Dev" />
    <Cmt Name="LcxAdmin" />
    <Cmt Name="RCCX" />
  </OwnedComments>
  <Settings Name="@vsLocTools@\default.lss" Type="Lss" />
  <Item ItemId=";Managed Resources" ItemType="0" PsrId="211" Leaf="true">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
  </Item>
  <Item ItemId=";TypeScript.Tasks.Strings.resources" ItemType="0" PsrId="211" Leaf="false">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" Path=" \ ;Managed Resources \ 0 \ 0" />
    <Item ItemId=";Strings" ItemType="0" PsrId="211" Leaf="false">
      <Disp Icon="Str" Disp="true" LocTbl="false" />
      <Item ItemId=";CompilerLogNotSpecified" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[No compiler log specified, 'Clean' won't work.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[未指定任何编译器日志，“清理”将不起作用。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ConfigFileFoundOnDisk" ItemType="0" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[This project is using one or more tsconfig.json files for the build that may not be properly used by IntelliSense. Please set the item type of each tsconfig.json file to TypeScriptCompile or Content to ensure they are used properly by the editor.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[此项目正在针对生成使用一个或多个 tsconfig.json 文件，而 IntelliSense 可能未正确使用它们。请将每个 tsconfig.json 文件的项目类型设置为 TypeScriptCompile 或 Content，确保编辑器均正确使用它们。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ErrorListBuildPrefix" ItemType="0" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Build:]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生成:]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ErrorWritingLog" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Failed to write compiler log to '{0}. Exception: '{1}']]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[无法将编译器日志写入“{0}”。 异常:“{1}”]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";FallbackVersionWarning" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Using compiler version ({0}), if this is incorrect change the <TypeScriptToolsVersion> element in your project file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[使用编译器版本({0})，如果这不正确，则更改项目文件中的 <TypeScriptToolsVersion> 元素。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";LocatedReferenceFilesAt_0" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Located references file at: '{0}'.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[已将引用文件放置在“{0}”处。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NoCompilerError" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your project file uses a different version of the TypeScript compiler and tools than is currently installed on this machine.  No compiler was found at {0}.  You may be able to fix this problem by changing the <TypeScriptToolsVersion> element in your project file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[你的项目文件使用的 TypeScript 编译器和工具的版本不同于此计算机上当前安装的版本。在 {0} 处未找到编译器。通过更改项目文件中的 <TypeScriptToolsVersion> 元素，或许可以修复此问题。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";NodeNotFound" ItemType="0" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[The build task could not find node.exe which is required to run the TypeScript compiler. Please install Node and ensure that the system path contains its location.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[生成任务找不到运行 TypeScript 编译器所需的 node.exe。请安装 Node 并确保系统路径包含其位置。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";ToolsVersionWarning" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Couldn't locate the compiler version ({0}) specified in the project file.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[找不到项目文件中指定的编译器版本({0})。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TypeScriptCompileBlockedSet" ItemType="0" PsrId="211" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[TypeScript compile is skipped because the TypeScriptCompileBlocked property is set to 'true'.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[由于 TypeScriptCompileBlocked 属性设置为 "true"，因此会跳过 TypeScript 编译。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TypeScriptNoVersionWarning" ItemType="0" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your project does not specify a TypeScriptToolsVersion. The latest available TypeScript compiler will be used ({0}). To remove this warning, set TypeScriptToolsVersion to a specific version or "Latest" to always select the latest compiler.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[项目未指定 TypeScriptToolsVersion。系统将使用最新可用的 TypeScript 编译器({0})。若要消除此警告，请将 TypeScriptToolsVersion 设置为特定版本或“最新版本”以始终选择最新编译器。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
      <Item ItemId=";TypeScriptVersionMismatchWarning" ItemType="0" PsrId="211" InstFlg="true" Leaf="true">
        <Str Cat="Text">
          <Val><![CDATA[Your project specifies TypeScriptToolsVersion {0}, but a matching compiler was not found. The latest available TypeScript compiler will be used ({1}). To remove this warning, install the TypeScript {0} SDK or update the value of TypeScriptToolsVersion.]]></Val>
          <Tgt Cat="Text" Stat="Loc" Orig="New">
            <Val><![CDATA[项目指定 TypeScriptToolsVersion {0}，但未找到匹配的编译器。系统将使用最新可用的 TypeScript 编译器({1})。若要消除此警告，请安装 TypeScript {0} SDK 或更新 TypeScriptToolsVersion 的值。]]></Val>
          </Tgt>
        </Str>
        <Disp Icon="Str" />
      </Item>
    </Item>
  </Item>
  <Item ItemId=";Version" ItemType="0" PsrId="211" Leaf="false">
    <Disp Icon="Ver" Disp="true" LocTbl="false" Path=" \ ;Version \ 8 \ 0" />
    <Item ItemId=";CompanyName" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text">
        <Val><![CDATA[Microsoft Corporation]]></Val>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId=";FileDescription" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text">
        <Val><![CDATA[TypeScript Build Tasks]]></Val>
        <Tgt Cat="Text" Stat="Loc" Orig="New">
          <Val><![CDATA[TypeScript 生成任务]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId=";InternalName" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text" DevLk="true">
        <Val><![CDATA[TypeScript.Tasks.dll]]></Val>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId=";LegalCopyright" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text">
        <Val><![CDATA[© Microsoft Corporation. All rights reserved.]]></Val>
        <Tgt Cat="Text" Stat="Loc" Orig="New">
          <Val><![CDATA[© Microsoft Corporation。保留所有权利。]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId=";OriginalFilename" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text" DevLk="true">
        <Val><![CDATA[TypeScript.Tasks.dll]]></Val>
      </Str>
      <Disp Icon="Str" />
    </Item>
    <Item ItemId=";ProductName" ItemType="0" PsrId="211" Leaf="true">
      <Str Cat="Text">
        <Val><![CDATA[TypeScript Build Tasks]]></Val>
        <Tgt Cat="Text" Stat="Loc" Orig="New">
          <Val><![CDATA[TypeScript 生成任务]]></Val>
        </Tgt>
      </Str>
      <Disp Icon="Str" />
    </Item>
  </Item>
  <Item ItemId=";Version" ItemType="8" PsrId="211" Leaf="true">
    <Disp Icon="Expand" Expand="true" Disp="true" LocTbl="false" />
  </Item>
</LCX>