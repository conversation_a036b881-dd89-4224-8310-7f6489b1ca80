# Environment Variables for Genius Actions E2E Testing

# Lit Protocol Configuration
LIT_RPC_URL=https://datil-dev-relayer.getlit.dev

# Executor Configuration (Private key for test wallet)
# WARNING: Never use real private keys with funds in testing!
# Generate a test private key for development only
EXECUTOR_PK=your_test_private_key_here

# Database Configuration (if using PostgreSQL for testing)
DATABASE_URL=postgresql://username:password@localhost:5432/genius_test

# Network RPC URLs for testing
ETHEREUM_RPC_URL=https://eth-mainnet.alchemyapi.io/v2/your-api-key
POLYGON_RPC_URL=https://polygon-mainnet.alchemyapi.io/v2/your-api-key
BASE_RPC_URL=https://base-mainnet.alchemyapi.io/v2/your-api-key
ARBITRUM_RPC_URL=https://arb-mainnet.alchemyapi.io/v2/your-api-key
OPTIMISM_RPC_URL=https://opt-mainnet.alchemyapi.io/v2/your-api-key
BSC_RPC_URL=https://bsc-dataseed.binance.org/
AVAX_RPC_URL=https://api.avax.network/ext/bc/C/rpc
SONIC_RPC_URL=your_sonic_rpc_url_here

# API Keys
ETHERSCAN_API_KEY=your_etherscan_api_key_here

# Test Configuration
NODE_ENV=test
LOG_LEVEL=debug

# Instructions:
# 1. Copy this file to .env in the genius-actions directory
# 2. Replace placeholder values with actual test values
# 3. For EXECUTOR_PK, generate a test wallet private key (never use real funds)
# 4. Set up test RPC endpoints or use public ones for testing
# 5. Obtain API keys from respective services for full functionality
