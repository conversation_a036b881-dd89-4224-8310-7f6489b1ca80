{"name": "@solana/errors", "version": "2.0.0-preview.2", "description": "Throw, identify, and decode Solana JavaScript errors", "exports": {"browser": {"import": "./dist/index.browser.js", "require": "./dist/index.browser.cjs"}, "node": {"import": "./dist/index.node.js", "require": "./dist/index.node.cjs"}, "react-native": "./dist/index.native.js", "types": "./dist/types/index.d.ts"}, "browser": {"./dist/index.node.cjs": "./dist/index.browser.cjs", "./dist/index.node.js": "./dist/index.browser.js"}, "main": "./dist/index.node.cjs", "module": "./dist/index.node.js", "react-native": "./dist/index.native.js", "types": "./dist/types/index.d.ts", "type": "module", "files": ["./dist/"], "sideEffects": false, "keywords": ["blockchain", "solana", "web3"], "bin": "./bin/cli.js", "author": "Solana Labs Maintainers <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/solana-labs/solana-web3.js"}, "bugs": {"url": "http://github.com/solana-labs/solana-web3.js/issues"}, "browserslist": ["supports bigint and not dead", "maintained node versions"], "engine": {"node": ">=17.4"}, "dependencies": {"chalk": "^5.3.0", "commander": "^12.0.0"}, "bundlewatch": {"defaultCompression": "gzip", "files": [{"path": "./dist/index*.js"}]}, "scripts": {"compile:js": "tsup --config build-scripts/tsup.config.package.ts && tsup src/cli.ts --format esm", "compile:typedefs": "tsc -p ./tsconfig.declarations.json && node ../../node_modules/@solana/build-scripts/add-js-extension-to-types.mjs", "dev": "jest -c ../../node_modules/@solana/test-config/jest-dev.config.ts --rootDir . --watch", "publish-impl": "npm view $npm_package_name@$npm_package_version > /dev/null 2>&1 || pnpm publish --tag ${PUBLISH_TAG:-preview} --access public --no-git-checks", "publish-packages": "pnpm prepublishOnly && pnpm publish-impl", "style:fix": "pnpm eslint --fix src/* && pnpm prettier -w src/* package.json", "test:lint": "jest -c ../../node_modules/@solana/test-config/jest-lint.config.ts --rootDir . --silent", "test:prettier": "jest -c ../../node_modules/@solana/test-config/jest-prettier.config.ts --rootDir . --silent", "test:treeshakability:browser": "agadoo dist/index.browser.js", "test:treeshakability:native": "agadoo dist/index.native.js", "test:treeshakability:node": "agadoo dist/index.node.js", "test:typecheck": "tsc --noEmit", "test:unit:browser": "jest -c ../../node_modules/@solana/test-config/jest-unit.config.browser.ts --rootDir . --silent", "test:unit:node": "jest -c ../../node_modules/@solana/test-config/jest-unit.config.node.ts --rootDir . --silent"}}