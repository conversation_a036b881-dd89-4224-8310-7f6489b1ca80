{"name": "rpc-websockets", "version": "7.11.2", "description": "JSON-RPC 2.0 implementation over WebSockets for Node.js", "main": "./dist/index.cjs", "types": "./dist/index.d.cts", "scripts": {"build": "tsc && mkdir -p ./dist && eslint --fix -c ./.eslintrc './src/**/*.cts' && ./build-browser-bundle.mjs", "pretest": "npm run-script build", "test": "mocha --exit test/*spec.js", "test:client": "mocha --exit test/client.spec.js", "test:server": "mocha --exit test/server.spec.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec --exit && cat ./coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js && rm -rf ./coverage", "coveralls": "npm run-script coverage && coveralls < ./coverage/lcov.info"}, "repository": {"type": "git", "url": "git+https://github.com/elpheria/rpc-websockets.git"}, "dependencies": {"eventemitter3": "^4.0.7", "uuid": "^8.3.2", "ws": "^8.5.0"}, "optionalDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "devDependencies": {"@babel/core": "^7.24.5", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.16.11", "@types/uuid": "^8.3.4", "@types/ws": "^8.2.2", "@typescript-eslint/eslint-plugin": "^5.11.0", "@typescript-eslint/parser": "^5.11.0", "async": "^3.2.3", "babel-eslint": "^10.1.0", "chai": "^4.3.6", "coveralls": "^3.1.1", "esbuild": "^0.20.2", "esbuild-plugin-babel": "^0.2.3", "eslint": "^8.8.0", "istanbul": "^0.4.5", "mocha": "^10.0.0", "mocha-lcov-reporter": "^1.3.0", "typescript": "^4.5.5"}, "browser": "./dist/index.browser.cjs", "keywords": ["json", "rpc", "websocket", "ws", "client", "server"], "author": "Elpheria", "license": "LGPL-3.0-only", "bugs": {"url": "https://github.com/elpheria/rpc-websockets/issues"}, "homepage": "https://github.com/elpheria/rpc-websockets#readme", "funding": {"type": "paypal", "url": "https://paypal.me/kozjak"}}