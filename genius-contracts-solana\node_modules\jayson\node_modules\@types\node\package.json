{"name": "@types/node", "version": "12.20.55", "description": "TypeScript definitions for Node.js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node", "license": "MIT", "contributors": [{"name": "Microsoft TypeScript", "url": "https://github.com/Microsoft", "githubUsername": "Microsoft"}, {"name": "DefinitelyTyped", "url": "https://github.com/DefinitelyTyped", "githubUsername": "DefinitelyTyped"}, {"name": "<PERSON>", "url": "https://github.com/jkomyno", "githubUsername": "jkomyno"}, {"name": "Alvis <PERSON>", "url": "https://github.com/alvis", "githubUsername": "alvis"}, {"name": "<PERSON>", "url": "https://github.com/r3nya", "githubUsername": "r3nya"}, {"name": "<PERSON>", "url": "https://github.com/btoueg", "githubUsername": "btoueg"}, {"name": "Chigozirim C.", "url": "https://github.com/smac89", "githubUsername": "smac89"}, {"name": "<PERSON>", "url": "https://github.com/touffy", "githubUsername": "touffy"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/DeividasBakanas", "githubUsername": "DeividasBakanas"}, {"name": "<PERSON>", "url": "https://github.com/eyqs", "githubUsername": "eyqs"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>-<PERSON>-CK", "githubUsername": "Hannes-<PERSON><PERSON>-CK"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/KSXGitHub", "githubUsername": "KSXGitHub"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/hoo29", "githubUsername": "hoo29"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kjin", "githubUsername": "kjin"}, {"name": "<PERSON>", "url": "https://github.com/ajafff", "githubUsername": "a<PERSON><PERSON><PERSON>"}, {"name": "Lishude", "url": "https://github.com/islishude", "githubUsername": "islishude"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mwiktorczyk", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/mohsen1", "githubUsername": "mohsen1"}, {"name": "<PERSON>", "url": "https://github.com/n-e", "githubUsername": "n-e"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/galkin", "githubUsername": "galkin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/parambirs", "githubUsername": "parambirs"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ThomasdenH", "githubUsername": "ThomasdenH"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/WilcoBakker", "githubUsername": "WilcoBakker"}, {"name": "wwwy3y3", "url": "https://github.com/wwwy3y3", "githubUsername": "wwwy3y3"}, {"name": "<PERSON>", "url": "https://github.com/ZaneHannanAU", "githubUsername": "ZaneHannanAU"}, {"name": "<PERSON>", "url": "https://github.com/samuela", "githubUsername": "samuela"}, {"name": "<PERSON>", "url": "https://github.com/kuehlein", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/bhongy", "githubUsername": "b<PERSON>y"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/chyzwar", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/trivikr", "githubUsername": "trivikr"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/yoursunny", "githubUsername": "<PERSON><PERSON>ny"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/qwelias", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "ExE Boss", "url": "https://github.com/ExE-Boss", "githubUsername": "ExE-Boss"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "3d29774cbf5180f3bd5b1bd954e268a18a74c90d34acff15c56308ec98960bec", "typeScriptVersion": "3.9"}