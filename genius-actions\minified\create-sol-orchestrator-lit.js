"use strict";(()=>{var dE=Object.create;var Li=Object.defineProperty;var hE=Object.getOwnPropertyDescriptor;var _E=Object.getOwnPropertyNames;var pE=Object.getPrototypeOf,RE=Object.prototype.hasOwnProperty;var Lt=(r,e)=>()=>(r&&(e=r(r=0)),e);var Re=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),EE=(r,e)=>{for(var t in e)Li(r,t,{get:e[t],enumerable:!0})},yu=(r,e,t,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of _E(e))!RE.call(r,i)&&i!==t&&Li(r,i,{get:()=>e[i],enumerable:!(n=hE(e,i))||n.enumerable});return r};var Au=(r,e,t)=>(t=r!=null?dE(pE(r)):{},yu(e||!r||!r.__esModule?Li(t,"default",{value:r,enumerable:!0}):t,r)),mu=r=>yu(Li({},"__esModule",{value:!0}),r);var Ou=Re(ki=>{"use strict";z();ki.byteLength=yE;ki.toByteArray=mE;ki.fromByteArray=OE;var dr=[],nr=[],gE=typeof Uint8Array<"u"?Uint8Array:Array,ks="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(nn=0,Su=ks.length;nn<Su;++nn)dr[nn]=ks[nn],nr[ks.charCodeAt(nn)]=nn;var nn,Su;nr[45]=62;nr[95]=63;function bu(r){var e=r.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var t=r.indexOf("=");t===-1&&(t=e);var n=t===e?0:4-t%4;return[t,n]}function yE(r){var e=bu(r),t=e[0],n=e[1];return(t+n)*3/4-n}function AE(r,e,t){return(e+t)*3/4-t}function mE(r){var e,t=bu(r),n=t[0],i=t[1],o=new gE(AE(r,n,i)),s=0,u=i>0?n-4:n,l;for(l=0;l<u;l+=4)e=nr[r.charCodeAt(l)]<<18|nr[r.charCodeAt(l+1)]<<12|nr[r.charCodeAt(l+2)]<<6|nr[r.charCodeAt(l+3)],o[s++]=e>>16&255,o[s++]=e>>8&255,o[s++]=e&255;return i===2&&(e=nr[r.charCodeAt(l)]<<2|nr[r.charCodeAt(l+1)]>>4,o[s++]=e&255),i===1&&(e=nr[r.charCodeAt(l)]<<10|nr[r.charCodeAt(l+1)]<<4|nr[r.charCodeAt(l+2)]>>2,o[s++]=e>>8&255,o[s++]=e&255),o}function SE(r){return dr[r>>18&63]+dr[r>>12&63]+dr[r>>6&63]+dr[r&63]}function bE(r,e,t){for(var n,i=[],o=e;o<t;o+=3)n=(r[o]<<16&16711680)+(r[o+1]<<8&65280)+(r[o+2]&255),i.push(SE(n));return i.join("")}function OE(r){for(var e,t=r.length,n=t%3,i=[],o=16383,s=0,u=t-n;s<u;s+=o)i.push(bE(r,s,s+o>u?u:s+o));return n===1?(e=r[t-1],i.push(dr[e>>2]+dr[e<<4&63]+"==")):n===2&&(e=(r[t-2]<<8)+r[t-1],i.push(dr[e>>10]+dr[e>>4&63]+dr[e<<2&63]+"=")),i.join("")}});var wu=Re(Bs=>{z();Bs.read=function(r,e,t,n,i){var o,s,u=i*8-n-1,l=(1<<u)-1,p=l>>1,R=-7,g=t?i-1:0,v=t?-1:1,m=r[e+g];for(g+=v,o=m&(1<<-R)-1,m>>=-R,R+=u;R>0;o=o*256+r[e+g],g+=v,R-=8);for(s=o&(1<<-R)-1,o>>=-R,R+=n;R>0;s=s*256+r[e+g],g+=v,R-=8);if(o===0)o=1-p;else{if(o===l)return s?NaN:(m?-1:1)*(1/0);s=s+Math.pow(2,n),o=o-p}return(m?-1:1)*s*Math.pow(2,o-n)};Bs.write=function(r,e,t,n,i,o){var s,u,l,p=o*8-i-1,R=(1<<p)-1,g=R>>1,v=i===23?Math.pow(2,-24)-Math.pow(2,-77):0,m=n?0:o-1,x=n?1:-1,I=e<0||e===0&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(u=isNaN(e)?1:0,s=R):(s=Math.floor(Math.log(e)/Math.LN2),e*(l=Math.pow(2,-s))<1&&(s--,l*=2),s+g>=1?e+=v/l:e+=v*Math.pow(2,1-g),e*l>=2&&(s++,l/=2),s+g>=R?(u=0,s=R):s+g>=1?(u=(e*l-1)*Math.pow(2,i),s=s+g):(u=e*Math.pow(2,g-1)*Math.pow(2,i),s=0));i>=8;r[t+m]=u&255,m+=x,u/=256,i-=8);for(s=s<<i|u,p+=i;p>0;r[t+m]=s&255,m+=x,s/=256,p-=8);r[t+m-x]|=I*128}});var Tn=Re(Nn=>{"use strict";z();var Ds=Ou(),wn=wu(),Iu=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):null;Nn.Buffer=V;Nn.SlowBuffer=CE;Nn.INSPECT_MAX_BYTES=50;var Bi=2147483647;Nn.kMaxLength=Bi;V.TYPED_ARRAY_SUPPORT=wE();!V.TYPED_ARRAY_SUPPORT&&typeof console<"u"&&typeof console.error=="function"&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");function wE(){try{let r=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(r,e),r.foo()===42}catch{return!1}}Object.defineProperty(V.prototype,"parent",{enumerable:!0,get:function(){if(V.isBuffer(this))return this.buffer}});Object.defineProperty(V.prototype,"offset",{enumerable:!0,get:function(){if(V.isBuffer(this))return this.byteOffset}});function wr(r){if(r>Bi)throw new RangeError('The value "'+r+'" is invalid for option "size"');let e=new Uint8Array(r);return Object.setPrototypeOf(e,V.prototype),e}function V(r,e,t){if(typeof r=="number"){if(typeof e=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return Fs(r)}return Cu(r,e,t)}V.poolSize=8192;function Cu(r,e,t){if(typeof r=="string")return NE(r,e);if(ArrayBuffer.isView(r))return TE(r);if(r==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof r);if(hr(r,ArrayBuffer)||r&&hr(r.buffer,ArrayBuffer)||typeof SharedArrayBuffer<"u"&&(hr(r,SharedArrayBuffer)||r&&hr(r.buffer,SharedArrayBuffer)))return Us(r,e,t);if(typeof r=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');let n=r.valueOf&&r.valueOf();if(n!=null&&n!==r)return V.from(n,e,t);let i=vE(r);if(i)return i;if(typeof Symbol<"u"&&Symbol.toPrimitive!=null&&typeof r[Symbol.toPrimitive]=="function")return V.from(r[Symbol.toPrimitive]("string"),e,t);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof r)}V.from=function(r,e,t){return Cu(r,e,t)};Object.setPrototypeOf(V.prototype,Uint8Array.prototype);Object.setPrototypeOf(V,Uint8Array);function xu(r){if(typeof r!="number")throw new TypeError('"size" argument must be of type number');if(r<0)throw new RangeError('The value "'+r+'" is invalid for option "size"')}function IE(r,e,t){return xu(r),r<=0?wr(r):e!==void 0?typeof t=="string"?wr(r).fill(e,t):wr(r).fill(e):wr(r)}V.alloc=function(r,e,t){return IE(r,e,t)};function Fs(r){return xu(r),wr(r<0?0:zs(r)|0)}V.allocUnsafe=function(r){return Fs(r)};V.allocUnsafeSlow=function(r){return Fs(r)};function NE(r,e){if((typeof e!="string"||e==="")&&(e="utf8"),!V.isEncoding(e))throw new TypeError("Unknown encoding: "+e);let t=Lu(r,e)|0,n=wr(t),i=n.write(r,e);return i!==t&&(n=n.slice(0,i)),n}function Ms(r){let e=r.length<0?0:zs(r.length)|0,t=wr(e);for(let n=0;n<e;n+=1)t[n]=r[n]&255;return t}function TE(r){if(hr(r,Uint8Array)){let e=new Uint8Array(r);return Us(e.buffer,e.byteOffset,e.byteLength)}return Ms(r)}function Us(r,e,t){if(e<0||r.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(r.byteLength<e+(t||0))throw new RangeError('"length" is outside of buffer bounds');let n;return e===void 0&&t===void 0?n=new Uint8Array(r):t===void 0?n=new Uint8Array(r,e):n=new Uint8Array(r,e,t),Object.setPrototypeOf(n,V.prototype),n}function vE(r){if(V.isBuffer(r)){let e=zs(r.length)|0,t=wr(e);return t.length===0||r.copy(t,0,0,e),t}if(r.length!==void 0)return typeof r.length!="number"||Gs(r.length)?wr(0):Ms(r);if(r.type==="Buffer"&&Array.isArray(r.data))return Ms(r.data)}function zs(r){if(r>=Bi)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+Bi.toString(16)+" bytes");return r|0}function CE(r){return+r!=r&&(r=0),V.alloc(+r)}V.isBuffer=function(e){return e!=null&&e._isBuffer===!0&&e!==V.prototype};V.compare=function(e,t){if(hr(e,Uint8Array)&&(e=V.from(e,e.offset,e.byteLength)),hr(t,Uint8Array)&&(t=V.from(t,t.offset,t.byteLength)),!V.isBuffer(e)||!V.isBuffer(t))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;let n=e.length,i=t.length;for(let o=0,s=Math.min(n,i);o<s;++o)if(e[o]!==t[o]){n=e[o],i=t[o];break}return n<i?-1:i<n?1:0};V.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}};V.concat=function(e,t){if(!Array.isArray(e))throw new TypeError('"list" argument must be an Array of Buffers');if(e.length===0)return V.alloc(0);let n;if(t===void 0)for(t=0,n=0;n<e.length;++n)t+=e[n].length;let i=V.allocUnsafe(t),o=0;for(n=0;n<e.length;++n){let s=e[n];if(hr(s,Uint8Array))o+s.length>i.length?(V.isBuffer(s)||(s=V.from(s)),s.copy(i,o)):Uint8Array.prototype.set.call(i,s,o);else if(V.isBuffer(s))s.copy(i,o);else throw new TypeError('"list" argument must be an Array of Buffers');o+=s.length}return i};function Lu(r,e){if(V.isBuffer(r))return r.length;if(ArrayBuffer.isView(r)||hr(r,ArrayBuffer))return r.byteLength;if(typeof r!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof r);let t=r.length,n=arguments.length>2&&arguments[2]===!0;if(!n&&t===0)return 0;let i=!1;for(;;)switch(e){case"ascii":case"latin1":case"binary":return t;case"utf8":case"utf-8":return Ps(r).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return t*2;case"hex":return t>>>1;case"base64":return Vu(r).length;default:if(i)return n?-1:Ps(r).length;e=(""+e).toLowerCase(),i=!0}}V.byteLength=Lu;function xE(r,e,t){let n=!1;if((e===void 0||e<0)&&(e=0),e>this.length||((t===void 0||t>this.length)&&(t=this.length),t<=0)||(t>>>=0,e>>>=0,t<=e))return"";for(r||(r="utf8");;)switch(r){case"hex":return VE(this,e,t);case"utf8":case"utf-8":return Bu(this,e,t);case"ascii":return FE(this,e,t);case"latin1":case"binary":return zE(this,e,t);case"base64":return UE(this,e,t);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return GE(this,e,t);default:if(n)throw new TypeError("Unknown encoding: "+r);r=(r+"").toLowerCase(),n=!0}}V.prototype._isBuffer=!0;function on(r,e,t){let n=r[e];r[e]=r[t],r[t]=n}V.prototype.swap16=function(){let e=this.length;if(e%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let t=0;t<e;t+=2)on(this,t,t+1);return this};V.prototype.swap32=function(){let e=this.length;if(e%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let t=0;t<e;t+=4)on(this,t,t+3),on(this,t+1,t+2);return this};V.prototype.swap64=function(){let e=this.length;if(e%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let t=0;t<e;t+=8)on(this,t,t+7),on(this,t+1,t+6),on(this,t+2,t+5),on(this,t+3,t+4);return this};V.prototype.toString=function(){let e=this.length;return e===0?"":arguments.length===0?Bu(this,0,e):xE.apply(this,arguments)};V.prototype.toLocaleString=V.prototype.toString;V.prototype.equals=function(e){if(!V.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e?!0:V.compare(this,e)===0};V.prototype.inspect=function(){let e="",t=Nn.INSPECT_MAX_BYTES;return e=this.toString("hex",0,t).replace(/(.{2})/g,"$1 ").trim(),this.length>t&&(e+=" ... "),"<Buffer "+e+">"};Iu&&(V.prototype[Iu]=V.prototype.inspect);V.prototype.compare=function(e,t,n,i,o){if(hr(e,Uint8Array)&&(e=V.from(e,e.offset,e.byteLength)),!V.isBuffer(e))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(t===void 0&&(t=0),n===void 0&&(n=e?e.length:0),i===void 0&&(i=0),o===void 0&&(o=this.length),t<0||n>e.length||i<0||o>this.length)throw new RangeError("out of range index");if(i>=o&&t>=n)return 0;if(i>=o)return-1;if(t>=n)return 1;if(t>>>=0,n>>>=0,i>>>=0,o>>>=0,this===e)return 0;let s=o-i,u=n-t,l=Math.min(s,u),p=this.slice(i,o),R=e.slice(t,n);for(let g=0;g<l;++g)if(p[g]!==R[g]){s=p[g],u=R[g];break}return s<u?-1:u<s?1:0};function ku(r,e,t,n,i){if(r.length===0)return-1;if(typeof t=="string"?(n=t,t=0):t>2147483647?t=2147483647:t<-2147483648&&(t=-2147483648),t=+t,Gs(t)&&(t=i?0:r.length-1),t<0&&(t=r.length+t),t>=r.length){if(i)return-1;t=r.length-1}else if(t<0)if(i)t=0;else return-1;if(typeof e=="string"&&(e=V.from(e,n)),V.isBuffer(e))return e.length===0?-1:Nu(r,e,t,n,i);if(typeof e=="number")return e=e&255,typeof Uint8Array.prototype.indexOf=="function"?i?Uint8Array.prototype.indexOf.call(r,e,t):Uint8Array.prototype.lastIndexOf.call(r,e,t):Nu(r,[e],t,n,i);throw new TypeError("val must be string, number or Buffer")}function Nu(r,e,t,n,i){let o=1,s=r.length,u=e.length;if(n!==void 0&&(n=String(n).toLowerCase(),n==="ucs2"||n==="ucs-2"||n==="utf16le"||n==="utf-16le")){if(r.length<2||e.length<2)return-1;o=2,s/=2,u/=2,t/=2}function l(R,g){return o===1?R[g]:R.readUInt16BE(g*o)}let p;if(i){let R=-1;for(p=t;p<s;p++)if(l(r,p)===l(e,R===-1?0:p-R)){if(R===-1&&(R=p),p-R+1===u)return R*o}else R!==-1&&(p-=p-R),R=-1}else for(t+u>s&&(t=s-u),p=t;p>=0;p--){let R=!0;for(let g=0;g<u;g++)if(l(r,p+g)!==l(e,g)){R=!1;break}if(R)return p}return-1}V.prototype.includes=function(e,t,n){return this.indexOf(e,t,n)!==-1};V.prototype.indexOf=function(e,t,n){return ku(this,e,t,n,!0)};V.prototype.lastIndexOf=function(e,t,n){return ku(this,e,t,n,!1)};function LE(r,e,t,n){t=Number(t)||0;let i=r.length-t;n?(n=Number(n),n>i&&(n=i)):n=i;let o=e.length;n>o/2&&(n=o/2);let s;for(s=0;s<n;++s){let u=parseInt(e.substr(s*2,2),16);if(Gs(u))return s;r[t+s]=u}return s}function kE(r,e,t,n){return Di(Ps(e,r.length-t),r,t,n)}function BE(r,e,t,n){return Di(WE(e),r,t,n)}function DE(r,e,t,n){return Di(Vu(e),r,t,n)}function ME(r,e,t,n){return Di($E(e,r.length-t),r,t,n)}V.prototype.write=function(e,t,n,i){if(t===void 0)i="utf8",n=this.length,t=0;else if(n===void 0&&typeof t=="string")i=t,n=this.length,t=0;else if(isFinite(t))t=t>>>0,isFinite(n)?(n=n>>>0,i===void 0&&(i="utf8")):(i=n,n=void 0);else throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");let o=this.length-t;if((n===void 0||n>o)&&(n=o),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");i||(i="utf8");let s=!1;for(;;)switch(i){case"hex":return LE(this,e,t,n);case"utf8":case"utf-8":return kE(this,e,t,n);case"ascii":case"latin1":case"binary":return BE(this,e,t,n);case"base64":return DE(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return ME(this,e,t,n);default:if(s)throw new TypeError("Unknown encoding: "+i);i=(""+i).toLowerCase(),s=!0}};V.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function UE(r,e,t){return e===0&&t===r.length?Ds.fromByteArray(r):Ds.fromByteArray(r.slice(e,t))}function Bu(r,e,t){t=Math.min(r.length,t);let n=[],i=e;for(;i<t;){let o=r[i],s=null,u=o>239?4:o>223?3:o>191?2:1;if(i+u<=t){let l,p,R,g;switch(u){case 1:o<128&&(s=o);break;case 2:l=r[i+1],(l&192)===128&&(g=(o&31)<<6|l&63,g>127&&(s=g));break;case 3:l=r[i+1],p=r[i+2],(l&192)===128&&(p&192)===128&&(g=(o&15)<<12|(l&63)<<6|p&63,g>2047&&(g<55296||g>57343)&&(s=g));break;case 4:l=r[i+1],p=r[i+2],R=r[i+3],(l&192)===128&&(p&192)===128&&(R&192)===128&&(g=(o&15)<<18|(l&63)<<12|(p&63)<<6|R&63,g>65535&&g<1114112&&(s=g))}}s===null?(s=65533,u=1):s>65535&&(s-=65536,n.push(s>>>10&1023|55296),s=56320|s&1023),n.push(s),i+=u}return PE(n)}var Tu=4096;function PE(r){let e=r.length;if(e<=Tu)return String.fromCharCode.apply(String,r);let t="",n=0;for(;n<e;)t+=String.fromCharCode.apply(String,r.slice(n,n+=Tu));return t}function FE(r,e,t){let n="";t=Math.min(r.length,t);for(let i=e;i<t;++i)n+=String.fromCharCode(r[i]&127);return n}function zE(r,e,t){let n="";t=Math.min(r.length,t);for(let i=e;i<t;++i)n+=String.fromCharCode(r[i]);return n}function VE(r,e,t){let n=r.length;(!e||e<0)&&(e=0),(!t||t<0||t>n)&&(t=n);let i="";for(let o=e;o<t;++o)i+=YE[r[o]];return i}function GE(r,e,t){let n=r.slice(e,t),i="";for(let o=0;o<n.length-1;o+=2)i+=String.fromCharCode(n[o]+n[o+1]*256);return i}V.prototype.slice=function(e,t){let n=this.length;e=~~e,t=t===void 0?n:~~t,e<0?(e+=n,e<0&&(e=0)):e>n&&(e=n),t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),t<e&&(t=e);let i=this.subarray(e,t);return Object.setPrototypeOf(i,V.prototype),i};function bt(r,e,t){if(r%1!==0||r<0)throw new RangeError("offset is not uint");if(r+e>t)throw new RangeError("Trying to access beyond buffer length")}V.prototype.readUintLE=V.prototype.readUIntLE=function(e,t,n){e=e>>>0,t=t>>>0,n||bt(e,t,this.length);let i=this[e],o=1,s=0;for(;++s<t&&(o*=256);)i+=this[e+s]*o;return i};V.prototype.readUintBE=V.prototype.readUIntBE=function(e,t,n){e=e>>>0,t=t>>>0,n||bt(e,t,this.length);let i=this[e+--t],o=1;for(;t>0&&(o*=256);)i+=this[e+--t]*o;return i};V.prototype.readUint8=V.prototype.readUInt8=function(e,t){return e=e>>>0,t||bt(e,1,this.length),this[e]};V.prototype.readUint16LE=V.prototype.readUInt16LE=function(e,t){return e=e>>>0,t||bt(e,2,this.length),this[e]|this[e+1]<<8};V.prototype.readUint16BE=V.prototype.readUInt16BE=function(e,t){return e=e>>>0,t||bt(e,2,this.length),this[e]<<8|this[e+1]};V.prototype.readUint32LE=V.prototype.readUInt32LE=function(e,t){return e=e>>>0,t||bt(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+this[e+3]*16777216};V.prototype.readUint32BE=V.prototype.readUInt32BE=function(e,t){return e=e>>>0,t||bt(e,4,this.length),this[e]*16777216+(this[e+1]<<16|this[e+2]<<8|this[e+3])};V.prototype.readBigUInt64LE=Mr(function(e){e=e>>>0,In(e,"offset");let t=this[e],n=this[e+7];(t===void 0||n===void 0)&&Wn(e,this.length-8);let i=t+this[++e]*2**8+this[++e]*2**16+this[++e]*2**24,o=this[++e]+this[++e]*2**8+this[++e]*2**16+n*2**24;return BigInt(i)+(BigInt(o)<<BigInt(32))});V.prototype.readBigUInt64BE=Mr(function(e){e=e>>>0,In(e,"offset");let t=this[e],n=this[e+7];(t===void 0||n===void 0)&&Wn(e,this.length-8);let i=t*2**24+this[++e]*2**16+this[++e]*2**8+this[++e],o=this[++e]*2**24+this[++e]*2**16+this[++e]*2**8+n;return(BigInt(i)<<BigInt(32))+BigInt(o)});V.prototype.readIntLE=function(e,t,n){e=e>>>0,t=t>>>0,n||bt(e,t,this.length);let i=this[e],o=1,s=0;for(;++s<t&&(o*=256);)i+=this[e+s]*o;return o*=128,i>=o&&(i-=Math.pow(2,8*t)),i};V.prototype.readIntBE=function(e,t,n){e=e>>>0,t=t>>>0,n||bt(e,t,this.length);let i=t,o=1,s=this[e+--i];for(;i>0&&(o*=256);)s+=this[e+--i]*o;return o*=128,s>=o&&(s-=Math.pow(2,8*t)),s};V.prototype.readInt8=function(e,t){return e=e>>>0,t||bt(e,1,this.length),this[e]&128?(255-this[e]+1)*-1:this[e]};V.prototype.readInt16LE=function(e,t){e=e>>>0,t||bt(e,2,this.length);let n=this[e]|this[e+1]<<8;return n&32768?n|4294901760:n};V.prototype.readInt16BE=function(e,t){e=e>>>0,t||bt(e,2,this.length);let n=this[e+1]|this[e]<<8;return n&32768?n|4294901760:n};V.prototype.readInt32LE=function(e,t){return e=e>>>0,t||bt(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24};V.prototype.readInt32BE=function(e,t){return e=e>>>0,t||bt(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]};V.prototype.readBigInt64LE=Mr(function(e){e=e>>>0,In(e,"offset");let t=this[e],n=this[e+7];(t===void 0||n===void 0)&&Wn(e,this.length-8);let i=this[e+4]+this[e+5]*2**8+this[e+6]*2**16+(n<<24);return(BigInt(i)<<BigInt(32))+BigInt(t+this[++e]*2**8+this[++e]*2**16+this[++e]*2**24)});V.prototype.readBigInt64BE=Mr(function(e){e=e>>>0,In(e,"offset");let t=this[e],n=this[e+7];(t===void 0||n===void 0)&&Wn(e,this.length-8);let i=(t<<24)+this[++e]*2**16+this[++e]*2**8+this[++e];return(BigInt(i)<<BigInt(32))+BigInt(this[++e]*2**24+this[++e]*2**16+this[++e]*2**8+n)});V.prototype.readFloatLE=function(e,t){return e=e>>>0,t||bt(e,4,this.length),wn.read(this,e,!0,23,4)};V.prototype.readFloatBE=function(e,t){return e=e>>>0,t||bt(e,4,this.length),wn.read(this,e,!1,23,4)};V.prototype.readDoubleLE=function(e,t){return e=e>>>0,t||bt(e,8,this.length),wn.read(this,e,!0,52,8)};V.prototype.readDoubleBE=function(e,t){return e=e>>>0,t||bt(e,8,this.length),wn.read(this,e,!1,52,8)};function Ht(r,e,t,n,i,o){if(!V.isBuffer(r))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw new RangeError('"value" argument is out of bounds');if(t+n>r.length)throw new RangeError("Index out of range")}V.prototype.writeUintLE=V.prototype.writeUIntLE=function(e,t,n,i){if(e=+e,t=t>>>0,n=n>>>0,!i){let u=Math.pow(2,8*n)-1;Ht(this,e,t,n,u,0)}let o=1,s=0;for(this[t]=e&255;++s<n&&(o*=256);)this[t+s]=e/o&255;return t+n};V.prototype.writeUintBE=V.prototype.writeUIntBE=function(e,t,n,i){if(e=+e,t=t>>>0,n=n>>>0,!i){let u=Math.pow(2,8*n)-1;Ht(this,e,t,n,u,0)}let o=n-1,s=1;for(this[t+o]=e&255;--o>=0&&(s*=256);)this[t+o]=e/s&255;return t+n};V.prototype.writeUint8=V.prototype.writeUInt8=function(e,t,n){return e=+e,t=t>>>0,n||Ht(this,e,t,1,255,0),this[t]=e&255,t+1};V.prototype.writeUint16LE=V.prototype.writeUInt16LE=function(e,t,n){return e=+e,t=t>>>0,n||Ht(this,e,t,2,65535,0),this[t]=e&255,this[t+1]=e>>>8,t+2};V.prototype.writeUint16BE=V.prototype.writeUInt16BE=function(e,t,n){return e=+e,t=t>>>0,n||Ht(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=e&255,t+2};V.prototype.writeUint32LE=V.prototype.writeUInt32LE=function(e,t,n){return e=+e,t=t>>>0,n||Ht(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=e&255,t+4};V.prototype.writeUint32BE=V.prototype.writeUInt32BE=function(e,t,n){return e=+e,t=t>>>0,n||Ht(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=e&255,t+4};function Du(r,e,t,n,i){zu(e,n,i,r,t,7);let o=Number(e&BigInt(4294967295));r[t++]=o,o=o>>8,r[t++]=o,o=o>>8,r[t++]=o,o=o>>8,r[t++]=o;let s=Number(e>>BigInt(32)&BigInt(4294967295));return r[t++]=s,s=s>>8,r[t++]=s,s=s>>8,r[t++]=s,s=s>>8,r[t++]=s,t}function Mu(r,e,t,n,i){zu(e,n,i,r,t,7);let o=Number(e&BigInt(4294967295));r[t+7]=o,o=o>>8,r[t+6]=o,o=o>>8,r[t+5]=o,o=o>>8,r[t+4]=o;let s=Number(e>>BigInt(32)&BigInt(4294967295));return r[t+3]=s,s=s>>8,r[t+2]=s,s=s>>8,r[t+1]=s,s=s>>8,r[t]=s,t+8}V.prototype.writeBigUInt64LE=Mr(function(e,t=0){return Du(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))});V.prototype.writeBigUInt64BE=Mr(function(e,t=0){return Mu(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))});V.prototype.writeIntLE=function(e,t,n,i){if(e=+e,t=t>>>0,!i){let l=Math.pow(2,8*n-1);Ht(this,e,t,n,l-1,-l)}let o=0,s=1,u=0;for(this[t]=e&255;++o<n&&(s*=256);)e<0&&u===0&&this[t+o-1]!==0&&(u=1),this[t+o]=(e/s>>0)-u&255;return t+n};V.prototype.writeIntBE=function(e,t,n,i){if(e=+e,t=t>>>0,!i){let l=Math.pow(2,8*n-1);Ht(this,e,t,n,l-1,-l)}let o=n-1,s=1,u=0;for(this[t+o]=e&255;--o>=0&&(s*=256);)e<0&&u===0&&this[t+o+1]!==0&&(u=1),this[t+o]=(e/s>>0)-u&255;return t+n};V.prototype.writeInt8=function(e,t,n){return e=+e,t=t>>>0,n||Ht(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=e&255,t+1};V.prototype.writeInt16LE=function(e,t,n){return e=+e,t=t>>>0,n||Ht(this,e,t,2,32767,-32768),this[t]=e&255,this[t+1]=e>>>8,t+2};V.prototype.writeInt16BE=function(e,t,n){return e=+e,t=t>>>0,n||Ht(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=e&255,t+2};V.prototype.writeInt32LE=function(e,t,n){return e=+e,t=t>>>0,n||Ht(this,e,t,4,2147483647,-2147483648),this[t]=e&255,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4};V.prototype.writeInt32BE=function(e,t,n){return e=+e,t=t>>>0,n||Ht(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=e&255,t+4};V.prototype.writeBigInt64LE=Mr(function(e,t=0){return Du(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))});V.prototype.writeBigInt64BE=Mr(function(e,t=0){return Mu(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))});function Uu(r,e,t,n,i,o){if(t+n>r.length)throw new RangeError("Index out of range");if(t<0)throw new RangeError("Index out of range")}function Pu(r,e,t,n,i){return e=+e,t=t>>>0,i||Uu(r,e,t,4,34028234663852886e22,-34028234663852886e22),wn.write(r,e,t,n,23,4),t+4}V.prototype.writeFloatLE=function(e,t,n){return Pu(this,e,t,!0,n)};V.prototype.writeFloatBE=function(e,t,n){return Pu(this,e,t,!1,n)};function Fu(r,e,t,n,i){return e=+e,t=t>>>0,i||Uu(r,e,t,8,17976931348623157e292,-17976931348623157e292),wn.write(r,e,t,n,52,8),t+8}V.prototype.writeDoubleLE=function(e,t,n){return Fu(this,e,t,!0,n)};V.prototype.writeDoubleBE=function(e,t,n){return Fu(this,e,t,!1,n)};V.prototype.copy=function(e,t,n,i){if(!V.isBuffer(e))throw new TypeError("argument should be a Buffer");if(n||(n=0),!i&&i!==0&&(i=this.length),t>=e.length&&(t=e.length),t||(t=0),i>0&&i<n&&(i=n),i===n||e.length===0||this.length===0)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("Index out of range");if(i<0)throw new RangeError("sourceEnd out of bounds");i>this.length&&(i=this.length),e.length-t<i-n&&(i=e.length-t+n);let o=i-n;return this===e&&typeof Uint8Array.prototype.copyWithin=="function"?this.copyWithin(t,n,i):Uint8Array.prototype.set.call(e,this.subarray(n,i),t),o};V.prototype.fill=function(e,t,n,i){if(typeof e=="string"){if(typeof t=="string"?(i=t,t=0,n=this.length):typeof n=="string"&&(i=n,n=this.length),i!==void 0&&typeof i!="string")throw new TypeError("encoding must be a string");if(typeof i=="string"&&!V.isEncoding(i))throw new TypeError("Unknown encoding: "+i);if(e.length===1){let s=e.charCodeAt(0);(i==="utf8"&&s<128||i==="latin1")&&(e=s)}}else typeof e=="number"?e=e&255:typeof e=="boolean"&&(e=Number(e));if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;t=t>>>0,n=n===void 0?this.length:n>>>0,e||(e=0);let o;if(typeof e=="number")for(o=t;o<n;++o)this[o]=e;else{let s=V.isBuffer(e)?e:V.from(e,i),u=s.length;if(u===0)throw new TypeError('The value "'+e+'" is invalid for argument "value"');for(o=0;o<n-t;++o)this[o+t]=s[o%u]}return this};var On={};function Vs(r,e,t){On[r]=class extends t{constructor(){super(),Object.defineProperty(this,"message",{value:e.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${r}]`,this.stack,delete this.name}get code(){return r}set code(i){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:i,writable:!0})}toString(){return`${this.name} [${r}]: ${this.message}`}}}Vs("ERR_BUFFER_OUT_OF_BOUNDS",function(r){return r?`${r} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError);Vs("ERR_INVALID_ARG_TYPE",function(r,e){return`The "${r}" argument must be of type number. Received type ${typeof e}`},TypeError);Vs("ERR_OUT_OF_RANGE",function(r,e,t){let n=`The value of "${r}" is out of range.`,i=t;return Number.isInteger(t)&&Math.abs(t)>2**32?i=vu(String(t)):typeof t=="bigint"&&(i=String(t),(t>BigInt(2)**BigInt(32)||t<-(BigInt(2)**BigInt(32)))&&(i=vu(i)),i+="n"),n+=` It must be ${e}. Received ${i}`,n},RangeError);function vu(r){let e="",t=r.length,n=r[0]==="-"?1:0;for(;t>=n+4;t-=3)e=`_${r.slice(t-3,t)}${e}`;return`${r.slice(0,t)}${e}`}function HE(r,e,t){In(e,"offset"),(r[e]===void 0||r[e+t]===void 0)&&Wn(e,r.length-(t+1))}function zu(r,e,t,n,i,o){if(r>t||r<e){let s=typeof e=="bigint"?"n":"",u;throw o>3?e===0||e===BigInt(0)?u=`>= 0${s} and < 2${s} ** ${(o+1)*8}${s}`:u=`>= -(2${s} ** ${(o+1)*8-1}${s}) and < 2 ** ${(o+1)*8-1}${s}`:u=`>= ${e}${s} and <= ${t}${s}`,new On.ERR_OUT_OF_RANGE("value",u,r)}HE(n,i,o)}function In(r,e){if(typeof r!="number")throw new On.ERR_INVALID_ARG_TYPE(e,"number",r)}function Wn(r,e,t){throw Math.floor(r)!==r?(In(r,t),new On.ERR_OUT_OF_RANGE(t||"offset","an integer",r)):e<0?new On.ERR_BUFFER_OUT_OF_BOUNDS:new On.ERR_OUT_OF_RANGE(t||"offset",`>= ${t?1:0} and <= ${e}`,r)}var KE=/[^+/0-9A-Za-z-_]/g;function qE(r){if(r=r.split("=")[0],r=r.trim().replace(KE,""),r.length<2)return"";for(;r.length%4!==0;)r=r+"=";return r}function Ps(r,e){e=e||1/0;let t,n=r.length,i=null,o=[];for(let s=0;s<n;++s){if(t=r.charCodeAt(s),t>55295&&t<57344){if(!i){if(t>56319){(e-=3)>-1&&o.push(239,191,189);continue}else if(s+1===n){(e-=3)>-1&&o.push(239,191,189);continue}i=t;continue}if(t<56320){(e-=3)>-1&&o.push(239,191,189),i=t;continue}t=(i-55296<<10|t-56320)+65536}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,t<128){if((e-=1)<0)break;o.push(t)}else if(t<2048){if((e-=2)<0)break;o.push(t>>6|192,t&63|128)}else if(t<65536){if((e-=3)<0)break;o.push(t>>12|224,t>>6&63|128,t&63|128)}else if(t<1114112){if((e-=4)<0)break;o.push(t>>18|240,t>>12&63|128,t>>6&63|128,t&63|128)}else throw new Error("Invalid code point")}return o}function WE(r){let e=[];for(let t=0;t<r.length;++t)e.push(r.charCodeAt(t)&255);return e}function $E(r,e){let t,n,i,o=[];for(let s=0;s<r.length&&!((e-=2)<0);++s)t=r.charCodeAt(s),n=t>>8,i=t%256,o.push(i),o.push(n);return o}function Vu(r){return Ds.toByteArray(qE(r))}function Di(r,e,t,n){let i;for(i=0;i<n&&!(i+t>=e.length||i>=r.length);++i)e[i+t]=r[i];return i}function hr(r,e){return r instanceof e||r!=null&&r.constructor!=null&&r.constructor.name!=null&&r.constructor.name===e.name}function Gs(r){return r!==r}var YE=function(){let r="0123456789abcdef",e=new Array(256);for(let t=0;t<16;++t){let n=t*16;for(let i=0;i<16;++i)e[n+i]=r[t]+r[i]}return e}();function Mr(r){return typeof BigInt>"u"?jE:r}function jE(){throw new Error("BigInt not supported")}});var Gu,z=Lt(()=>{"use strict";Gu=Au(Tn());globalThis.Buffer=Gu.Buffer});var $n=Re(sn=>{"use strict";z();Object.defineProperty(sn,"__esModule",{value:!0});sn.anumber=Hs;sn.abytes=Hu;sn.ahash=ZE;sn.aexists=JE;sn.aoutput=QE;function Hs(r){if(!Number.isSafeInteger(r)||r<0)throw new Error("positive integer expected, got "+r)}function XE(r){return r instanceof Uint8Array||ArrayBuffer.isView(r)&&r.constructor.name==="Uint8Array"}function Hu(r,...e){if(!XE(r))throw new Error("Uint8Array expected");if(e.length>0&&!e.includes(r.length))throw new Error("Uint8Array expected of length "+e+", got length="+r.length)}function ZE(r){if(typeof r!="function"||typeof r.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");Hs(r.outputLen),Hs(r.blockLen)}function JE(r,e=!0){if(r.destroyed)throw new Error("Hash instance has been destroyed");if(e&&r.finished)throw new Error("Hash#digest() has already been called")}function QE(r,e){Hu(r);let t=e.outputLen;if(r.length<t)throw new Error("digestInto() expects output buffer of length at least "+t)}});var Ku=Re(Mi=>{"use strict";z();Object.defineProperty(Mi,"__esModule",{value:!0});Mi.crypto=void 0;Mi.crypto=typeof globalThis=="object"&&"crypto"in globalThis?globalThis.crypto:void 0});var Nr=Re(ve=>{"use strict";z();Object.defineProperty(ve,"__esModule",{value:!0});ve.Hash=ve.nextTick=ve.byteSwapIfBE=ve.isLE=void 0;ve.isBytes=eg;ve.u8=tg;ve.u32=rg;ve.createView=ng;ve.rotr=ig;ve.rotl=og;ve.byteSwap=Ws;ve.byteSwap32=sg;ve.bytesToHex=cg;ve.hexToBytes=ug;ve.asyncLoop=fg;ve.utf8ToBytes=Wu;ve.toBytes=Ui;ve.concatBytes=dg;ve.checkOpts=hg;ve.wrapConstructor=_g;ve.wrapConstructorWithOpts=pg;ve.wrapXOFConstructorWithOpts=Rg;ve.randomBytes=Eg;var vn=Ku(),qs=$n();function eg(r){return r instanceof Uint8Array||ArrayBuffer.isView(r)&&r.constructor.name==="Uint8Array"}function tg(r){return new Uint8Array(r.buffer,r.byteOffset,r.byteLength)}function rg(r){return new Uint32Array(r.buffer,r.byteOffset,Math.floor(r.byteLength/4))}function ng(r){return new DataView(r.buffer,r.byteOffset,r.byteLength)}function ig(r,e){return r<<32-e|r>>>e}function og(r,e){return r<<e|r>>>32-e>>>0}ve.isLE=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function Ws(r){return r<<24&4278190080|r<<8&16711680|r>>>8&65280|r>>>24&255}ve.byteSwapIfBE=ve.isLE?r=>r:r=>Ws(r);function sg(r){for(let e=0;e<r.length;e++)r[e]=Ws(r[e])}var ag=Array.from({length:256},(r,e)=>e.toString(16).padStart(2,"0"));function cg(r){(0,qs.abytes)(r);let e="";for(let t=0;t<r.length;t++)e+=ag[r[t]];return e}var Ir={_0:48,_9:57,A:65,F:70,a:97,f:102};function qu(r){if(r>=Ir._0&&r<=Ir._9)return r-Ir._0;if(r>=Ir.A&&r<=Ir.F)return r-(Ir.A-10);if(r>=Ir.a&&r<=Ir.f)return r-(Ir.a-10)}function ug(r){if(typeof r!="string")throw new Error("hex string expected, got "+typeof r);let e=r.length,t=e/2;if(e%2)throw new Error("hex string expected, got unpadded hex of length "+e);let n=new Uint8Array(t);for(let i=0,o=0;i<t;i++,o+=2){let s=qu(r.charCodeAt(o)),u=qu(r.charCodeAt(o+1));if(s===void 0||u===void 0){let l=r[o]+r[o+1];throw new Error('hex string expected, got non-hex character "'+l+'" at index '+o)}n[i]=s*16+u}return n}var lg=async()=>{};ve.nextTick=lg;async function fg(r,e,t){let n=Date.now();for(let i=0;i<r;i++){t(i);let o=Date.now()-n;o>=0&&o<e||(await(0,ve.nextTick)(),n+=o)}}function Wu(r){if(typeof r!="string")throw new Error("utf8ToBytes expected string, got "+typeof r);return new Uint8Array(new TextEncoder().encode(r))}function Ui(r){return typeof r=="string"&&(r=Wu(r)),(0,qs.abytes)(r),r}function dg(...r){let e=0;for(let n=0;n<r.length;n++){let i=r[n];(0,qs.abytes)(i),e+=i.length}let t=new Uint8Array(e);for(let n=0,i=0;n<r.length;n++){let o=r[n];t.set(o,i),i+=o.length}return t}var Ks=class{clone(){return this._cloneInto()}};ve.Hash=Ks;function hg(r,e){if(e!==void 0&&{}.toString.call(e)!=="[object Object]")throw new Error("Options should be object or undefined");return Object.assign(r,e)}function _g(r){let e=n=>r().update(Ui(n)).digest(),t=r();return e.outputLen=t.outputLen,e.blockLen=t.blockLen,e.create=()=>r(),e}function pg(r){let e=(n,i)=>r(i).update(Ui(n)).digest(),t=r({});return e.outputLen=t.outputLen,e.blockLen=t.blockLen,e.create=n=>r(n),e}function Rg(r){let e=(n,i)=>r(i).update(Ui(n)).digest(),t=r({});return e.outputLen=t.outputLen,e.blockLen=t.blockLen,e.create=n=>r(n),e}function Eg(r=32){if(vn.crypto&&typeof vn.crypto.getRandomValues=="function")return vn.crypto.getRandomValues(new Uint8Array(r));if(vn.crypto&&typeof vn.crypto.randomBytes=="function")return vn.crypto.randomBytes(r);throw new Error("crypto.getRandomValues must be defined")}});var js=Re(an=>{"use strict";z();Object.defineProperty(an,"__esModule",{value:!0});an.HashMD=void 0;an.setBigUint64=$u;an.Chi=gg;an.Maj=yg;var $s=$n(),Yn=Nr();function $u(r,e,t,n){if(typeof r.setBigUint64=="function")return r.setBigUint64(e,t,n);let i=BigInt(32),o=BigInt(4294967295),s=Number(t>>i&o),u=Number(t&o),l=n?4:0,p=n?0:4;r.setUint32(e+l,s,n),r.setUint32(e+p,u,n)}function gg(r,e,t){return r&e^~r&t}function yg(r,e,t){return r&e^r&t^e&t}var Ys=class extends Yn.Hash{constructor(e,t,n,i){super(),this.blockLen=e,this.outputLen=t,this.padOffset=n,this.isLE=i,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=(0,Yn.createView)(this.buffer)}update(e){(0,$s.aexists)(this);let{view:t,buffer:n,blockLen:i}=this;e=(0,Yn.toBytes)(e);let o=e.length;for(let s=0;s<o;){let u=Math.min(i-this.pos,o-s);if(u===i){let l=(0,Yn.createView)(e);for(;i<=o-s;s+=i)this.process(l,s);continue}n.set(e.subarray(s,s+u),this.pos),this.pos+=u,s+=u,this.pos===i&&(this.process(t,0),this.pos=0)}return this.length+=e.length,this.roundClean(),this}digestInto(e){(0,$s.aexists)(this),(0,$s.aoutput)(e,this),this.finished=!0;let{buffer:t,view:n,blockLen:i,isLE:o}=this,{pos:s}=this;t[s++]=128,this.buffer.subarray(s).fill(0),this.padOffset>i-s&&(this.process(n,0),s=0);for(let g=s;g<i;g++)t[g]=0;$u(n,i-8,BigInt(this.length*8),o),this.process(n,0);let u=(0,Yn.createView)(e),l=this.outputLen;if(l%4)throw new Error("_sha2: outputLen should be aligned to 32bit");let p=l/4,R=this.get();if(p>R.length)throw new Error("_sha2: outputLen bigger than state");for(let g=0;g<p;g++)u.setUint32(4*g,R[g],o)}digest(){let{buffer:e,outputLen:t}=this;this.digestInto(e);let n=e.slice(0,t);return this.destroy(),n}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());let{blockLen:t,buffer:n,length:i,finished:o,destroyed:s,pos:u}=this;return e.length=i,e.pos=u,e.finished=o,e.destroyed=s,i%t&&e.buffer.set(n),e}};an.HashMD=Ys});var Js=Re(fe=>{"use strict";z();Object.defineProperty(fe,"__esModule",{value:!0});fe.add5L=fe.add5H=fe.add4H=fe.add4L=fe.add3H=fe.add3L=fe.rotlBL=fe.rotlBH=fe.rotlSL=fe.rotlSH=fe.rotr32L=fe.rotr32H=fe.rotrBL=fe.rotrBH=fe.rotrSL=fe.rotrSH=fe.shrSL=fe.shrSH=fe.toBig=void 0;fe.fromBig=Zs;fe.split=Yu;fe.add=cl;var Pi=BigInt(2**32-1),Xs=BigInt(32);function Zs(r,e=!1){return e?{h:Number(r&Pi),l:Number(r>>Xs&Pi)}:{h:Number(r>>Xs&Pi)|0,l:Number(r&Pi)|0}}function Yu(r,e=!1){let t=new Uint32Array(r.length),n=new Uint32Array(r.length);for(let i=0;i<r.length;i++){let{h:o,l:s}=Zs(r[i],e);[t[i],n[i]]=[o,s]}return[t,n]}var ju=(r,e)=>BigInt(r>>>0)<<Xs|BigInt(e>>>0);fe.toBig=ju;var Xu=(r,e,t)=>r>>>t;fe.shrSH=Xu;var Zu=(r,e,t)=>r<<32-t|e>>>t;fe.shrSL=Zu;var Ju=(r,e,t)=>r>>>t|e<<32-t;fe.rotrSH=Ju;var Qu=(r,e,t)=>r<<32-t|e>>>t;fe.rotrSL=Qu;var el=(r,e,t)=>r<<64-t|e>>>t-32;fe.rotrBH=el;var tl=(r,e,t)=>r>>>t-32|e<<64-t;fe.rotrBL=tl;var rl=(r,e)=>e;fe.rotr32H=rl;var nl=(r,e)=>r;fe.rotr32L=nl;var il=(r,e,t)=>r<<t|e>>>32-t;fe.rotlSH=il;var ol=(r,e,t)=>e<<t|r>>>32-t;fe.rotlSL=ol;var sl=(r,e,t)=>e<<t-32|r>>>64-t;fe.rotlBH=sl;var al=(r,e,t)=>r<<t-32|e>>>64-t;fe.rotlBL=al;function cl(r,e,t,n){let i=(e>>>0)+(n>>>0);return{h:r+t+(i/2**32|0)|0,l:i|0}}var ul=(r,e,t)=>(r>>>0)+(e>>>0)+(t>>>0);fe.add3L=ul;var ll=(r,e,t,n)=>e+t+n+(r/2**32|0)|0;fe.add3H=ll;var fl=(r,e,t,n)=>(r>>>0)+(e>>>0)+(t>>>0)+(n>>>0);fe.add4L=fl;var dl=(r,e,t,n,i)=>e+t+n+i+(r/2**32|0)|0;fe.add4H=dl;var hl=(r,e,t,n,i)=>(r>>>0)+(e>>>0)+(t>>>0)+(n>>>0)+(i>>>0);fe.add5L=hl;var _l=(r,e,t,n,i,o)=>e+t+n+i+o+(r/2**32|0)|0;fe.add5H=_l;var Ag={fromBig:Zs,split:Yu,toBig:ju,shrSH:Xu,shrSL:Zu,rotrSH:Ju,rotrSL:Qu,rotrBH:el,rotrBL:tl,rotr32H:rl,rotr32L:nl,rotlSH:il,rotlSL:ol,rotlBH:sl,rotlBL:al,add:cl,add3L:ul,add3H:ll,add4L:fl,add4H:dl,add5H:_l,add5L:hl};fe.default=Ag});var pl=Re(Ot=>{"use strict";z();Object.defineProperty(Ot,"__esModule",{value:!0});Ot.sha384=Ot.sha512_256=Ot.sha512_224=Ot.sha512=Ot.SHA384=Ot.SHA512_256=Ot.SHA512_224=Ot.SHA512=void 0;var mg=js(),ge=Js(),Gi=Nr(),[Sg,bg]=ge.default.split(["0x428a2f98d728ae22","0x7137449123ef65cd","0xb5c0fbcfec4d3b2f","0xe9b5dba58189dbbc","0x3956c25bf348b538","0x59f111f1b605d019","0x923f82a4af194f9b","0xab1c5ed5da6d8118","0xd807aa98a3030242","0x12835b0145706fbe","0x243185be4ee4b28c","0x550c7dc3d5ffb4e2","0x72be5d74f27b896f","0x80deb1fe3b1696b1","0x9bdc06a725c71235","0xc19bf174cf692694","0xe49b69c19ef14ad2","0xefbe4786384f25e3","0x0fc19dc68b8cd5b5","0x240ca1cc77ac9c65","0x2de92c6f592b0275","0x4a7484aa6ea6e483","0x5cb0a9dcbd41fbd4","0x76f988da831153b5","0x983e5152ee66dfab","0xa831c66d2db43210","0xb00327c898fb213f","0xbf597fc7beef0ee4","0xc6e00bf33da88fc2","0xd5a79147930aa725","0x06ca6351e003826f","0x142929670a0e6e70","0x27b70a8546d22ffc","0x2e1b21385c26c926","0x4d2c6dfc5ac42aed","0x53380d139d95b3df","0x650a73548baf63de","0x766a0abb3c77b2a8","0x81c2c92e47edaee6","0x92722c851482353b","0xa2bfe8a14cf10364","0xa81a664bbc423001","0xc24b8b70d0f89791","0xc76c51a30654be30","0xd192e819d6ef5218","0xd69906245565a910","0xf40e35855771202a","0x106aa07032bbd1b8","0x19a4c116b8d2d0c8","0x1e376c085141ab53","0x2748774cdf8eeb99","0x34b0bcb5e19b48a8","0x391c0cb3c5c95a63","0x4ed8aa4ae3418acb","0x5b9cca4f7763e373","0x682e6ff3d6b2b8a3","0x748f82ee5defb2fc","0x78a5636f43172f60","0x84c87814a1f0ab72","0x8cc702081a6439ec","0x90befffa23631e28","0xa4506cebde82bde9","0xbef9a3f7b2c67915","0xc67178f2e372532b","0xca273eceea26619c","0xd186b8c721c0c207","0xeada7dd6cde0eb1e","0xf57d4f7fee6ed178","0x06f067aa72176fba","0x0a637dc5a2c898a6","0x113f9804bef90dae","0x1b710b35131c471b","0x28db77f523047d84","0x32caab7b40c72493","0x3c9ebe0a15c9bebc","0x431d67c49c100d4c","0x4cc5d4becb3e42b6","0x597f299cfc657e2a","0x5fcb6fab3ad6faec","0x6c44198c4a475817"].map(r=>BigInt(r))),Ur=new Uint32Array(80),Pr=new Uint32Array(80),cn=class extends mg.HashMD{constructor(){super(128,64,16,!1),this.Ah=1779033703,this.Al=-205731576,this.Bh=-1150833019,this.Bl=-2067093701,this.Ch=1013904242,this.Cl=-23791573,this.Dh=-1521486534,this.Dl=1595750129,this.Eh=1359893119,this.El=-1377402159,this.Fh=-1694144372,this.Fl=725511199,this.Gh=528734635,this.Gl=-79577749,this.Hh=1541459225,this.Hl=327033209}get(){let{Ah:e,Al:t,Bh:n,Bl:i,Ch:o,Cl:s,Dh:u,Dl:l,Eh:p,El:R,Fh:g,Fl:v,Gh:m,Gl:x,Hh:I,Hl:O}=this;return[e,t,n,i,o,s,u,l,p,R,g,v,m,x,I,O]}set(e,t,n,i,o,s,u,l,p,R,g,v,m,x,I,O){this.Ah=e|0,this.Al=t|0,this.Bh=n|0,this.Bl=i|0,this.Ch=o|0,this.Cl=s|0,this.Dh=u|0,this.Dl=l|0,this.Eh=p|0,this.El=R|0,this.Fh=g|0,this.Fl=v|0,this.Gh=m|0,this.Gl=x|0,this.Hh=I|0,this.Hl=O|0}process(e,t){for(let B=0;B<16;B++,t+=4)Ur[B]=e.getUint32(t),Pr[B]=e.getUint32(t+=4);for(let B=16;B<80;B++){let F=Ur[B-15]|0,$=Pr[B-15]|0,H=ge.default.rotrSH(F,$,1)^ge.default.rotrSH(F,$,8)^ge.default.shrSH(F,$,7),K=ge.default.rotrSL(F,$,1)^ge.default.rotrSL(F,$,8)^ge.default.shrSL(F,$,7),j=Ur[B-2]|0,J=Pr[B-2]|0,me=ge.default.rotrSH(j,J,19)^ge.default.rotrBH(j,J,61)^ge.default.shrSH(j,J,6),ee=ge.default.rotrSL(j,J,19)^ge.default.rotrBL(j,J,61)^ge.default.shrSL(j,J,6),ae=ge.default.add4L(K,ee,Pr[B-7],Pr[B-16]),b=ge.default.add4H(ae,H,me,Ur[B-7],Ur[B-16]);Ur[B]=b|0,Pr[B]=ae|0}let{Ah:n,Al:i,Bh:o,Bl:s,Ch:u,Cl:l,Dh:p,Dl:R,Eh:g,El:v,Fh:m,Fl:x,Gh:I,Gl:O,Hh:D,Hl:k}=this;for(let B=0;B<80;B++){let F=ge.default.rotrSH(g,v,14)^ge.default.rotrSH(g,v,18)^ge.default.rotrBH(g,v,41),$=ge.default.rotrSL(g,v,14)^ge.default.rotrSL(g,v,18)^ge.default.rotrBL(g,v,41),H=g&m^~g&I,K=v&x^~v&O,j=ge.default.add5L(k,$,K,bg[B],Pr[B]),J=ge.default.add5H(j,D,F,H,Sg[B],Ur[B]),me=j|0,ee=ge.default.rotrSH(n,i,28)^ge.default.rotrBH(n,i,34)^ge.default.rotrBH(n,i,39),ae=ge.default.rotrSL(n,i,28)^ge.default.rotrBL(n,i,34)^ge.default.rotrBL(n,i,39),b=n&o^n&u^o&u,a=i&s^i&l^s&l;D=I|0,k=O|0,I=m|0,O=x|0,m=g|0,x=v|0,{h:g,l:v}=ge.default.add(p|0,R|0,J|0,me|0),p=u|0,R=l|0,u=o|0,l=s|0,o=n|0,s=i|0;let d=ge.default.add3L(me,ae,a);n=ge.default.add3H(d,J,ee,b),i=d|0}({h:n,l:i}=ge.default.add(this.Ah|0,this.Al|0,n|0,i|0)),{h:o,l:s}=ge.default.add(this.Bh|0,this.Bl|0,o|0,s|0),{h:u,l}=ge.default.add(this.Ch|0,this.Cl|0,u|0,l|0),{h:p,l:R}=ge.default.add(this.Dh|0,this.Dl|0,p|0,R|0),{h:g,l:v}=ge.default.add(this.Eh|0,this.El|0,g|0,v|0),{h:m,l:x}=ge.default.add(this.Fh|0,this.Fl|0,m|0,x|0),{h:I,l:O}=ge.default.add(this.Gh|0,this.Gl|0,I|0,O|0),{h:D,l:k}=ge.default.add(this.Hh|0,this.Hl|0,D|0,k|0),this.set(n,i,o,s,u,l,p,R,g,v,m,x,I,O,D,k)}roundClean(){Ur.fill(0),Pr.fill(0)}destroy(){this.buffer.fill(0),this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)}};Ot.SHA512=cn;var Fi=class extends cn{constructor(){super(),this.Ah=-1942145080,this.Al=424955298,this.Bh=1944164710,this.Bl=-1982016298,this.Ch=502970286,this.Cl=855612546,this.Dh=1738396948,this.Dl=1479516111,this.Eh=258812777,this.El=2077511080,this.Fh=2011393907,this.Fl=79989058,this.Gh=1067287976,this.Gl=1780299464,this.Hh=286451373,this.Hl=-1848208735,this.outputLen=28}};Ot.SHA512_224=Fi;var zi=class extends cn{constructor(){super(),this.Ah=573645204,this.Al=-64227540,this.Bh=-1621794909,this.Bl=-934517566,this.Ch=596883563,this.Cl=1867755857,this.Dh=-1774684391,this.Dl=1497426621,this.Eh=-1775747358,this.El=-1467023389,this.Fh=-1101128155,this.Fl=1401305490,this.Gh=721525244,this.Gl=746961066,this.Hh=246885852,this.Hl=-2117784414,this.outputLen=32}};Ot.SHA512_256=zi;var Vi=class extends cn{constructor(){super(),this.Ah=-876896931,this.Al=-1056596264,this.Bh=1654270250,this.Bl=914150663,this.Ch=-1856437926,this.Cl=812702999,this.Dh=355462360,this.Dl=-150054599,this.Eh=1731405415,this.El=-4191439,this.Fh=-1900787065,this.Fl=1750603025,this.Gh=-619958771,this.Gl=1694076839,this.Hh=1203062813,this.Hl=-1090891868,this.outputLen=48}};Ot.SHA384=Vi;Ot.sha512=(0,Gi.wrapConstructor)(()=>new cn);Ot.sha512_224=(0,Gi.wrapConstructor)(()=>new Fi);Ot.sha512_256=(0,Gi.wrapConstructor)(()=>new zi);Ot.sha384=(0,Gi.wrapConstructor)(()=>new Vi)});var ar=Re(ke=>{"use strict";z();Object.defineProperty(ke,"__esModule",{value:!0});ke.notImplemented=ke.bitMask=void 0;ke.isBytes=qi;ke.abytes=Wi;ke.abool=wg;ke.bytesToHex=ta;ke.numberToHexUnpadded=gl;ke.hexToNumber=ra;ke.hexToBytes=$i;ke.bytesToNumberBE=Ng;ke.bytesToNumberLE=Tg;ke.numberToBytesBE=yl;ke.numberToBytesLE=vg;ke.numberToVarBytesBE=Cg;ke.ensureBytes=xg;ke.concatBytes=Al;ke.equalBytes=Lg;ke.utf8ToBytes=kg;ke.inRange=ml;ke.aInRange=Bg;ke.bitLen=Dg;ke.bitGet=Mg;ke.bitSet=Ug;ke.createHmacDrbg=Fg;ke.validateObject=Vg;ke.memoized=Hg;var Hi=BigInt(0),Ki=BigInt(1),Og=BigInt(2);function qi(r){return r instanceof Uint8Array||ArrayBuffer.isView(r)&&r.constructor.name==="Uint8Array"}function Wi(r){if(!qi(r))throw new Error("Uint8Array expected")}function wg(r,e){if(typeof e!="boolean")throw new Error(r+" boolean expected, got "+e)}var Ig=Array.from({length:256},(r,e)=>e.toString(16).padStart(2,"0"));function ta(r){Wi(r);let e="";for(let t=0;t<r.length;t++)e+=Ig[r[t]];return e}function gl(r){let e=r.toString(16);return e.length&1?"0"+e:e}function ra(r){if(typeof r!="string")throw new Error("hex string expected, got "+typeof r);return r===""?Hi:BigInt("0x"+r)}var Tr={_0:48,_9:57,A:65,F:70,a:97,f:102};function Rl(r){if(r>=Tr._0&&r<=Tr._9)return r-Tr._0;if(r>=Tr.A&&r<=Tr.F)return r-(Tr.A-10);if(r>=Tr.a&&r<=Tr.f)return r-(Tr.a-10)}function $i(r){if(typeof r!="string")throw new Error("hex string expected, got "+typeof r);let e=r.length,t=e/2;if(e%2)throw new Error("hex string expected, got unpadded hex of length "+e);let n=new Uint8Array(t);for(let i=0,o=0;i<t;i++,o+=2){let s=Rl(r.charCodeAt(o)),u=Rl(r.charCodeAt(o+1));if(s===void 0||u===void 0){let l=r[o]+r[o+1];throw new Error('hex string expected, got non-hex character "'+l+'" at index '+o)}n[i]=s*16+u}return n}function Ng(r){return ra(ta(r))}function Tg(r){return Wi(r),ra(ta(Uint8Array.from(r).reverse()))}function yl(r,e){return $i(r.toString(16).padStart(e*2,"0"))}function vg(r,e){return yl(r,e).reverse()}function Cg(r){return $i(gl(r))}function xg(r,e,t){let n;if(typeof e=="string")try{n=$i(e)}catch(o){throw new Error(r+" must be hex string or Uint8Array, cause: "+o)}else if(qi(e))n=Uint8Array.from(e);else throw new Error(r+" must be hex string or Uint8Array");let i=n.length;if(typeof t=="number"&&i!==t)throw new Error(r+" of length "+t+" expected, got "+i);return n}function Al(...r){let e=0;for(let n=0;n<r.length;n++){let i=r[n];Wi(i),e+=i.length}let t=new Uint8Array(e);for(let n=0,i=0;n<r.length;n++){let o=r[n];t.set(o,i),i+=o.length}return t}function Lg(r,e){if(r.length!==e.length)return!1;let t=0;for(let n=0;n<r.length;n++)t|=r[n]^e[n];return t===0}function kg(r){if(typeof r!="string")throw new Error("string expected");return new Uint8Array(new TextEncoder().encode(r))}var Qs=r=>typeof r=="bigint"&&Hi<=r;function ml(r,e,t){return Qs(r)&&Qs(e)&&Qs(t)&&e<=r&&r<t}function Bg(r,e,t,n){if(!ml(e,t,n))throw new Error("expected valid "+r+": "+t+" <= n < "+n+", got "+e)}function Dg(r){let e;for(e=0;r>Hi;r>>=Ki,e+=1);return e}function Mg(r,e){return r>>BigInt(e)&Ki}function Ug(r,e,t){return r|(t?Ki:Hi)<<BigInt(e)}var Pg=r=>(Og<<BigInt(r-1))-Ki;ke.bitMask=Pg;var ea=r=>new Uint8Array(r),El=r=>Uint8Array.from(r);function Fg(r,e,t){if(typeof r!="number"||r<2)throw new Error("hashLen must be a number");if(typeof e!="number"||e<2)throw new Error("qByteLen must be a number");if(typeof t!="function")throw new Error("hmacFn must be a function");let n=ea(r),i=ea(r),o=0,s=()=>{n.fill(1),i.fill(0),o=0},u=(...g)=>t(i,n,...g),l=(g=ea())=>{i=u(El([0]),g),n=u(),g.length!==0&&(i=u(El([1]),g),n=u())},p=()=>{if(o++>=1e3)throw new Error("drbg: tried 1000 values");let g=0,v=[];for(;g<e;){n=u();let m=n.slice();v.push(m),g+=n.length}return Al(...v)};return(g,v)=>{s(),l(g);let m;for(;!(m=v(p()));)l();return s(),m}}var zg={bigint:r=>typeof r=="bigint",function:r=>typeof r=="function",boolean:r=>typeof r=="boolean",string:r=>typeof r=="string",stringOrUint8Array:r=>typeof r=="string"||qi(r),isSafeInteger:r=>Number.isSafeInteger(r),array:r=>Array.isArray(r),field:(r,e)=>e.Fp.isValid(r),hash:r=>typeof r=="function"&&Number.isSafeInteger(r.outputLen)};function Vg(r,e,t={}){let n=(i,o,s)=>{let u=zg[o];if(typeof u!="function")throw new Error("invalid validator function");let l=r[i];if(!(s&&l===void 0)&&!u(l,r))throw new Error("param "+String(i)+" is invalid. Expected "+o+", got "+l)};for(let[i,o]of Object.entries(e))n(i,o,!1);for(let[i,o]of Object.entries(t))n(i,o,!0);return r}var Gg=()=>{throw new Error("not implemented")};ke.notImplemented=Gg;function Hg(r){let e=new WeakMap;return(t,...n)=>{let i=e.get(t);if(i!==void 0)return i;let o=r(t,...n);return e.set(t,o),o}}});var zr=Re(Qe=>{"use strict";z();Object.defineProperty(Qe,"__esModule",{value:!0});Qe.isNegativeLE=void 0;Qe.mod=Qt;Qe.pow=Ol;Qe.pow2=$g;Qe.invert=Yi;Qe.tonelliShanks=wl;Qe.FpSqrt=Il;Qe.validateField=Xg;Qe.FpPow=Nl;Qe.FpInvertBatch=Tl;Qe.FpDiv=Zg;Qe.FpLegendre=vl;Qe.FpIsSquare=Jg;Qe.nLength=ia;Qe.Field=Qg;Qe.FpSqrtOdd=ey;Qe.FpSqrtEven=ty;Qe.hashToPrivateScalar=ry;Qe.getFieldBytesLength=oa;Qe.getMinHashLength=Cl;Qe.mapHashToField=ny;var er=ar(),At=BigInt(0),ze=BigInt(1),Fr=BigInt(2),Kg=BigInt(3),na=BigInt(4),Sl=BigInt(5),bl=BigInt(8),qg=BigInt(9),Wg=BigInt(16);function Qt(r,e){let t=r%e;return t>=At?t:e+t}function Ol(r,e,t){if(e<At)throw new Error("invalid exponent, negatives unsupported");if(t<=At)throw new Error("invalid modulus");if(t===ze)return At;let n=ze;for(;e>At;)e&ze&&(n=n*r%t),r=r*r%t,e>>=ze;return n}function $g(r,e,t){let n=r;for(;e-- >At;)n*=n,n%=t;return n}function Yi(r,e){if(r===At)throw new Error("invert: expected non-zero number");if(e<=At)throw new Error("invert: expected positive modulus, got "+e);let t=Qt(r,e),n=e,i=At,o=ze,s=ze,u=At;for(;t!==At;){let p=n/t,R=n%t,g=i-s*p,v=o-u*p;n=t,t=R,i=s,o=u,s=g,u=v}if(n!==ze)throw new Error("invert: does not exist");return Qt(i,e)}function wl(r){let e=(r-ze)/Fr,t,n,i;for(t=r-ze,n=0;t%Fr===At;t/=Fr,n++);for(i=Fr;i<r&&Ol(i,e,r)!==r-ze;i++)if(i>1e3)throw new Error("Cannot find square root: likely non-prime P");if(n===1){let s=(r+ze)/na;return function(l,p){let R=l.pow(p,s);if(!l.eql(l.sqr(R),p))throw new Error("Cannot find square root");return R}}let o=(t+ze)/Fr;return function(u,l){if(u.pow(l,e)===u.neg(u.ONE))throw new Error("Cannot find square root");let p=n,R=u.pow(u.mul(u.ONE,i),t),g=u.pow(l,o),v=u.pow(l,t);for(;!u.eql(v,u.ONE);){if(u.eql(v,u.ZERO))return u.ZERO;let m=1;for(let I=u.sqr(v);m<p&&!u.eql(I,u.ONE);m++)I=u.sqr(I);let x=u.pow(R,ze<<BigInt(p-m-1));R=u.sqr(x),g=u.mul(g,x),v=u.mul(v,R),p=m}return g}}function Il(r){if(r%na===Kg){let e=(r+ze)/na;return function(n,i){let o=n.pow(i,e);if(!n.eql(n.sqr(o),i))throw new Error("Cannot find square root");return o}}if(r%bl===Sl){let e=(r-Sl)/bl;return function(n,i){let o=n.mul(i,Fr),s=n.pow(o,e),u=n.mul(i,s),l=n.mul(n.mul(u,Fr),s),p=n.mul(u,n.sub(l,n.ONE));if(!n.eql(n.sqr(p),i))throw new Error("Cannot find square root");return p}}return r%Wg,wl(r)}var Yg=(r,e)=>(Qt(r,e)&ze)===ze;Qe.isNegativeLE=Yg;var jg=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function Xg(r){let e={ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"},t=jg.reduce((n,i)=>(n[i]="function",n),e);return(0,er.validateObject)(r,t)}function Nl(r,e,t){if(t<At)throw new Error("invalid exponent, negatives unsupported");if(t===At)return r.ONE;if(t===ze)return e;let n=r.ONE,i=e;for(;t>At;)t&ze&&(n=r.mul(n,i)),i=r.sqr(i),t>>=ze;return n}function Tl(r,e){let t=new Array(e.length),n=e.reduce((o,s,u)=>r.is0(s)?o:(t[u]=o,r.mul(o,s)),r.ONE),i=r.inv(n);return e.reduceRight((o,s,u)=>r.is0(s)?o:(t[u]=r.mul(o,t[u]),r.mul(o,s)),i),t}function Zg(r,e,t){return r.mul(e,typeof t=="bigint"?Yi(t,r.ORDER):r.inv(t))}function vl(r){let e=(r-ze)/Fr;return(t,n)=>t.pow(n,e)}function Jg(r){let e=vl(r.ORDER);return t=>{let n=e(r,t);return r.eql(n,r.ZERO)||r.eql(n,r.ONE)}}function ia(r,e){let t=e!==void 0?e:r.toString(2).length,n=Math.ceil(t/8);return{nBitLength:t,nByteLength:n}}function Qg(r,e,t=!1,n={}){if(r<=At)throw new Error("invalid field: expected ORDER > 0, got "+r);let{nBitLength:i,nByteLength:o}=ia(r,e);if(o>2048)throw new Error("invalid field: expected ORDER of <= 2048 bytes");let s,u=Object.freeze({ORDER:r,isLE:t,BITS:i,BYTES:o,MASK:(0,er.bitMask)(i),ZERO:At,ONE:ze,create:l=>Qt(l,r),isValid:l=>{if(typeof l!="bigint")throw new Error("invalid field element: expected bigint, got "+typeof l);return At<=l&&l<r},is0:l=>l===At,isOdd:l=>(l&ze)===ze,neg:l=>Qt(-l,r),eql:(l,p)=>l===p,sqr:l=>Qt(l*l,r),add:(l,p)=>Qt(l+p,r),sub:(l,p)=>Qt(l-p,r),mul:(l,p)=>Qt(l*p,r),pow:(l,p)=>Nl(u,l,p),div:(l,p)=>Qt(l*Yi(p,r),r),sqrN:l=>l*l,addN:(l,p)=>l+p,subN:(l,p)=>l-p,mulN:(l,p)=>l*p,inv:l=>Yi(l,r),sqrt:n.sqrt||(l=>(s||(s=Il(r)),s(u,l))),invertBatch:l=>Tl(u,l),cmov:(l,p,R)=>R?p:l,toBytes:l=>t?(0,er.numberToBytesLE)(l,o):(0,er.numberToBytesBE)(l,o),fromBytes:l=>{if(l.length!==o)throw new Error("Field.fromBytes: expected "+o+" bytes, got "+l.length);return t?(0,er.bytesToNumberLE)(l):(0,er.bytesToNumberBE)(l)}});return Object.freeze(u)}function ey(r,e){if(!r.isOdd)throw new Error("Field doesn't have isOdd");let t=r.sqrt(e);return r.isOdd(t)?t:r.neg(t)}function ty(r,e){if(!r.isOdd)throw new Error("Field doesn't have isOdd");let t=r.sqrt(e);return r.isOdd(t)?r.neg(t):t}function ry(r,e,t=!1){r=(0,er.ensureBytes)("privateHash",r);let n=r.length,i=ia(e).nByteLength+8;if(i<24||n<i||n>1024)throw new Error("hashToPrivateScalar: expected "+i+"-1024 bytes of input, got "+n);let o=t?(0,er.bytesToNumberLE)(r):(0,er.bytesToNumberBE)(r);return Qt(o,e-ze)+ze}function oa(r){if(typeof r!="bigint")throw new Error("field order must be bigint");let e=r.toString(2).length;return Math.ceil(e/8)}function Cl(r){let e=oa(r);return e+Math.ceil(e/2)}function ny(r,e,t=!1){let n=r.length,i=oa(e),o=Cl(e);if(n<16||n<o||n>1024)throw new Error("expected "+o+"-1024 bytes of input, got "+n);let s=t?(0,er.bytesToNumberLE)(r):(0,er.bytesToNumberBE)(r),u=Qt(s,e-ze)+ze;return t?(0,er.numberToBytesLE)(u,i):(0,er.numberToBytesBE)(u,i)}});var Xi=Re(Cn=>{"use strict";z();Object.defineProperty(Cn,"__esModule",{value:!0});Cn.wNAF=iy;Cn.pippenger=oy;Cn.precomputeMSMUnsafe=sy;Cn.validateBasic=ay;var xl=zr(),kl=ar(),Ll=BigInt(0),ji=BigInt(1);function sa(r,e){let t=e.negate();return r?t:e}function la(r,e){if(!Number.isSafeInteger(r)||r<=0||r>e)throw new Error("invalid window size, expected [1.."+e+"], got W="+r)}function aa(r,e){la(r,e);let t=Math.ceil(e/r)+1,n=2**(r-1);return{windows:t,windowSize:n}}function Bl(r,e){if(!Array.isArray(r))throw new Error("array expected");r.forEach((t,n)=>{if(!(t instanceof e))throw new Error("invalid point at index "+n)})}function Dl(r,e){if(!Array.isArray(r))throw new Error("array of scalars expected");r.forEach((t,n)=>{if(!e.isValid(t))throw new Error("invalid scalar at index "+n)})}var ca=new WeakMap,Ml=new WeakMap;function ua(r){return Ml.get(r)||1}function iy(r,e){return{constTimeNegate:sa,hasPrecomputes(t){return ua(t)!==1},unsafeLadder(t,n,i=r.ZERO){let o=t;for(;n>Ll;)n&ji&&(i=i.add(o)),o=o.double(),n>>=ji;return i},precomputeWindow(t,n){let{windows:i,windowSize:o}=aa(n,e),s=[],u=t,l=u;for(let p=0;p<i;p++){l=u,s.push(l);for(let R=1;R<o;R++)l=l.add(u),s.push(l);u=l.double()}return s},wNAF(t,n,i){let{windows:o,windowSize:s}=aa(t,e),u=r.ZERO,l=r.BASE,p=BigInt(2**t-1),R=2**t,g=BigInt(t);for(let v=0;v<o;v++){let m=v*s,x=Number(i&p);i>>=g,x>s&&(x-=R,i+=ji);let I=m,O=m+Math.abs(x)-1,D=v%2!==0,k=x<0;x===0?l=l.add(sa(D,n[I])):u=u.add(sa(k,n[O]))}return{p:u,f:l}},wNAFUnsafe(t,n,i,o=r.ZERO){let{windows:s,windowSize:u}=aa(t,e),l=BigInt(2**t-1),p=2**t,R=BigInt(t);for(let g=0;g<s;g++){let v=g*u;if(i===Ll)break;let m=Number(i&l);if(i>>=R,m>u&&(m-=p,i+=ji),m===0)continue;let x=n[v+Math.abs(m)-1];m<0&&(x=x.negate()),o=o.add(x)}return o},getPrecomputes(t,n,i){let o=ca.get(n);return o||(o=this.precomputeWindow(n,t),t!==1&&ca.set(n,i(o))),o},wNAFCached(t,n,i){let o=ua(t);return this.wNAF(o,this.getPrecomputes(o,t,i),n)},wNAFCachedUnsafe(t,n,i,o){let s=ua(t);return s===1?this.unsafeLadder(t,n,o):this.wNAFUnsafe(s,this.getPrecomputes(s,t,i),n,o)},setWindowSize(t,n){la(n,e),Ml.set(t,n),ca.delete(t)}}}function oy(r,e,t,n){if(Bl(t,r),Dl(n,e),t.length!==n.length)throw new Error("arrays of points and scalars must have equal length");let i=r.ZERO,o=(0,kl.bitLen)(BigInt(t.length)),s=o>12?o-3:o>4?o-2:o?2:1,u=(1<<s)-1,l=new Array(u+1).fill(i),p=Math.floor((e.BITS-1)/s)*s,R=i;for(let g=p;g>=0;g-=s){l.fill(i);for(let m=0;m<n.length;m++){let x=n[m],I=Number(x>>BigInt(g)&BigInt(u));l[I]=l[I].add(t[m])}let v=i;for(let m=l.length-1,x=i;m>0;m--)x=x.add(l[m]),v=v.add(x);if(R=R.add(v),g!==0)for(let m=0;m<s;m++)R=R.double()}return R}function sy(r,e,t,n){la(n,e.BITS),Bl(t,r);let i=r.ZERO,o=2**n-1,s=Math.ceil(e.BITS/n),u=BigInt((1<<n)-1),l=t.map(p=>{let R=[];for(let g=0,v=p;g<o;g++)R.push(v),v=v.add(p);return R});return p=>{if(Dl(p,e),p.length>t.length)throw new Error("array of scalars must be smaller than array of points");let R=i;for(let g=0;g<s;g++){if(R!==i)for(let m=0;m<n;m++)R=R.double();let v=BigInt(s*n-(g+1)*n);for(let m=0;m<p.length;m++){let x=p[m],I=Number(x>>v&u);I&&(R=R.add(l[m][I-1]))}}return R}}function ay(r){return(0,xl.validateField)(r.Fp),(0,kl.validateObject)(r,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...(0,xl.nLength)(r.n,r.nBitLength),...r,p:r.Fp.ORDER})}});var Pl=Re(da=>{"use strict";z();Object.defineProperty(da,"__esModule",{value:!0});da.twistedEdwards=fy;var fa=Xi(),Ul=zr(),Wt=ar(),Kt=ar(),cr=BigInt(0),qt=BigInt(1),Zi=BigInt(2),cy=BigInt(8),uy={zip215:!0};function ly(r){let e=(0,fa.validateBasic)(r);return Wt.validateObject(r,{hash:"function",a:"bigint",d:"bigint",randomBytes:"function"},{adjustScalarBytes:"function",domain:"function",uvRatio:"function",mapToCurve:"function"}),Object.freeze({...e})}function fy(r){let e=ly(r),{Fp:t,n,prehash:i,hash:o,randomBytes:s,nByteLength:u,h:l}=e,p=Zi<<BigInt(u*8)-qt,R=t.create,g=(0,Ul.Field)(e.n,e.nBitLength),v=e.uvRatio||((h,_)=>{try{return{isValid:!0,value:t.sqrt(h*t.inv(_))}}catch{return{isValid:!1,value:cr}}}),m=e.adjustScalarBytes||(h=>h),x=e.domain||((h,_,E)=>{if((0,Kt.abool)("phflag",E),_.length||E)throw new Error("Contexts/pre-hash are not supported");return h});function I(h,_){Wt.aInRange("coordinate "+h,_,cr,p)}function O(h){if(!(h instanceof B))throw new Error("ExtendedPoint expected")}let D=(0,Kt.memoized)((h,_)=>{let{ex:E,ey:S,ez:C}=h,y=h.is0();_==null&&(_=y?cy:t.inv(C));let f=R(E*_),A=R(S*_),G=R(C*_);if(y)return{x:cr,y:qt};if(G!==qt)throw new Error("invZ was invalid");return{x:f,y:A}}),k=(0,Kt.memoized)(h=>{let{a:_,d:E}=e;if(h.is0())throw new Error("bad point: ZERO");let{ex:S,ey:C,ez:y,et:f}=h,A=R(S*S),G=R(C*C),q=R(y*y),Y=R(q*q),te=R(A*_),oe=R(q*R(te+G)),ce=R(Y+R(E*R(A*G)));if(oe!==ce)throw new Error("bad point: equation left != right (1)");let Ue=R(S*C),he=R(y*f);if(Ue!==he)throw new Error("bad point: equation left != right (2)");return!0});class B{constructor(_,E,S,C){this.ex=_,this.ey=E,this.ez=S,this.et=C,I("x",_),I("y",E),I("z",S),I("t",C),Object.freeze(this)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static fromAffine(_){if(_ instanceof B)throw new Error("extended point not allowed");let{x:E,y:S}=_||{};return I("x",E),I("y",S),new B(E,S,qt,R(E*S))}static normalizeZ(_){let E=t.invertBatch(_.map(S=>S.ez));return _.map((S,C)=>S.toAffine(E[C])).map(B.fromAffine)}static msm(_,E){return(0,fa.pippenger)(B,g,_,E)}_setWindowSize(_){H.setWindowSize(this,_)}assertValidity(){k(this)}equals(_){O(_);let{ex:E,ey:S,ez:C}=this,{ex:y,ey:f,ez:A}=_,G=R(E*A),q=R(y*C),Y=R(S*A),te=R(f*C);return G===q&&Y===te}is0(){return this.equals(B.ZERO)}negate(){return new B(R(-this.ex),this.ey,this.ez,R(-this.et))}double(){let{a:_}=e,{ex:E,ey:S,ez:C}=this,y=R(E*E),f=R(S*S),A=R(Zi*R(C*C)),G=R(_*y),q=E+S,Y=R(R(q*q)-y-f),te=G+f,oe=te-A,ce=G-f,Ue=R(Y*oe),he=R(te*ce),ue=R(Y*ce),zt=R(oe*te);return new B(Ue,he,zt,ue)}add(_){O(_);let{a:E,d:S}=e,{ex:C,ey:y,ez:f,et:A}=this,{ex:G,ey:q,ez:Y,et:te}=_;if(E===BigInt(-1)){let Ke=R((y-C)*(q+G)),qe=R((y+C)*(q-G)),Dr=R(qe-Ke);if(Dr===cr)return this.double();let We=R(f*Zi*te),$e=R(A*Zi*Y),Jt=$e+We,N=qe+Ke,L=$e-We,M=R(Jt*Dr),U=R(N*L),W=R(Jt*L),ie=R(Dr*N);return new B(M,U,ie,W)}let oe=R(C*G),ce=R(y*q),Ue=R(A*S*te),he=R(f*Y),ue=R((C+y)*(G+q)-oe-ce),zt=he-Ue,we=he+Ue,Le=R(ce-E*oe),bn=R(ue*zt),Xe=R(we*Le),Ze=R(ue*Le),rn=R(zt*we);return new B(bn,Xe,rn,Ze)}subtract(_){return this.add(_.negate())}wNAF(_){return H.wNAFCached(this,_,B.normalizeZ)}multiply(_){let E=_;Wt.aInRange("scalar",E,qt,n);let{p:S,f:C}=this.wNAF(E);return B.normalizeZ([S,C])[0]}multiplyUnsafe(_,E=B.ZERO){let S=_;return Wt.aInRange("scalar",S,cr,n),S===cr?$:this.is0()||S===qt?this:H.wNAFCachedUnsafe(this,S,B.normalizeZ,E)}isSmallOrder(){return this.multiplyUnsafe(l).is0()}isTorsionFree(){return H.unsafeLadder(this,n).is0()}toAffine(_){return D(this,_)}clearCofactor(){let{h:_}=e;return _===qt?this:this.multiplyUnsafe(_)}static fromHex(_,E=!1){let{d:S,a:C}=e,y=t.BYTES;_=(0,Kt.ensureBytes)("pointHex",_,y),(0,Kt.abool)("zip215",E);let f=_.slice(),A=_[y-1];f[y-1]=A&-129;let G=Wt.bytesToNumberLE(f),q=E?p:t.ORDER;Wt.aInRange("pointHex.y",G,cr,q);let Y=R(G*G),te=R(Y-qt),oe=R(S*Y-C),{isValid:ce,value:Ue}=v(te,oe);if(!ce)throw new Error("Point.fromHex: invalid y coordinate");let he=(Ue&qt)===qt,ue=(A&128)!==0;if(!E&&Ue===cr&&ue)throw new Error("Point.fromHex: x=0 and x_0=1");return ue!==he&&(Ue=R(-Ue)),B.fromAffine({x:Ue,y:G})}static fromPrivateKey(_){return J(_).point}toRawBytes(){let{x:_,y:E}=this.toAffine(),S=Wt.numberToBytesLE(E,t.BYTES);return S[S.length-1]|=_&qt?128:0,S}toHex(){return Wt.bytesToHex(this.toRawBytes())}}B.BASE=new B(e.Gx,e.Gy,qt,R(e.Gx*e.Gy)),B.ZERO=new B(cr,qt,qt,cr);let{BASE:F,ZERO:$}=B,H=(0,fa.wNAF)(B,u*8);function K(h){return(0,Ul.mod)(h,n)}function j(h){return K(Wt.bytesToNumberLE(h))}function J(h){let _=t.BYTES;h=(0,Kt.ensureBytes)("private key",h,_);let E=(0,Kt.ensureBytes)("hashed private key",o(h),2*_),S=m(E.slice(0,_)),C=E.slice(_,2*_),y=j(S),f=F.multiply(y),A=f.toRawBytes();return{head:S,prefix:C,scalar:y,point:f,pointBytes:A}}function me(h){return J(h).pointBytes}function ee(h=new Uint8Array,..._){let E=Wt.concatBytes(..._);return j(o(x(E,(0,Kt.ensureBytes)("context",h),!!i)))}function ae(h,_,E={}){h=(0,Kt.ensureBytes)("message",h),i&&(h=i(h));let{prefix:S,scalar:C,pointBytes:y}=J(_),f=ee(E.context,S,h),A=F.multiply(f).toRawBytes(),G=ee(E.context,A,y,h),q=K(f+G*C);Wt.aInRange("signature.s",q,cr,n);let Y=Wt.concatBytes(A,Wt.numberToBytesLE(q,t.BYTES));return(0,Kt.ensureBytes)("result",Y,t.BYTES*2)}let b=uy;function a(h,_,E,S=b){let{context:C,zip215:y}=S,f=t.BYTES;h=(0,Kt.ensureBytes)("signature",h,2*f),_=(0,Kt.ensureBytes)("message",_),E=(0,Kt.ensureBytes)("publicKey",E,f),y!==void 0&&(0,Kt.abool)("zip215",y),i&&(_=i(_));let A=Wt.bytesToNumberLE(h.slice(f,2*f)),G,q,Y;try{G=B.fromHex(E,y),q=B.fromHex(h.slice(0,f),y),Y=F.multiplyUnsafe(A)}catch{return!1}if(!y&&G.isSmallOrder())return!1;let te=ee(C,q.toRawBytes(),G.toRawBytes(),_);return q.add(G.multiplyUnsafe(te)).subtract(Y).clearCofactor().equals(B.ZERO)}return F._setWindowSize(8),{CURVE:e,getPublicKey:me,sign:ae,verify:a,ExtendedPoint:B,utils:{getExtendedPublicKey:J,randomPrivateKey:()=>s(t.BYTES),precompute(h=8,_=B.BASE){return _._setWindowSize(h),_.multiply(BigInt(3)),_}}}}});var _a=Re(un=>{"use strict";z();Object.defineProperty(un,"__esModule",{value:!0});un.expand_message_xmd=Fl;un.expand_message_xof=zl;un.hash_to_field=ha;un.isogenyMap=py;un.createHasher=Ry;var dy=zr(),kt=ar(),hy=kt.bytesToNumberBE;function Vr(r,e){if(jn(r),jn(e),r<0||r>=1<<8*e)throw new Error("invalid I2OSP input: "+r);let t=Array.from({length:e}).fill(0);for(let n=e-1;n>=0;n--)t[n]=r&255,r>>>=8;return new Uint8Array(t)}function _y(r,e){let t=new Uint8Array(r.length);for(let n=0;n<r.length;n++)t[n]=r[n]^e[n];return t}function jn(r){if(!Number.isSafeInteger(r))throw new Error("number expected")}function Fl(r,e,t,n){(0,kt.abytes)(r),(0,kt.abytes)(e),jn(t),e.length>255&&(e=n((0,kt.concatBytes)((0,kt.utf8ToBytes)("H2C-OVERSIZE-DST-"),e)));let{outputLen:i,blockLen:o}=n,s=Math.ceil(t/i);if(t>65535||s>255)throw new Error("expand_message_xmd: invalid lenInBytes");let u=(0,kt.concatBytes)(e,Vr(e.length,1)),l=Vr(0,o),p=Vr(t,2),R=new Array(s),g=n((0,kt.concatBytes)(l,r,p,Vr(0,1),u));R[0]=n((0,kt.concatBytes)(g,Vr(1,1),u));for(let m=1;m<=s;m++){let x=[_y(g,R[m-1]),Vr(m+1,1),u];R[m]=n((0,kt.concatBytes)(...x))}return(0,kt.concatBytes)(...R).slice(0,t)}function zl(r,e,t,n,i){if((0,kt.abytes)(r),(0,kt.abytes)(e),jn(t),e.length>255){let o=Math.ceil(2*n/8);e=i.create({dkLen:o}).update((0,kt.utf8ToBytes)("H2C-OVERSIZE-DST-")).update(e).digest()}if(t>65535||e.length>255)throw new Error("expand_message_xof: invalid lenInBytes");return i.create({dkLen:t}).update(r).update(Vr(t,2)).update(e).update(Vr(e.length,1)).digest()}function ha(r,e,t){(0,kt.validateObject)(t,{DST:"stringOrUint8Array",p:"bigint",m:"isSafeInteger",k:"isSafeInteger",hash:"hash"});let{p:n,k:i,m:o,hash:s,expand:u,DST:l}=t;(0,kt.abytes)(r),jn(e);let p=typeof l=="string"?(0,kt.utf8ToBytes)(l):l,R=n.toString(2).length,g=Math.ceil((R+i)/8),v=e*o*g,m;if(u==="xmd")m=Fl(r,p,v,s);else if(u==="xof")m=zl(r,p,v,i,s);else if(u==="_internal_pass")m=r;else throw new Error('expand must be "xmd" or "xof"');let x=new Array(e);for(let I=0;I<e;I++){let O=new Array(o);for(let D=0;D<o;D++){let k=g*(D+I*o),B=m.subarray(k,k+g);O[D]=(0,dy.mod)(hy(B),n)}x[I]=O}return x}function py(r,e){let t=e.map(n=>Array.from(n).reverse());return(n,i)=>{let[o,s,u,l]=t.map(p=>p.reduce((R,g)=>r.add(r.mul(R,n),g)));return n=r.div(o,s),i=r.mul(i,r.div(u,l)),{x:n,y:i}}}function Ry(r,e,t){if(typeof e!="function")throw new Error("mapToCurve() must be defined");return{hashToCurve(n,i){let o=ha(n,2,{...t,DST:t.DST,...i}),s=r.fromAffine(e(o[0])),u=r.fromAffine(e(o[1])),l=s.add(u).clearCofactor();return l.assertValidity(),l},encodeToCurve(n,i){let o=ha(n,1,{...t,DST:t.encodeDST,...i}),s=r.fromAffine(e(o[0])).clearCofactor();return s.assertValidity(),s},mapToCurve(n){if(!Array.isArray(n))throw new Error("mapToCurve: expected array of bigints");for(let o of n)if(typeof o!="bigint")throw new Error("mapToCurve: expected array of bigints");let i=r.fromAffine(e(n)).clearCofactor();return i.assertValidity(),i}}}});var Gl=Re(Ra=>{"use strict";z();Object.defineProperty(Ra,"__esModule",{value:!0});Ra.montgomery=gy;var Vl=zr(),Gr=ar(),xn=BigInt(0),pa=BigInt(1);function Ey(r){return(0,Gr.validateObject)(r,{a:"bigint"},{montgomeryBits:"isSafeInteger",nByteLength:"isSafeInteger",adjustScalarBytes:"function",domain:"function",powPminus2:"function",Gu:"bigint"}),Object.freeze({...r})}function gy(r){let e=Ey(r),{P:t}=e,n=k=>(0,Vl.mod)(k,t),i=e.montgomeryBits,o=Math.ceil(i/8),s=e.nByteLength,u=e.adjustScalarBytes||(k=>k),l=e.powPminus2||(k=>(0,Vl.pow)(k,t-BigInt(2),t));function p(k,B,F){let $=n(k*(B-F));return B=n(B-$),F=n(F+$),[B,F]}let R=(e.a-BigInt(2))/BigInt(4);function g(k,B){(0,Gr.aInRange)("u",k,xn,t),(0,Gr.aInRange)("scalar",B,xn,t);let F=B,$=k,H=pa,K=xn,j=k,J=pa,me=xn,ee;for(let b=BigInt(i-1);b>=xn;b--){let a=F>>b&pa;me^=a,ee=p(me,H,j),H=ee[0],j=ee[1],ee=p(me,K,J),K=ee[0],J=ee[1],me=a;let d=H+K,h=n(d*d),_=H-K,E=n(_*_),S=h-E,C=j+J,y=j-J,f=n(y*d),A=n(C*_),G=f+A,q=f-A;j=n(G*G),J=n($*n(q*q)),H=n(h*E),K=n(S*(h+n(R*S)))}ee=p(me,H,j),H=ee[0],j=ee[1],ee=p(me,K,J),K=ee[0],J=ee[1];let ae=l(K);return n(H*ae)}function v(k){return(0,Gr.numberToBytesLE)(n(k),o)}function m(k){let B=(0,Gr.ensureBytes)("u coordinate",k,o);return s===32&&(B[31]&=127),(0,Gr.bytesToNumberLE)(B)}function x(k){let B=(0,Gr.ensureBytes)("scalar",k),F=B.length;if(F!==o&&F!==s){let $=""+o+" or "+s;throw new Error("invalid scalar, expected "+$+" bytes, got "+F)}return(0,Gr.bytesToNumberLE)(u(B))}function I(k,B){let F=m(B),$=x(k),H=g(F,$);if(H===xn)throw new Error("invalid private or public key received");return v(H)}let O=v(e.Gu);function D(k){return I(k,O)}return{scalarMult:I,scalarMultBase:D,getSharedSecret:(k,B)=>I(k,B),getPublicKey:k=>D(k),utils:{randomPrivateKey:()=>e.randomBytes(e.nByteLength)},GuBytes:O}}});var ef=Re(le=>{"use strict";z();Object.defineProperty(le,"__esModule",{value:!0});le.hash_to_ristretto255=le.hashToRistretto255=le.RistrettoPoint=le.encodeToCurve=le.hashToCurve=le.edwardsToMontgomery=le.x25519=le.ed25519ph=le.ed25519ctx=le.ed25519=le.ED25519_TORSION_SUBGROUP=void 0;le.edwardsToMontgomeryPub=Jl;le.edwardsToMontgomeryPriv=Sy;var Qi=pl(),Xn=Nr(),yy=Xi(),ma=Pl(),Wl=_a(),Ce=zr(),Ay=Gl(),ln=ar(),Zn=BigInt("57896044618658097711785492504343953926634992332820282019728792003956564819949"),ya=BigInt("19681161376707505956807079304988542015446066515923890162744021073123829784752"),my=BigInt(0),_r=BigInt(1),Ji=BigInt(2),$l=BigInt(3),Yl=BigInt(5),Sa=BigInt(8);function jl(r){let e=BigInt(10),t=BigInt(20),n=BigInt(40),i=BigInt(80),o=Zn,u=r*r%o*r%o,l=(0,Ce.pow2)(u,Ji,o)*u%o,p=(0,Ce.pow2)(l,_r,o)*r%o,R=(0,Ce.pow2)(p,Yl,o)*p%o,g=(0,Ce.pow2)(R,e,o)*R%o,v=(0,Ce.pow2)(g,t,o)*g%o,m=(0,Ce.pow2)(v,n,o)*v%o,x=(0,Ce.pow2)(m,i,o)*m%o,I=(0,Ce.pow2)(x,i,o)*m%o,O=(0,Ce.pow2)(I,e,o)*R%o;return{pow_p_5_8:(0,Ce.pow2)(O,Ji,o)*r%o,b2:u}}function Xl(r){return r[0]&=248,r[31]&=127,r[31]|=64,r}function ba(r,e){let t=Zn,n=(0,Ce.mod)(e*e*e,t),i=(0,Ce.mod)(n*n*e,t),o=jl(r*i).pow_p_5_8,s=(0,Ce.mod)(r*n*o,t),u=(0,Ce.mod)(e*s*s,t),l=s,p=(0,Ce.mod)(s*ya,t),R=u===r,g=u===(0,Ce.mod)(-r,t),v=u===(0,Ce.mod)(-r*ya,t);return R&&(s=l),(g||v)&&(s=p),(0,Ce.isNegativeLE)(s,t)&&(s=(0,Ce.mod)(-s,t)),{isValid:R||g,value:s}}le.ED25519_TORSION_SUBGROUP=["0100000000000000000000000000000000000000000000000000000000000000","c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac037a","0000000000000000000000000000000000000000000000000000000000000080","26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc05","ecffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff7f","26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc85","0000000000000000000000000000000000000000000000000000000000000000","c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac03fa"];var Q=(0,Ce.Field)(Zn,void 0,!0),Jn={a:BigInt(-1),d:BigInt("37095705934669439343138083508754565189542113879843219016388785533085940283555"),Fp:Q,n:BigInt("7237005577332262213973186563042994240857116359379907606001950938285454250989"),h:Sa,Gx:BigInt("15112221349535400772501151409588531511454012693041857206046113283949847762202"),Gy:BigInt("46316835694926478169428394003475163141307993866256225615783033603165251855960"),hash:Qi.sha512,randomBytes:Xn.randomBytes,adjustScalarBytes:Xl,uvRatio:ba};le.ed25519=(0,ma.twistedEdwards)(Jn);function Zl(r,e,t){if(e.length>255)throw new Error("Context is too big");return(0,Xn.concatBytes)((0,Xn.utf8ToBytes)("SigEd25519 no Ed25519 collisions"),new Uint8Array([t?1:0,e.length]),e,r)}le.ed25519ctx=(0,ma.twistedEdwards)({...Jn,domain:Zl});le.ed25519ph=(0,ma.twistedEdwards)(Object.assign({},Jn,{domain:Zl,prehash:Qi.sha512}));le.x25519=(0,Ay.montgomery)({P:Zn,a:BigInt(486662),montgomeryBits:255,nByteLength:32,Gu:BigInt(9),powPminus2:r=>{let e=Zn,{pow_p_5_8:t,b2:n}=jl(r);return(0,Ce.mod)((0,Ce.pow2)(t,$l,e)*n,e)},adjustScalarBytes:Xl,randomBytes:Xn.randomBytes});function Jl(r){let{y:e}=le.ed25519.ExtendedPoint.fromHex(r),t=BigInt(1);return Q.toBytes(Q.create((t+e)*Q.inv(t-e)))}le.edwardsToMontgomery=Jl;function Sy(r){let e=Jn.hash(r.subarray(0,32));return Jn.adjustScalarBytes(e).subarray(0,32)}var by=(Q.ORDER+$l)/Sa,Oy=Q.pow(Ji,by),Hl=Q.sqrt(Q.neg(Q.ONE));function wy(r){let e=(Q.ORDER-Yl)/Sa,t=BigInt(486662),n=Q.sqr(r);n=Q.mul(n,Ji);let i=Q.add(n,Q.ONE),o=Q.neg(t),s=Q.sqr(i),u=Q.mul(s,i),l=Q.mul(n,t);l=Q.mul(l,o),l=Q.add(l,s),l=Q.mul(l,o);let p=Q.sqr(u);s=Q.sqr(p),p=Q.mul(p,u),p=Q.mul(p,l),s=Q.mul(s,p);let R=Q.pow(s,e);R=Q.mul(R,p);let g=Q.mul(R,Hl);s=Q.sqr(R),s=Q.mul(s,u);let v=Q.eql(s,l),m=Q.cmov(g,R,v),x=Q.mul(o,n),I=Q.mul(R,r);I=Q.mul(I,Oy);let O=Q.mul(I,Hl),D=Q.mul(l,n);s=Q.sqr(I),s=Q.mul(s,u);let k=Q.eql(s,D),B=Q.cmov(O,I,k);s=Q.sqr(m),s=Q.mul(s,u);let F=Q.eql(s,l),$=Q.cmov(x,o,F),H=Q.cmov(B,m,F),K=Q.isOdd(H);return H=Q.cmov(H,Q.neg(H),F!==K),{xMn:$,xMd:i,yMn:H,yMd:_r}}var Iy=(0,Ce.FpSqrtEven)(Q,Q.neg(BigInt(486664)));function Ny(r){let{xMn:e,xMd:t,yMn:n,yMd:i}=wy(r),o=Q.mul(e,i);o=Q.mul(o,Iy);let s=Q.mul(t,n),u=Q.sub(e,t),l=Q.add(e,t),p=Q.mul(s,l),R=Q.eql(p,Q.ZERO);o=Q.cmov(o,Q.ZERO,R),s=Q.cmov(s,Q.ONE,R),u=Q.cmov(u,Q.ONE,R),l=Q.cmov(l,Q.ONE,R);let g=Q.invertBatch([s,l]);return{x:Q.mul(o,g[0]),y:Q.mul(u,g[1])}}var Ql=(0,Wl.createHasher)(le.ed25519.ExtendedPoint,r=>Ny(r[0]),{DST:"edwards25519_XMD:SHA-512_ELL2_RO_",encodeDST:"edwards25519_XMD:SHA-512_ELL2_NU_",p:Q.ORDER,m:1,k:128,expand:"xmd",hash:Qi.sha512});le.hashToCurve=Ql.hashToCurve;le.encodeToCurve=Ql.encodeToCurve;function Ea(r){if(!(r instanceof ur))throw new Error("RistrettoPoint expected")}var Aa=ya,Ty=BigInt("25063068953384623474111414158702152701244531502492656460079210482610430750235"),vy=BigInt("54469307008909316920995813868745141605393597292927456921205312896311721017578"),Cy=BigInt("1159843021668779879193775521855586647937357759715417654439879720876111806838"),xy=BigInt("40440834346308536858101042469323190826248399146238708352240133220865137265952"),Kl=r=>ba(_r,r),Ly=BigInt("0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"),ga=r=>le.ed25519.CURVE.Fp.create((0,ln.bytesToNumberLE)(r)&Ly);function ql(r){let{d:e}=le.ed25519.CURVE,t=le.ed25519.CURVE.Fp.ORDER,n=le.ed25519.CURVE.Fp.create,i=n(Aa*r*r),o=n((i+_r)*Cy),s=BigInt(-1),u=n((s-e*i)*n(i+e)),{isValid:l,value:p}=ba(o,u),R=n(p*r);(0,Ce.isNegativeLE)(R,t)||(R=n(-R)),l||(p=R),l||(s=i);let g=n(s*(i-_r)*xy-u),v=p*p,m=n((p+p)*u),x=n(g*Ty),I=n(_r-v),O=n(_r+v);return new le.ed25519.ExtendedPoint(n(m*O),n(I*x),n(x*O),n(m*I))}var ur=class r{constructor(e){this.ep=e}static fromAffine(e){return new r(le.ed25519.ExtendedPoint.fromAffine(e))}static hashToCurve(e){e=(0,ln.ensureBytes)("ristrettoHash",e,64);let t=ga(e.slice(0,32)),n=ql(t),i=ga(e.slice(32,64)),o=ql(i);return new r(n.add(o))}static fromHex(e){e=(0,ln.ensureBytes)("ristrettoHex",e,32);let{a:t,d:n}=le.ed25519.CURVE,i=le.ed25519.CURVE.Fp.ORDER,o=le.ed25519.CURVE.Fp.create,s="RistrettoPoint.fromHex: the hex is not valid encoding of RistrettoPoint",u=ga(e);if(!(0,ln.equalBytes)((0,ln.numberToBytesLE)(u,32),e)||(0,Ce.isNegativeLE)(u,i))throw new Error(s);let l=o(u*u),p=o(_r+t*l),R=o(_r-t*l),g=o(p*p),v=o(R*R),m=o(t*n*g-v),{isValid:x,value:I}=Kl(o(m*v)),O=o(I*R),D=o(I*O*m),k=o((u+u)*O);(0,Ce.isNegativeLE)(k,i)&&(k=o(-k));let B=o(p*D),F=o(k*B);if(!x||(0,Ce.isNegativeLE)(F,i)||B===my)throw new Error(s);return new r(new le.ed25519.ExtendedPoint(k,B,_r,F))}static msm(e,t){let n=(0,Ce.Field)(le.ed25519.CURVE.n,le.ed25519.CURVE.nBitLength);return(0,yy.pippenger)(r,n,e,t)}toRawBytes(){let{ex:e,ey:t,ez:n,et:i}=this.ep,o=le.ed25519.CURVE.Fp.ORDER,s=le.ed25519.CURVE.Fp.create,u=s(s(n+t)*s(n-t)),l=s(e*t),p=s(l*l),{value:R}=Kl(s(u*p)),g=s(R*u),v=s(R*l),m=s(g*v*i),x;if((0,Ce.isNegativeLE)(i*m,o)){let O=s(t*Aa),D=s(e*Aa);e=O,t=D,x=s(g*vy)}else x=v;(0,Ce.isNegativeLE)(e*m,o)&&(t=s(-t));let I=s((n-t)*x);return(0,Ce.isNegativeLE)(I,o)&&(I=s(-I)),(0,ln.numberToBytesLE)(I,32)}toHex(){return(0,ln.bytesToHex)(this.toRawBytes())}toString(){return this.toHex()}equals(e){Ea(e);let{ex:t,ey:n}=this.ep,{ex:i,ey:o}=e.ep,s=le.ed25519.CURVE.Fp.create,u=s(t*o)===s(n*i),l=s(n*o)===s(t*i);return u||l}add(e){return Ea(e),new r(this.ep.add(e.ep))}subtract(e){return Ea(e),new r(this.ep.subtract(e.ep))}multiply(e){return new r(this.ep.multiply(e))}multiplyUnsafe(e){return new r(this.ep.multiplyUnsafe(e))}double(){return new r(this.ep.double())}negate(){return new r(this.ep.negate())}};le.RistrettoPoint=(ur.BASE||(ur.BASE=new ur(le.ed25519.ExtendedPoint.BASE)),ur.ZERO||(ur.ZERO=new ur(le.ed25519.ExtendedPoint.ZERO)),ur);var ky=(r,e)=>{let t=e.DST,n=typeof t=="string"?(0,Xn.utf8ToBytes)(t):t,i=(0,Wl.expand_message_xmd)(r,n,64,Qi.sha512);return ur.hashToCurve(i)};le.hashToRistretto255=ky;le.hash_to_ristretto255=le.hashToRistretto255});var tf=Re(()=>{z()});var wa=Re((rf,Oa)=>{z();(function(r,e){"use strict";function t(b,a){if(!b)throw new Error(a||"Assertion failed")}function n(b,a){b.super_=a;var d=function(){};d.prototype=a.prototype,b.prototype=new d,b.prototype.constructor=b}function i(b,a,d){if(i.isBN(b))return b;this.negative=0,this.words=null,this.length=0,this.red=null,b!==null&&((a==="le"||a==="be")&&(d=a,a=10),this._init(b||0,a||10,d||"be"))}typeof r=="object"?r.exports=i:e.BN=i,i.BN=i,i.wordSize=26;var o;try{typeof window<"u"&&typeof window.Buffer<"u"?o=window.Buffer:o=tf().Buffer}catch{}i.isBN=function(a){return a instanceof i?!0:a!==null&&typeof a=="object"&&a.constructor.wordSize===i.wordSize&&Array.isArray(a.words)},i.max=function(a,d){return a.cmp(d)>0?a:d},i.min=function(a,d){return a.cmp(d)<0?a:d},i.prototype._init=function(a,d,h){if(typeof a=="number")return this._initNumber(a,d,h);if(typeof a=="object")return this._initArray(a,d,h);d==="hex"&&(d=16),t(d===(d|0)&&d>=2&&d<=36),a=a.toString().replace(/\s+/g,"");var _=0;a[0]==="-"&&(_++,this.negative=1),_<a.length&&(d===16?this._parseHex(a,_,h):(this._parseBase(a,d,_),h==="le"&&this._initArray(this.toArray(),d,h)))},i.prototype._initNumber=function(a,d,h){a<0&&(this.negative=1,a=-a),a<67108864?(this.words=[a&67108863],this.length=1):a<4503599627370496?(this.words=[a&67108863,a/67108864&67108863],this.length=2):(t(a<9007199254740992),this.words=[a&67108863,a/67108864&67108863,1],this.length=3),h==="le"&&this._initArray(this.toArray(),d,h)},i.prototype._initArray=function(a,d,h){if(t(typeof a.length=="number"),a.length<=0)return this.words=[0],this.length=1,this;this.length=Math.ceil(a.length/3),this.words=new Array(this.length);for(var _=0;_<this.length;_++)this.words[_]=0;var E,S,C=0;if(h==="be")for(_=a.length-1,E=0;_>=0;_-=3)S=a[_]|a[_-1]<<8|a[_-2]<<16,this.words[E]|=S<<C&67108863,this.words[E+1]=S>>>26-C&67108863,C+=24,C>=26&&(C-=26,E++);else if(h==="le")for(_=0,E=0;_<a.length;_+=3)S=a[_]|a[_+1]<<8|a[_+2]<<16,this.words[E]|=S<<C&67108863,this.words[E+1]=S>>>26-C&67108863,C+=24,C>=26&&(C-=26,E++);return this._strip()};function s(b,a){var d=b.charCodeAt(a);if(d>=48&&d<=57)return d-48;if(d>=65&&d<=70)return d-55;if(d>=97&&d<=102)return d-87;t(!1,"Invalid character in "+b)}function u(b,a,d){var h=s(b,d);return d-1>=a&&(h|=s(b,d-1)<<4),h}i.prototype._parseHex=function(a,d,h){this.length=Math.ceil((a.length-d)/6),this.words=new Array(this.length);for(var _=0;_<this.length;_++)this.words[_]=0;var E=0,S=0,C;if(h==="be")for(_=a.length-1;_>=d;_-=2)C=u(a,d,_)<<E,this.words[S]|=C&67108863,E>=18?(E-=18,S+=1,this.words[S]|=C>>>26):E+=8;else{var y=a.length-d;for(_=y%2===0?d+1:d;_<a.length;_+=2)C=u(a,d,_)<<E,this.words[S]|=C&67108863,E>=18?(E-=18,S+=1,this.words[S]|=C>>>26):E+=8}this._strip()};function l(b,a,d,h){for(var _=0,E=0,S=Math.min(b.length,d),C=a;C<S;C++){var y=b.charCodeAt(C)-48;_*=h,y>=49?E=y-49+10:y>=17?E=y-17+10:E=y,t(y>=0&&E<h,"Invalid character"),_+=E}return _}i.prototype._parseBase=function(a,d,h){this.words=[0],this.length=1;for(var _=0,E=1;E<=67108863;E*=d)_++;_--,E=E/d|0;for(var S=a.length-h,C=S%_,y=Math.min(S,S-C)+h,f=0,A=h;A<y;A+=_)f=l(a,A,A+_,d),this.imuln(E),this.words[0]+f<67108864?this.words[0]+=f:this._iaddn(f);if(C!==0){var G=1;for(f=l(a,A,a.length,d),A=0;A<C;A++)G*=d;this.imuln(G),this.words[0]+f<67108864?this.words[0]+=f:this._iaddn(f)}this._strip()},i.prototype.copy=function(a){a.words=new Array(this.length);for(var d=0;d<this.length;d++)a.words[d]=this.words[d];a.length=this.length,a.negative=this.negative,a.red=this.red};function p(b,a){b.words=a.words,b.length=a.length,b.negative=a.negative,b.red=a.red}if(i.prototype._move=function(a){p(a,this)},i.prototype.clone=function(){var a=new i(null);return this.copy(a),a},i.prototype._expand=function(a){for(;this.length<a;)this.words[this.length++]=0;return this},i.prototype._strip=function(){for(;this.length>1&&this.words[this.length-1]===0;)this.length--;return this._normSign()},i.prototype._normSign=function(){return this.length===1&&this.words[0]===0&&(this.negative=0),this},typeof Symbol<"u"&&typeof Symbol.for=="function")try{i.prototype[Symbol.for("nodejs.util.inspect.custom")]=R}catch{i.prototype.inspect=R}else i.prototype.inspect=R;function R(){return(this.red?"<BN-R: ":"<BN: ")+this.toString(16)+">"}var g=["","0","00","000","0000","00000","000000","0000000","00000000","000000000","0000000000","00000000000","000000000000","0000000000000","00000000000000","000000000000000","0000000000000000","00000000000000000","000000000000000000","0000000000000000000","00000000000000000000","000000000000000000000","0000000000000000000000","00000000000000000000000","000000000000000000000000","0000000000000000000000000"],v=[0,0,25,16,12,11,10,9,8,8,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5],m=[0,0,33554432,43046721,16777216,48828125,60466176,40353607,16777216,43046721,1e7,19487171,35831808,62748517,7529536,11390625,16777216,24137569,34012224,47045881,64e6,4084101,5153632,6436343,7962624,9765625,11881376,14348907,17210368,20511149,243e5,28629151,33554432,39135393,45435424,52521875,60466176];i.prototype.toString=function(a,d){a=a||10,d=d|0||1;var h;if(a===16||a==="hex"){h="";for(var _=0,E=0,S=0;S<this.length;S++){var C=this.words[S],y=((C<<_|E)&16777215).toString(16);E=C>>>24-_&16777215,_+=2,_>=26&&(_-=26,S--),E!==0||S!==this.length-1?h=g[6-y.length]+y+h:h=y+h}for(E!==0&&(h=E.toString(16)+h);h.length%d!==0;)h="0"+h;return this.negative!==0&&(h="-"+h),h}if(a===(a|0)&&a>=2&&a<=36){var f=v[a],A=m[a];h="";var G=this.clone();for(G.negative=0;!G.isZero();){var q=G.modrn(A).toString(a);G=G.idivn(A),G.isZero()?h=q+h:h=g[f-q.length]+q+h}for(this.isZero()&&(h="0"+h);h.length%d!==0;)h="0"+h;return this.negative!==0&&(h="-"+h),h}t(!1,"Base should be between 2 and 36")},i.prototype.toNumber=function(){var a=this.words[0];return this.length===2?a+=this.words[1]*67108864:this.length===3&&this.words[2]===1?a+=4503599627370496+this.words[1]*67108864:this.length>2&&t(!1,"Number can only safely store up to 53 bits"),this.negative!==0?-a:a},i.prototype.toJSON=function(){return this.toString(16,2)},o&&(i.prototype.toBuffer=function(a,d){return this.toArrayLike(o,a,d)}),i.prototype.toArray=function(a,d){return this.toArrayLike(Array,a,d)};var x=function(a,d){return a.allocUnsafe?a.allocUnsafe(d):new a(d)};i.prototype.toArrayLike=function(a,d,h){this._strip();var _=this.byteLength(),E=h||Math.max(1,_);t(_<=E,"byte array longer than desired length"),t(E>0,"Requested array length <= 0");var S=x(a,E),C=d==="le"?"LE":"BE";return this["_toArrayLike"+C](S,_),S},i.prototype._toArrayLikeLE=function(a,d){for(var h=0,_=0,E=0,S=0;E<this.length;E++){var C=this.words[E]<<S|_;a[h++]=C&255,h<a.length&&(a[h++]=C>>8&255),h<a.length&&(a[h++]=C>>16&255),S===6?(h<a.length&&(a[h++]=C>>24&255),_=0,S=0):(_=C>>>24,S+=2)}if(h<a.length)for(a[h++]=_;h<a.length;)a[h++]=0},i.prototype._toArrayLikeBE=function(a,d){for(var h=a.length-1,_=0,E=0,S=0;E<this.length;E++){var C=this.words[E]<<S|_;a[h--]=C&255,h>=0&&(a[h--]=C>>8&255),h>=0&&(a[h--]=C>>16&255),S===6?(h>=0&&(a[h--]=C>>24&255),_=0,S=0):(_=C>>>24,S+=2)}if(h>=0)for(a[h--]=_;h>=0;)a[h--]=0},Math.clz32?i.prototype._countBits=function(a){return 32-Math.clz32(a)}:i.prototype._countBits=function(a){var d=a,h=0;return d>=4096&&(h+=13,d>>>=13),d>=64&&(h+=7,d>>>=7),d>=8&&(h+=4,d>>>=4),d>=2&&(h+=2,d>>>=2),h+d},i.prototype._zeroBits=function(a){if(a===0)return 26;var d=a,h=0;return d&8191||(h+=13,d>>>=13),d&127||(h+=7,d>>>=7),d&15||(h+=4,d>>>=4),d&3||(h+=2,d>>>=2),d&1||h++,h},i.prototype.bitLength=function(){var a=this.words[this.length-1],d=this._countBits(a);return(this.length-1)*26+d};function I(b){for(var a=new Array(b.bitLength()),d=0;d<a.length;d++){var h=d/26|0,_=d%26;a[d]=b.words[h]>>>_&1}return a}i.prototype.zeroBits=function(){if(this.isZero())return 0;for(var a=0,d=0;d<this.length;d++){var h=this._zeroBits(this.words[d]);if(a+=h,h!==26)break}return a},i.prototype.byteLength=function(){return Math.ceil(this.bitLength()/8)},i.prototype.toTwos=function(a){return this.negative!==0?this.abs().inotn(a).iaddn(1):this.clone()},i.prototype.fromTwos=function(a){return this.testn(a-1)?this.notn(a).iaddn(1).ineg():this.clone()},i.prototype.isNeg=function(){return this.negative!==0},i.prototype.neg=function(){return this.clone().ineg()},i.prototype.ineg=function(){return this.isZero()||(this.negative^=1),this},i.prototype.iuor=function(a){for(;this.length<a.length;)this.words[this.length++]=0;for(var d=0;d<a.length;d++)this.words[d]=this.words[d]|a.words[d];return this._strip()},i.prototype.ior=function(a){return t((this.negative|a.negative)===0),this.iuor(a)},i.prototype.or=function(a){return this.length>a.length?this.clone().ior(a):a.clone().ior(this)},i.prototype.uor=function(a){return this.length>a.length?this.clone().iuor(a):a.clone().iuor(this)},i.prototype.iuand=function(a){var d;this.length>a.length?d=a:d=this;for(var h=0;h<d.length;h++)this.words[h]=this.words[h]&a.words[h];return this.length=d.length,this._strip()},i.prototype.iand=function(a){return t((this.negative|a.negative)===0),this.iuand(a)},i.prototype.and=function(a){return this.length>a.length?this.clone().iand(a):a.clone().iand(this)},i.prototype.uand=function(a){return this.length>a.length?this.clone().iuand(a):a.clone().iuand(this)},i.prototype.iuxor=function(a){var d,h;this.length>a.length?(d=this,h=a):(d=a,h=this);for(var _=0;_<h.length;_++)this.words[_]=d.words[_]^h.words[_];if(this!==d)for(;_<d.length;_++)this.words[_]=d.words[_];return this.length=d.length,this._strip()},i.prototype.ixor=function(a){return t((this.negative|a.negative)===0),this.iuxor(a)},i.prototype.xor=function(a){return this.length>a.length?this.clone().ixor(a):a.clone().ixor(this)},i.prototype.uxor=function(a){return this.length>a.length?this.clone().iuxor(a):a.clone().iuxor(this)},i.prototype.inotn=function(a){t(typeof a=="number"&&a>=0);var d=Math.ceil(a/26)|0,h=a%26;this._expand(d),h>0&&d--;for(var _=0;_<d;_++)this.words[_]=~this.words[_]&67108863;return h>0&&(this.words[_]=~this.words[_]&67108863>>26-h),this._strip()},i.prototype.notn=function(a){return this.clone().inotn(a)},i.prototype.setn=function(a,d){t(typeof a=="number"&&a>=0);var h=a/26|0,_=a%26;return this._expand(h+1),d?this.words[h]=this.words[h]|1<<_:this.words[h]=this.words[h]&~(1<<_),this._strip()},i.prototype.iadd=function(a){var d;if(this.negative!==0&&a.negative===0)return this.negative=0,d=this.isub(a),this.negative^=1,this._normSign();if(this.negative===0&&a.negative!==0)return a.negative=0,d=this.isub(a),a.negative=1,d._normSign();var h,_;this.length>a.length?(h=this,_=a):(h=a,_=this);for(var E=0,S=0;S<_.length;S++)d=(h.words[S]|0)+(_.words[S]|0)+E,this.words[S]=d&67108863,E=d>>>26;for(;E!==0&&S<h.length;S++)d=(h.words[S]|0)+E,this.words[S]=d&67108863,E=d>>>26;if(this.length=h.length,E!==0)this.words[this.length]=E,this.length++;else if(h!==this)for(;S<h.length;S++)this.words[S]=h.words[S];return this},i.prototype.add=function(a){var d;return a.negative!==0&&this.negative===0?(a.negative=0,d=this.sub(a),a.negative^=1,d):a.negative===0&&this.negative!==0?(this.negative=0,d=a.sub(this),this.negative=1,d):this.length>a.length?this.clone().iadd(a):a.clone().iadd(this)},i.prototype.isub=function(a){if(a.negative!==0){a.negative=0;var d=this.iadd(a);return a.negative=1,d._normSign()}else if(this.negative!==0)return this.negative=0,this.iadd(a),this.negative=1,this._normSign();var h=this.cmp(a);if(h===0)return this.negative=0,this.length=1,this.words[0]=0,this;var _,E;h>0?(_=this,E=a):(_=a,E=this);for(var S=0,C=0;C<E.length;C++)d=(_.words[C]|0)-(E.words[C]|0)+S,S=d>>26,this.words[C]=d&67108863;for(;S!==0&&C<_.length;C++)d=(_.words[C]|0)+S,S=d>>26,this.words[C]=d&67108863;if(S===0&&C<_.length&&_!==this)for(;C<_.length;C++)this.words[C]=_.words[C];return this.length=Math.max(this.length,C),_!==this&&(this.negative=1),this._strip()},i.prototype.sub=function(a){return this.clone().isub(a)};function O(b,a,d){d.negative=a.negative^b.negative;var h=b.length+a.length|0;d.length=h,h=h-1|0;var _=b.words[0]|0,E=a.words[0]|0,S=_*E,C=S&67108863,y=S/67108864|0;d.words[0]=C;for(var f=1;f<h;f++){for(var A=y>>>26,G=y&67108863,q=Math.min(f,a.length-1),Y=Math.max(0,f-b.length+1);Y<=q;Y++){var te=f-Y|0;_=b.words[te]|0,E=a.words[Y]|0,S=_*E+G,A+=S/67108864|0,G=S&67108863}d.words[f]=G|0,y=A|0}return y!==0?d.words[f]=y|0:d.length--,d._strip()}var D=function(a,d,h){var _=a.words,E=d.words,S=h.words,C=0,y,f,A,G=_[0]|0,q=G&8191,Y=G>>>13,te=_[1]|0,oe=te&8191,ce=te>>>13,Ue=_[2]|0,he=Ue&8191,ue=Ue>>>13,zt=_[3]|0,we=zt&8191,Le=zt>>>13,bn=_[4]|0,Xe=bn&8191,Ze=bn>>>13,rn=_[5]|0,Ke=rn&8191,qe=rn>>>13,Dr=_[6]|0,We=Dr&8191,$e=Dr>>>13,Jt=_[7]|0,N=Jt&8191,L=Jt>>>13,M=_[8]|0,U=M&8191,W=M>>>13,ie=_[9]|0,ne=ie&8191,_e=ie>>>13,Je=E[0]|0,Ee=Je&8191,Ie=Je>>>13,xi=E[1]|0,tt=xi&8191,Ye=xi>>>13,fu=E[2]|0,it=fu&8191,ot=fu>>>13,du=E[3]|0,st=du&8191,at=du>>>13,hu=E[4]|0,ct=hu&8191,ut=hu>>>13,_u=E[5]|0,lt=_u&8191,ft=_u>>>13,pu=E[6]|0,dt=pu&8191,ht=pu>>>13,Ru=E[7]|0,_t=Ru&8191,pt=Ru>>>13,Eu=E[8]|0,Rt=Eu&8191,Et=Eu>>>13,gu=E[9]|0,gt=gu&8191,yt=gu>>>13;h.negative=a.negative^d.negative,h.length=19,y=Math.imul(q,Ee),f=Math.imul(q,Ie),f=f+Math.imul(Y,Ee)|0,A=Math.imul(Y,Ie);var _s=(C+y|0)+((f&8191)<<13)|0;C=(A+(f>>>13)|0)+(_s>>>26)|0,_s&=67108863,y=Math.imul(oe,Ee),f=Math.imul(oe,Ie),f=f+Math.imul(ce,Ee)|0,A=Math.imul(ce,Ie),y=y+Math.imul(q,tt)|0,f=f+Math.imul(q,Ye)|0,f=f+Math.imul(Y,tt)|0,A=A+Math.imul(Y,Ye)|0;var ps=(C+y|0)+((f&8191)<<13)|0;C=(A+(f>>>13)|0)+(ps>>>26)|0,ps&=67108863,y=Math.imul(he,Ee),f=Math.imul(he,Ie),f=f+Math.imul(ue,Ee)|0,A=Math.imul(ue,Ie),y=y+Math.imul(oe,tt)|0,f=f+Math.imul(oe,Ye)|0,f=f+Math.imul(ce,tt)|0,A=A+Math.imul(ce,Ye)|0,y=y+Math.imul(q,it)|0,f=f+Math.imul(q,ot)|0,f=f+Math.imul(Y,it)|0,A=A+Math.imul(Y,ot)|0;var Rs=(C+y|0)+((f&8191)<<13)|0;C=(A+(f>>>13)|0)+(Rs>>>26)|0,Rs&=67108863,y=Math.imul(we,Ee),f=Math.imul(we,Ie),f=f+Math.imul(Le,Ee)|0,A=Math.imul(Le,Ie),y=y+Math.imul(he,tt)|0,f=f+Math.imul(he,Ye)|0,f=f+Math.imul(ue,tt)|0,A=A+Math.imul(ue,Ye)|0,y=y+Math.imul(oe,it)|0,f=f+Math.imul(oe,ot)|0,f=f+Math.imul(ce,it)|0,A=A+Math.imul(ce,ot)|0,y=y+Math.imul(q,st)|0,f=f+Math.imul(q,at)|0,f=f+Math.imul(Y,st)|0,A=A+Math.imul(Y,at)|0;var Es=(C+y|0)+((f&8191)<<13)|0;C=(A+(f>>>13)|0)+(Es>>>26)|0,Es&=67108863,y=Math.imul(Xe,Ee),f=Math.imul(Xe,Ie),f=f+Math.imul(Ze,Ee)|0,A=Math.imul(Ze,Ie),y=y+Math.imul(we,tt)|0,f=f+Math.imul(we,Ye)|0,f=f+Math.imul(Le,tt)|0,A=A+Math.imul(Le,Ye)|0,y=y+Math.imul(he,it)|0,f=f+Math.imul(he,ot)|0,f=f+Math.imul(ue,it)|0,A=A+Math.imul(ue,ot)|0,y=y+Math.imul(oe,st)|0,f=f+Math.imul(oe,at)|0,f=f+Math.imul(ce,st)|0,A=A+Math.imul(ce,at)|0,y=y+Math.imul(q,ct)|0,f=f+Math.imul(q,ut)|0,f=f+Math.imul(Y,ct)|0,A=A+Math.imul(Y,ut)|0;var gs=(C+y|0)+((f&8191)<<13)|0;C=(A+(f>>>13)|0)+(gs>>>26)|0,gs&=67108863,y=Math.imul(Ke,Ee),f=Math.imul(Ke,Ie),f=f+Math.imul(qe,Ee)|0,A=Math.imul(qe,Ie),y=y+Math.imul(Xe,tt)|0,f=f+Math.imul(Xe,Ye)|0,f=f+Math.imul(Ze,tt)|0,A=A+Math.imul(Ze,Ye)|0,y=y+Math.imul(we,it)|0,f=f+Math.imul(we,ot)|0,f=f+Math.imul(Le,it)|0,A=A+Math.imul(Le,ot)|0,y=y+Math.imul(he,st)|0,f=f+Math.imul(he,at)|0,f=f+Math.imul(ue,st)|0,A=A+Math.imul(ue,at)|0,y=y+Math.imul(oe,ct)|0,f=f+Math.imul(oe,ut)|0,f=f+Math.imul(ce,ct)|0,A=A+Math.imul(ce,ut)|0,y=y+Math.imul(q,lt)|0,f=f+Math.imul(q,ft)|0,f=f+Math.imul(Y,lt)|0,A=A+Math.imul(Y,ft)|0;var ys=(C+y|0)+((f&8191)<<13)|0;C=(A+(f>>>13)|0)+(ys>>>26)|0,ys&=67108863,y=Math.imul(We,Ee),f=Math.imul(We,Ie),f=f+Math.imul($e,Ee)|0,A=Math.imul($e,Ie),y=y+Math.imul(Ke,tt)|0,f=f+Math.imul(Ke,Ye)|0,f=f+Math.imul(qe,tt)|0,A=A+Math.imul(qe,Ye)|0,y=y+Math.imul(Xe,it)|0,f=f+Math.imul(Xe,ot)|0,f=f+Math.imul(Ze,it)|0,A=A+Math.imul(Ze,ot)|0,y=y+Math.imul(we,st)|0,f=f+Math.imul(we,at)|0,f=f+Math.imul(Le,st)|0,A=A+Math.imul(Le,at)|0,y=y+Math.imul(he,ct)|0,f=f+Math.imul(he,ut)|0,f=f+Math.imul(ue,ct)|0,A=A+Math.imul(ue,ut)|0,y=y+Math.imul(oe,lt)|0,f=f+Math.imul(oe,ft)|0,f=f+Math.imul(ce,lt)|0,A=A+Math.imul(ce,ft)|0,y=y+Math.imul(q,dt)|0,f=f+Math.imul(q,ht)|0,f=f+Math.imul(Y,dt)|0,A=A+Math.imul(Y,ht)|0;var As=(C+y|0)+((f&8191)<<13)|0;C=(A+(f>>>13)|0)+(As>>>26)|0,As&=67108863,y=Math.imul(N,Ee),f=Math.imul(N,Ie),f=f+Math.imul(L,Ee)|0,A=Math.imul(L,Ie),y=y+Math.imul(We,tt)|0,f=f+Math.imul(We,Ye)|0,f=f+Math.imul($e,tt)|0,A=A+Math.imul($e,Ye)|0,y=y+Math.imul(Ke,it)|0,f=f+Math.imul(Ke,ot)|0,f=f+Math.imul(qe,it)|0,A=A+Math.imul(qe,ot)|0,y=y+Math.imul(Xe,st)|0,f=f+Math.imul(Xe,at)|0,f=f+Math.imul(Ze,st)|0,A=A+Math.imul(Ze,at)|0,y=y+Math.imul(we,ct)|0,f=f+Math.imul(we,ut)|0,f=f+Math.imul(Le,ct)|0,A=A+Math.imul(Le,ut)|0,y=y+Math.imul(he,lt)|0,f=f+Math.imul(he,ft)|0,f=f+Math.imul(ue,lt)|0,A=A+Math.imul(ue,ft)|0,y=y+Math.imul(oe,dt)|0,f=f+Math.imul(oe,ht)|0,f=f+Math.imul(ce,dt)|0,A=A+Math.imul(ce,ht)|0,y=y+Math.imul(q,_t)|0,f=f+Math.imul(q,pt)|0,f=f+Math.imul(Y,_t)|0,A=A+Math.imul(Y,pt)|0;var ms=(C+y|0)+((f&8191)<<13)|0;C=(A+(f>>>13)|0)+(ms>>>26)|0,ms&=67108863,y=Math.imul(U,Ee),f=Math.imul(U,Ie),f=f+Math.imul(W,Ee)|0,A=Math.imul(W,Ie),y=y+Math.imul(N,tt)|0,f=f+Math.imul(N,Ye)|0,f=f+Math.imul(L,tt)|0,A=A+Math.imul(L,Ye)|0,y=y+Math.imul(We,it)|0,f=f+Math.imul(We,ot)|0,f=f+Math.imul($e,it)|0,A=A+Math.imul($e,ot)|0,y=y+Math.imul(Ke,st)|0,f=f+Math.imul(Ke,at)|0,f=f+Math.imul(qe,st)|0,A=A+Math.imul(qe,at)|0,y=y+Math.imul(Xe,ct)|0,f=f+Math.imul(Xe,ut)|0,f=f+Math.imul(Ze,ct)|0,A=A+Math.imul(Ze,ut)|0,y=y+Math.imul(we,lt)|0,f=f+Math.imul(we,ft)|0,f=f+Math.imul(Le,lt)|0,A=A+Math.imul(Le,ft)|0,y=y+Math.imul(he,dt)|0,f=f+Math.imul(he,ht)|0,f=f+Math.imul(ue,dt)|0,A=A+Math.imul(ue,ht)|0,y=y+Math.imul(oe,_t)|0,f=f+Math.imul(oe,pt)|0,f=f+Math.imul(ce,_t)|0,A=A+Math.imul(ce,pt)|0,y=y+Math.imul(q,Rt)|0,f=f+Math.imul(q,Et)|0,f=f+Math.imul(Y,Rt)|0,A=A+Math.imul(Y,Et)|0;var Ss=(C+y|0)+((f&8191)<<13)|0;C=(A+(f>>>13)|0)+(Ss>>>26)|0,Ss&=67108863,y=Math.imul(ne,Ee),f=Math.imul(ne,Ie),f=f+Math.imul(_e,Ee)|0,A=Math.imul(_e,Ie),y=y+Math.imul(U,tt)|0,f=f+Math.imul(U,Ye)|0,f=f+Math.imul(W,tt)|0,A=A+Math.imul(W,Ye)|0,y=y+Math.imul(N,it)|0,f=f+Math.imul(N,ot)|0,f=f+Math.imul(L,it)|0,A=A+Math.imul(L,ot)|0,y=y+Math.imul(We,st)|0,f=f+Math.imul(We,at)|0,f=f+Math.imul($e,st)|0,A=A+Math.imul($e,at)|0,y=y+Math.imul(Ke,ct)|0,f=f+Math.imul(Ke,ut)|0,f=f+Math.imul(qe,ct)|0,A=A+Math.imul(qe,ut)|0,y=y+Math.imul(Xe,lt)|0,f=f+Math.imul(Xe,ft)|0,f=f+Math.imul(Ze,lt)|0,A=A+Math.imul(Ze,ft)|0,y=y+Math.imul(we,dt)|0,f=f+Math.imul(we,ht)|0,f=f+Math.imul(Le,dt)|0,A=A+Math.imul(Le,ht)|0,y=y+Math.imul(he,_t)|0,f=f+Math.imul(he,pt)|0,f=f+Math.imul(ue,_t)|0,A=A+Math.imul(ue,pt)|0,y=y+Math.imul(oe,Rt)|0,f=f+Math.imul(oe,Et)|0,f=f+Math.imul(ce,Rt)|0,A=A+Math.imul(ce,Et)|0,y=y+Math.imul(q,gt)|0,f=f+Math.imul(q,yt)|0,f=f+Math.imul(Y,gt)|0,A=A+Math.imul(Y,yt)|0;var bs=(C+y|0)+((f&8191)<<13)|0;C=(A+(f>>>13)|0)+(bs>>>26)|0,bs&=67108863,y=Math.imul(ne,tt),f=Math.imul(ne,Ye),f=f+Math.imul(_e,tt)|0,A=Math.imul(_e,Ye),y=y+Math.imul(U,it)|0,f=f+Math.imul(U,ot)|0,f=f+Math.imul(W,it)|0,A=A+Math.imul(W,ot)|0,y=y+Math.imul(N,st)|0,f=f+Math.imul(N,at)|0,f=f+Math.imul(L,st)|0,A=A+Math.imul(L,at)|0,y=y+Math.imul(We,ct)|0,f=f+Math.imul(We,ut)|0,f=f+Math.imul($e,ct)|0,A=A+Math.imul($e,ut)|0,y=y+Math.imul(Ke,lt)|0,f=f+Math.imul(Ke,ft)|0,f=f+Math.imul(qe,lt)|0,A=A+Math.imul(qe,ft)|0,y=y+Math.imul(Xe,dt)|0,f=f+Math.imul(Xe,ht)|0,f=f+Math.imul(Ze,dt)|0,A=A+Math.imul(Ze,ht)|0,y=y+Math.imul(we,_t)|0,f=f+Math.imul(we,pt)|0,f=f+Math.imul(Le,_t)|0,A=A+Math.imul(Le,pt)|0,y=y+Math.imul(he,Rt)|0,f=f+Math.imul(he,Et)|0,f=f+Math.imul(ue,Rt)|0,A=A+Math.imul(ue,Et)|0,y=y+Math.imul(oe,gt)|0,f=f+Math.imul(oe,yt)|0,f=f+Math.imul(ce,gt)|0,A=A+Math.imul(ce,yt)|0;var Os=(C+y|0)+((f&8191)<<13)|0;C=(A+(f>>>13)|0)+(Os>>>26)|0,Os&=67108863,y=Math.imul(ne,it),f=Math.imul(ne,ot),f=f+Math.imul(_e,it)|0,A=Math.imul(_e,ot),y=y+Math.imul(U,st)|0,f=f+Math.imul(U,at)|0,f=f+Math.imul(W,st)|0,A=A+Math.imul(W,at)|0,y=y+Math.imul(N,ct)|0,f=f+Math.imul(N,ut)|0,f=f+Math.imul(L,ct)|0,A=A+Math.imul(L,ut)|0,y=y+Math.imul(We,lt)|0,f=f+Math.imul(We,ft)|0,f=f+Math.imul($e,lt)|0,A=A+Math.imul($e,ft)|0,y=y+Math.imul(Ke,dt)|0,f=f+Math.imul(Ke,ht)|0,f=f+Math.imul(qe,dt)|0,A=A+Math.imul(qe,ht)|0,y=y+Math.imul(Xe,_t)|0,f=f+Math.imul(Xe,pt)|0,f=f+Math.imul(Ze,_t)|0,A=A+Math.imul(Ze,pt)|0,y=y+Math.imul(we,Rt)|0,f=f+Math.imul(we,Et)|0,f=f+Math.imul(Le,Rt)|0,A=A+Math.imul(Le,Et)|0,y=y+Math.imul(he,gt)|0,f=f+Math.imul(he,yt)|0,f=f+Math.imul(ue,gt)|0,A=A+Math.imul(ue,yt)|0;var ws=(C+y|0)+((f&8191)<<13)|0;C=(A+(f>>>13)|0)+(ws>>>26)|0,ws&=67108863,y=Math.imul(ne,st),f=Math.imul(ne,at),f=f+Math.imul(_e,st)|0,A=Math.imul(_e,at),y=y+Math.imul(U,ct)|0,f=f+Math.imul(U,ut)|0,f=f+Math.imul(W,ct)|0,A=A+Math.imul(W,ut)|0,y=y+Math.imul(N,lt)|0,f=f+Math.imul(N,ft)|0,f=f+Math.imul(L,lt)|0,A=A+Math.imul(L,ft)|0,y=y+Math.imul(We,dt)|0,f=f+Math.imul(We,ht)|0,f=f+Math.imul($e,dt)|0,A=A+Math.imul($e,ht)|0,y=y+Math.imul(Ke,_t)|0,f=f+Math.imul(Ke,pt)|0,f=f+Math.imul(qe,_t)|0,A=A+Math.imul(qe,pt)|0,y=y+Math.imul(Xe,Rt)|0,f=f+Math.imul(Xe,Et)|0,f=f+Math.imul(Ze,Rt)|0,A=A+Math.imul(Ze,Et)|0,y=y+Math.imul(we,gt)|0,f=f+Math.imul(we,yt)|0,f=f+Math.imul(Le,gt)|0,A=A+Math.imul(Le,yt)|0;var Is=(C+y|0)+((f&8191)<<13)|0;C=(A+(f>>>13)|0)+(Is>>>26)|0,Is&=67108863,y=Math.imul(ne,ct),f=Math.imul(ne,ut),f=f+Math.imul(_e,ct)|0,A=Math.imul(_e,ut),y=y+Math.imul(U,lt)|0,f=f+Math.imul(U,ft)|0,f=f+Math.imul(W,lt)|0,A=A+Math.imul(W,ft)|0,y=y+Math.imul(N,dt)|0,f=f+Math.imul(N,ht)|0,f=f+Math.imul(L,dt)|0,A=A+Math.imul(L,ht)|0,y=y+Math.imul(We,_t)|0,f=f+Math.imul(We,pt)|0,f=f+Math.imul($e,_t)|0,A=A+Math.imul($e,pt)|0,y=y+Math.imul(Ke,Rt)|0,f=f+Math.imul(Ke,Et)|0,f=f+Math.imul(qe,Rt)|0,A=A+Math.imul(qe,Et)|0,y=y+Math.imul(Xe,gt)|0,f=f+Math.imul(Xe,yt)|0,f=f+Math.imul(Ze,gt)|0,A=A+Math.imul(Ze,yt)|0;var Ns=(C+y|0)+((f&8191)<<13)|0;C=(A+(f>>>13)|0)+(Ns>>>26)|0,Ns&=67108863,y=Math.imul(ne,lt),f=Math.imul(ne,ft),f=f+Math.imul(_e,lt)|0,A=Math.imul(_e,ft),y=y+Math.imul(U,dt)|0,f=f+Math.imul(U,ht)|0,f=f+Math.imul(W,dt)|0,A=A+Math.imul(W,ht)|0,y=y+Math.imul(N,_t)|0,f=f+Math.imul(N,pt)|0,f=f+Math.imul(L,_t)|0,A=A+Math.imul(L,pt)|0,y=y+Math.imul(We,Rt)|0,f=f+Math.imul(We,Et)|0,f=f+Math.imul($e,Rt)|0,A=A+Math.imul($e,Et)|0,y=y+Math.imul(Ke,gt)|0,f=f+Math.imul(Ke,yt)|0,f=f+Math.imul(qe,gt)|0,A=A+Math.imul(qe,yt)|0;var Ts=(C+y|0)+((f&8191)<<13)|0;C=(A+(f>>>13)|0)+(Ts>>>26)|0,Ts&=67108863,y=Math.imul(ne,dt),f=Math.imul(ne,ht),f=f+Math.imul(_e,dt)|0,A=Math.imul(_e,ht),y=y+Math.imul(U,_t)|0,f=f+Math.imul(U,pt)|0,f=f+Math.imul(W,_t)|0,A=A+Math.imul(W,pt)|0,y=y+Math.imul(N,Rt)|0,f=f+Math.imul(N,Et)|0,f=f+Math.imul(L,Rt)|0,A=A+Math.imul(L,Et)|0,y=y+Math.imul(We,gt)|0,f=f+Math.imul(We,yt)|0,f=f+Math.imul($e,gt)|0,A=A+Math.imul($e,yt)|0;var vs=(C+y|0)+((f&8191)<<13)|0;C=(A+(f>>>13)|0)+(vs>>>26)|0,vs&=67108863,y=Math.imul(ne,_t),f=Math.imul(ne,pt),f=f+Math.imul(_e,_t)|0,A=Math.imul(_e,pt),y=y+Math.imul(U,Rt)|0,f=f+Math.imul(U,Et)|0,f=f+Math.imul(W,Rt)|0,A=A+Math.imul(W,Et)|0,y=y+Math.imul(N,gt)|0,f=f+Math.imul(N,yt)|0,f=f+Math.imul(L,gt)|0,A=A+Math.imul(L,yt)|0;var Cs=(C+y|0)+((f&8191)<<13)|0;C=(A+(f>>>13)|0)+(Cs>>>26)|0,Cs&=67108863,y=Math.imul(ne,Rt),f=Math.imul(ne,Et),f=f+Math.imul(_e,Rt)|0,A=Math.imul(_e,Et),y=y+Math.imul(U,gt)|0,f=f+Math.imul(U,yt)|0,f=f+Math.imul(W,gt)|0,A=A+Math.imul(W,yt)|0;var xs=(C+y|0)+((f&8191)<<13)|0;C=(A+(f>>>13)|0)+(xs>>>26)|0,xs&=67108863,y=Math.imul(ne,gt),f=Math.imul(ne,yt),f=f+Math.imul(_e,gt)|0,A=Math.imul(_e,yt);var Ls=(C+y|0)+((f&8191)<<13)|0;return C=(A+(f>>>13)|0)+(Ls>>>26)|0,Ls&=67108863,S[0]=_s,S[1]=ps,S[2]=Rs,S[3]=Es,S[4]=gs,S[5]=ys,S[6]=As,S[7]=ms,S[8]=Ss,S[9]=bs,S[10]=Os,S[11]=ws,S[12]=Is,S[13]=Ns,S[14]=Ts,S[15]=vs,S[16]=Cs,S[17]=xs,S[18]=Ls,C!==0&&(S[19]=C,h.length++),h};Math.imul||(D=O);function k(b,a,d){d.negative=a.negative^b.negative,d.length=b.length+a.length;for(var h=0,_=0,E=0;E<d.length-1;E++){var S=_;_=0;for(var C=h&67108863,y=Math.min(E,a.length-1),f=Math.max(0,E-b.length+1);f<=y;f++){var A=E-f,G=b.words[A]|0,q=a.words[f]|0,Y=G*q,te=Y&67108863;S=S+(Y/67108864|0)|0,te=te+C|0,C=te&67108863,S=S+(te>>>26)|0,_+=S>>>26,S&=67108863}d.words[E]=C,h=S,S=_}return h!==0?d.words[E]=h:d.length--,d._strip()}function B(b,a,d){return k(b,a,d)}i.prototype.mulTo=function(a,d){var h,_=this.length+a.length;return this.length===10&&a.length===10?h=D(this,a,d):_<63?h=O(this,a,d):_<1024?h=k(this,a,d):h=B(this,a,d),h};function F(b,a){this.x=b,this.y=a}F.prototype.makeRBT=function(a){for(var d=new Array(a),h=i.prototype._countBits(a)-1,_=0;_<a;_++)d[_]=this.revBin(_,h,a);return d},F.prototype.revBin=function(a,d,h){if(a===0||a===h-1)return a;for(var _=0,E=0;E<d;E++)_|=(a&1)<<d-E-1,a>>=1;return _},F.prototype.permute=function(a,d,h,_,E,S){for(var C=0;C<S;C++)_[C]=d[a[C]],E[C]=h[a[C]]},F.prototype.transform=function(a,d,h,_,E,S){this.permute(S,a,d,h,_,E);for(var C=1;C<E;C<<=1)for(var y=C<<1,f=Math.cos(2*Math.PI/y),A=Math.sin(2*Math.PI/y),G=0;G<E;G+=y)for(var q=f,Y=A,te=0;te<C;te++){var oe=h[G+te],ce=_[G+te],Ue=h[G+te+C],he=_[G+te+C],ue=q*Ue-Y*he;he=q*he+Y*Ue,Ue=ue,h[G+te]=oe+Ue,_[G+te]=ce+he,h[G+te+C]=oe-Ue,_[G+te+C]=ce-he,te!==y&&(ue=f*q-A*Y,Y=f*Y+A*q,q=ue)}},F.prototype.guessLen13b=function(a,d){var h=Math.max(d,a)|1,_=h&1,E=0;for(h=h/2|0;h;h=h>>>1)E++;return 1<<E+1+_},F.prototype.conjugate=function(a,d,h){if(!(h<=1))for(var _=0;_<h/2;_++){var E=a[_];a[_]=a[h-_-1],a[h-_-1]=E,E=d[_],d[_]=-d[h-_-1],d[h-_-1]=-E}},F.prototype.normalize13b=function(a,d){for(var h=0,_=0;_<d/2;_++){var E=Math.round(a[2*_+1]/d)*8192+Math.round(a[2*_]/d)+h;a[_]=E&67108863,E<67108864?h=0:h=E/67108864|0}return a},F.prototype.convert13b=function(a,d,h,_){for(var E=0,S=0;S<d;S++)E=E+(a[S]|0),h[2*S]=E&8191,E=E>>>13,h[2*S+1]=E&8191,E=E>>>13;for(S=2*d;S<_;++S)h[S]=0;t(E===0),t((E&-8192)===0)},F.prototype.stub=function(a){for(var d=new Array(a),h=0;h<a;h++)d[h]=0;return d},F.prototype.mulp=function(a,d,h){var _=2*this.guessLen13b(a.length,d.length),E=this.makeRBT(_),S=this.stub(_),C=new Array(_),y=new Array(_),f=new Array(_),A=new Array(_),G=new Array(_),q=new Array(_),Y=h.words;Y.length=_,this.convert13b(a.words,a.length,C,_),this.convert13b(d.words,d.length,A,_),this.transform(C,S,y,f,_,E),this.transform(A,S,G,q,_,E);for(var te=0;te<_;te++){var oe=y[te]*G[te]-f[te]*q[te];f[te]=y[te]*q[te]+f[te]*G[te],y[te]=oe}return this.conjugate(y,f,_),this.transform(y,f,Y,S,_,E),this.conjugate(Y,S,_),this.normalize13b(Y,_),h.negative=a.negative^d.negative,h.length=a.length+d.length,h._strip()},i.prototype.mul=function(a){var d=new i(null);return d.words=new Array(this.length+a.length),this.mulTo(a,d)},i.prototype.mulf=function(a){var d=new i(null);return d.words=new Array(this.length+a.length),B(this,a,d)},i.prototype.imul=function(a){return this.clone().mulTo(a,this)},i.prototype.imuln=function(a){var d=a<0;d&&(a=-a),t(typeof a=="number"),t(a<67108864);for(var h=0,_=0;_<this.length;_++){var E=(this.words[_]|0)*a,S=(E&67108863)+(h&67108863);h>>=26,h+=E/67108864|0,h+=S>>>26,this.words[_]=S&67108863}return h!==0&&(this.words[_]=h,this.length++),d?this.ineg():this},i.prototype.muln=function(a){return this.clone().imuln(a)},i.prototype.sqr=function(){return this.mul(this)},i.prototype.isqr=function(){return this.imul(this.clone())},i.prototype.pow=function(a){var d=I(a);if(d.length===0)return new i(1);for(var h=this,_=0;_<d.length&&d[_]===0;_++,h=h.sqr());if(++_<d.length)for(var E=h.sqr();_<d.length;_++,E=E.sqr())d[_]!==0&&(h=h.mul(E));return h},i.prototype.iushln=function(a){t(typeof a=="number"&&a>=0);var d=a%26,h=(a-d)/26,_=67108863>>>26-d<<26-d,E;if(d!==0){var S=0;for(E=0;E<this.length;E++){var C=this.words[E]&_,y=(this.words[E]|0)-C<<d;this.words[E]=y|S,S=C>>>26-d}S&&(this.words[E]=S,this.length++)}if(h!==0){for(E=this.length-1;E>=0;E--)this.words[E+h]=this.words[E];for(E=0;E<h;E++)this.words[E]=0;this.length+=h}return this._strip()},i.prototype.ishln=function(a){return t(this.negative===0),this.iushln(a)},i.prototype.iushrn=function(a,d,h){t(typeof a=="number"&&a>=0);var _;d?_=(d-d%26)/26:_=0;var E=a%26,S=Math.min((a-E)/26,this.length),C=67108863^67108863>>>E<<E,y=h;if(_-=S,_=Math.max(0,_),y){for(var f=0;f<S;f++)y.words[f]=this.words[f];y.length=S}if(S!==0)if(this.length>S)for(this.length-=S,f=0;f<this.length;f++)this.words[f]=this.words[f+S];else this.words[0]=0,this.length=1;var A=0;for(f=this.length-1;f>=0&&(A!==0||f>=_);f--){var G=this.words[f]|0;this.words[f]=A<<26-E|G>>>E,A=G&C}return y&&A!==0&&(y.words[y.length++]=A),this.length===0&&(this.words[0]=0,this.length=1),this._strip()},i.prototype.ishrn=function(a,d,h){return t(this.negative===0),this.iushrn(a,d,h)},i.prototype.shln=function(a){return this.clone().ishln(a)},i.prototype.ushln=function(a){return this.clone().iushln(a)},i.prototype.shrn=function(a){return this.clone().ishrn(a)},i.prototype.ushrn=function(a){return this.clone().iushrn(a)},i.prototype.testn=function(a){t(typeof a=="number"&&a>=0);var d=a%26,h=(a-d)/26,_=1<<d;if(this.length<=h)return!1;var E=this.words[h];return!!(E&_)},i.prototype.imaskn=function(a){t(typeof a=="number"&&a>=0);var d=a%26,h=(a-d)/26;if(t(this.negative===0,"imaskn works only with positive numbers"),this.length<=h)return this;if(d!==0&&h++,this.length=Math.min(h,this.length),d!==0){var _=67108863^67108863>>>d<<d;this.words[this.length-1]&=_}return this._strip()},i.prototype.maskn=function(a){return this.clone().imaskn(a)},i.prototype.iaddn=function(a){return t(typeof a=="number"),t(a<67108864),a<0?this.isubn(-a):this.negative!==0?this.length===1&&(this.words[0]|0)<=a?(this.words[0]=a-(this.words[0]|0),this.negative=0,this):(this.negative=0,this.isubn(a),this.negative=1,this):this._iaddn(a)},i.prototype._iaddn=function(a){this.words[0]+=a;for(var d=0;d<this.length&&this.words[d]>=67108864;d++)this.words[d]-=67108864,d===this.length-1?this.words[d+1]=1:this.words[d+1]++;return this.length=Math.max(this.length,d+1),this},i.prototype.isubn=function(a){if(t(typeof a=="number"),t(a<67108864),a<0)return this.iaddn(-a);if(this.negative!==0)return this.negative=0,this.iaddn(a),this.negative=1,this;if(this.words[0]-=a,this.length===1&&this.words[0]<0)this.words[0]=-this.words[0],this.negative=1;else for(var d=0;d<this.length&&this.words[d]<0;d++)this.words[d]+=67108864,this.words[d+1]-=1;return this._strip()},i.prototype.addn=function(a){return this.clone().iaddn(a)},i.prototype.subn=function(a){return this.clone().isubn(a)},i.prototype.iabs=function(){return this.negative=0,this},i.prototype.abs=function(){return this.clone().iabs()},i.prototype._ishlnsubmul=function(a,d,h){var _=a.length+h,E;this._expand(_);var S,C=0;for(E=0;E<a.length;E++){S=(this.words[E+h]|0)+C;var y=(a.words[E]|0)*d;S-=y&67108863,C=(S>>26)-(y/67108864|0),this.words[E+h]=S&67108863}for(;E<this.length-h;E++)S=(this.words[E+h]|0)+C,C=S>>26,this.words[E+h]=S&67108863;if(C===0)return this._strip();for(t(C===-1),C=0,E=0;E<this.length;E++)S=-(this.words[E]|0)+C,C=S>>26,this.words[E]=S&67108863;return this.negative=1,this._strip()},i.prototype._wordDiv=function(a,d){var h=this.length-a.length,_=this.clone(),E=a,S=E.words[E.length-1]|0,C=this._countBits(S);h=26-C,h!==0&&(E=E.ushln(h),_.iushln(h),S=E.words[E.length-1]|0);var y=_.length-E.length,f;if(d!=="mod"){f=new i(null),f.length=y+1,f.words=new Array(f.length);for(var A=0;A<f.length;A++)f.words[A]=0}var G=_.clone()._ishlnsubmul(E,1,y);G.negative===0&&(_=G,f&&(f.words[y]=1));for(var q=y-1;q>=0;q--){var Y=(_.words[E.length+q]|0)*67108864+(_.words[E.length+q-1]|0);for(Y=Math.min(Y/S|0,67108863),_._ishlnsubmul(E,Y,q);_.negative!==0;)Y--,_.negative=0,_._ishlnsubmul(E,1,q),_.isZero()||(_.negative^=1);f&&(f.words[q]=Y)}return f&&f._strip(),_._strip(),d!=="div"&&h!==0&&_.iushrn(h),{div:f||null,mod:_}},i.prototype.divmod=function(a,d,h){if(t(!a.isZero()),this.isZero())return{div:new i(0),mod:new i(0)};var _,E,S;return this.negative!==0&&a.negative===0?(S=this.neg().divmod(a,d),d!=="mod"&&(_=S.div.neg()),d!=="div"&&(E=S.mod.neg(),h&&E.negative!==0&&E.iadd(a)),{div:_,mod:E}):this.negative===0&&a.negative!==0?(S=this.divmod(a.neg(),d),d!=="mod"&&(_=S.div.neg()),{div:_,mod:S.mod}):this.negative&a.negative?(S=this.neg().divmod(a.neg(),d),d!=="div"&&(E=S.mod.neg(),h&&E.negative!==0&&E.isub(a)),{div:S.div,mod:E}):a.length>this.length||this.cmp(a)<0?{div:new i(0),mod:this}:a.length===1?d==="div"?{div:this.divn(a.words[0]),mod:null}:d==="mod"?{div:null,mod:new i(this.modrn(a.words[0]))}:{div:this.divn(a.words[0]),mod:new i(this.modrn(a.words[0]))}:this._wordDiv(a,d)},i.prototype.div=function(a){return this.divmod(a,"div",!1).div},i.prototype.mod=function(a){return this.divmod(a,"mod",!1).mod},i.prototype.umod=function(a){return this.divmod(a,"mod",!0).mod},i.prototype.divRound=function(a){var d=this.divmod(a);if(d.mod.isZero())return d.div;var h=d.div.negative!==0?d.mod.isub(a):d.mod,_=a.ushrn(1),E=a.andln(1),S=h.cmp(_);return S<0||E===1&&S===0?d.div:d.div.negative!==0?d.div.isubn(1):d.div.iaddn(1)},i.prototype.modrn=function(a){var d=a<0;d&&(a=-a),t(a<=67108863);for(var h=(1<<26)%a,_=0,E=this.length-1;E>=0;E--)_=(h*_+(this.words[E]|0))%a;return d?-_:_},i.prototype.modn=function(a){return this.modrn(a)},i.prototype.idivn=function(a){var d=a<0;d&&(a=-a),t(a<=67108863);for(var h=0,_=this.length-1;_>=0;_--){var E=(this.words[_]|0)+h*67108864;this.words[_]=E/a|0,h=E%a}return this._strip(),d?this.ineg():this},i.prototype.divn=function(a){return this.clone().idivn(a)},i.prototype.egcd=function(a){t(a.negative===0),t(!a.isZero());var d=this,h=a.clone();d.negative!==0?d=d.umod(a):d=d.clone();for(var _=new i(1),E=new i(0),S=new i(0),C=new i(1),y=0;d.isEven()&&h.isEven();)d.iushrn(1),h.iushrn(1),++y;for(var f=h.clone(),A=d.clone();!d.isZero();){for(var G=0,q=1;!(d.words[0]&q)&&G<26;++G,q<<=1);if(G>0)for(d.iushrn(G);G-- >0;)(_.isOdd()||E.isOdd())&&(_.iadd(f),E.isub(A)),_.iushrn(1),E.iushrn(1);for(var Y=0,te=1;!(h.words[0]&te)&&Y<26;++Y,te<<=1);if(Y>0)for(h.iushrn(Y);Y-- >0;)(S.isOdd()||C.isOdd())&&(S.iadd(f),C.isub(A)),S.iushrn(1),C.iushrn(1);d.cmp(h)>=0?(d.isub(h),_.isub(S),E.isub(C)):(h.isub(d),S.isub(_),C.isub(E))}return{a:S,b:C,gcd:h.iushln(y)}},i.prototype._invmp=function(a){t(a.negative===0),t(!a.isZero());var d=this,h=a.clone();d.negative!==0?d=d.umod(a):d=d.clone();for(var _=new i(1),E=new i(0),S=h.clone();d.cmpn(1)>0&&h.cmpn(1)>0;){for(var C=0,y=1;!(d.words[0]&y)&&C<26;++C,y<<=1);if(C>0)for(d.iushrn(C);C-- >0;)_.isOdd()&&_.iadd(S),_.iushrn(1);for(var f=0,A=1;!(h.words[0]&A)&&f<26;++f,A<<=1);if(f>0)for(h.iushrn(f);f-- >0;)E.isOdd()&&E.iadd(S),E.iushrn(1);d.cmp(h)>=0?(d.isub(h),_.isub(E)):(h.isub(d),E.isub(_))}var G;return d.cmpn(1)===0?G=_:G=E,G.cmpn(0)<0&&G.iadd(a),G},i.prototype.gcd=function(a){if(this.isZero())return a.abs();if(a.isZero())return this.abs();var d=this.clone(),h=a.clone();d.negative=0,h.negative=0;for(var _=0;d.isEven()&&h.isEven();_++)d.iushrn(1),h.iushrn(1);do{for(;d.isEven();)d.iushrn(1);for(;h.isEven();)h.iushrn(1);var E=d.cmp(h);if(E<0){var S=d;d=h,h=S}else if(E===0||h.cmpn(1)===0)break;d.isub(h)}while(!0);return h.iushln(_)},i.prototype.invm=function(a){return this.egcd(a).a.umod(a)},i.prototype.isEven=function(){return(this.words[0]&1)===0},i.prototype.isOdd=function(){return(this.words[0]&1)===1},i.prototype.andln=function(a){return this.words[0]&a},i.prototype.bincn=function(a){t(typeof a=="number");var d=a%26,h=(a-d)/26,_=1<<d;if(this.length<=h)return this._expand(h+1),this.words[h]|=_,this;for(var E=_,S=h;E!==0&&S<this.length;S++){var C=this.words[S]|0;C+=E,E=C>>>26,C&=67108863,this.words[S]=C}return E!==0&&(this.words[S]=E,this.length++),this},i.prototype.isZero=function(){return this.length===1&&this.words[0]===0},i.prototype.cmpn=function(a){var d=a<0;if(this.negative!==0&&!d)return-1;if(this.negative===0&&d)return 1;this._strip();var h;if(this.length>1)h=1;else{d&&(a=-a),t(a<=67108863,"Number is too big");var _=this.words[0]|0;h=_===a?0:_<a?-1:1}return this.negative!==0?-h|0:h},i.prototype.cmp=function(a){if(this.negative!==0&&a.negative===0)return-1;if(this.negative===0&&a.negative!==0)return 1;var d=this.ucmp(a);return this.negative!==0?-d|0:d},i.prototype.ucmp=function(a){if(this.length>a.length)return 1;if(this.length<a.length)return-1;for(var d=0,h=this.length-1;h>=0;h--){var _=this.words[h]|0,E=a.words[h]|0;if(_!==E){_<E?d=-1:_>E&&(d=1);break}}return d},i.prototype.gtn=function(a){return this.cmpn(a)===1},i.prototype.gt=function(a){return this.cmp(a)===1},i.prototype.gten=function(a){return this.cmpn(a)>=0},i.prototype.gte=function(a){return this.cmp(a)>=0},i.prototype.ltn=function(a){return this.cmpn(a)===-1},i.prototype.lt=function(a){return this.cmp(a)===-1},i.prototype.lten=function(a){return this.cmpn(a)<=0},i.prototype.lte=function(a){return this.cmp(a)<=0},i.prototype.eqn=function(a){return this.cmpn(a)===0},i.prototype.eq=function(a){return this.cmp(a)===0},i.red=function(a){return new ee(a)},i.prototype.toRed=function(a){return t(!this.red,"Already a number in reduction context"),t(this.negative===0,"red works only with positives"),a.convertTo(this)._forceRed(a)},i.prototype.fromRed=function(){return t(this.red,"fromRed works only with numbers in reduction context"),this.red.convertFrom(this)},i.prototype._forceRed=function(a){return this.red=a,this},i.prototype.forceRed=function(a){return t(!this.red,"Already a number in reduction context"),this._forceRed(a)},i.prototype.redAdd=function(a){return t(this.red,"redAdd works only with red numbers"),this.red.add(this,a)},i.prototype.redIAdd=function(a){return t(this.red,"redIAdd works only with red numbers"),this.red.iadd(this,a)},i.prototype.redSub=function(a){return t(this.red,"redSub works only with red numbers"),this.red.sub(this,a)},i.prototype.redISub=function(a){return t(this.red,"redISub works only with red numbers"),this.red.isub(this,a)},i.prototype.redShl=function(a){return t(this.red,"redShl works only with red numbers"),this.red.shl(this,a)},i.prototype.redMul=function(a){return t(this.red,"redMul works only with red numbers"),this.red._verify2(this,a),this.red.mul(this,a)},i.prototype.redIMul=function(a){return t(this.red,"redMul works only with red numbers"),this.red._verify2(this,a),this.red.imul(this,a)},i.prototype.redSqr=function(){return t(this.red,"redSqr works only with red numbers"),this.red._verify1(this),this.red.sqr(this)},i.prototype.redISqr=function(){return t(this.red,"redISqr works only with red numbers"),this.red._verify1(this),this.red.isqr(this)},i.prototype.redSqrt=function(){return t(this.red,"redSqrt works only with red numbers"),this.red._verify1(this),this.red.sqrt(this)},i.prototype.redInvm=function(){return t(this.red,"redInvm works only with red numbers"),this.red._verify1(this),this.red.invm(this)},i.prototype.redNeg=function(){return t(this.red,"redNeg works only with red numbers"),this.red._verify1(this),this.red.neg(this)},i.prototype.redPow=function(a){return t(this.red&&!a.red,"redPow(normalNum)"),this.red._verify1(this),this.red.pow(this,a)};var $={k256:null,p224:null,p192:null,p25519:null};function H(b,a){this.name=b,this.p=new i(a,16),this.n=this.p.bitLength(),this.k=new i(1).iushln(this.n).isub(this.p),this.tmp=this._tmp()}H.prototype._tmp=function(){var a=new i(null);return a.words=new Array(Math.ceil(this.n/13)),a},H.prototype.ireduce=function(a){var d=a,h;do this.split(d,this.tmp),d=this.imulK(d),d=d.iadd(this.tmp),h=d.bitLength();while(h>this.n);var _=h<this.n?-1:d.ucmp(this.p);return _===0?(d.words[0]=0,d.length=1):_>0?d.isub(this.p):d.strip!==void 0?d.strip():d._strip(),d},H.prototype.split=function(a,d){a.iushrn(this.n,0,d)},H.prototype.imulK=function(a){return a.imul(this.k)};function K(){H.call(this,"k256","ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f")}n(K,H),K.prototype.split=function(a,d){for(var h=4194303,_=Math.min(a.length,9),E=0;E<_;E++)d.words[E]=a.words[E];if(d.length=_,a.length<=9){a.words[0]=0,a.length=1;return}var S=a.words[9];for(d.words[d.length++]=S&h,E=10;E<a.length;E++){var C=a.words[E]|0;a.words[E-10]=(C&h)<<4|S>>>22,S=C}S>>>=22,a.words[E-10]=S,S===0&&a.length>10?a.length-=10:a.length-=9},K.prototype.imulK=function(a){a.words[a.length]=0,a.words[a.length+1]=0,a.length+=2;for(var d=0,h=0;h<a.length;h++){var _=a.words[h]|0;d+=_*977,a.words[h]=d&67108863,d=_*64+(d/67108864|0)}return a.words[a.length-1]===0&&(a.length--,a.words[a.length-1]===0&&a.length--),a};function j(){H.call(this,"p224","ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001")}n(j,H);function J(){H.call(this,"p192","ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff")}n(J,H);function me(){H.call(this,"25519","7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed")}n(me,H),me.prototype.imulK=function(a){for(var d=0,h=0;h<a.length;h++){var _=(a.words[h]|0)*19+d,E=_&67108863;_>>>=26,a.words[h]=E,d=_}return d!==0&&(a.words[a.length++]=d),a},i._prime=function(a){if($[a])return $[a];var d;if(a==="k256")d=new K;else if(a==="p224")d=new j;else if(a==="p192")d=new J;else if(a==="p25519")d=new me;else throw new Error("Unknown prime "+a);return $[a]=d,d};function ee(b){if(typeof b=="string"){var a=i._prime(b);this.m=a.p,this.prime=a}else t(b.gtn(1),"modulus must be greater than 1"),this.m=b,this.prime=null}ee.prototype._verify1=function(a){t(a.negative===0,"red works only with positives"),t(a.red,"red works only with red numbers")},ee.prototype._verify2=function(a,d){t((a.negative|d.negative)===0,"red works only with positives"),t(a.red&&a.red===d.red,"red works only with red numbers")},ee.prototype.imod=function(a){return this.prime?this.prime.ireduce(a)._forceRed(this):(p(a,a.umod(this.m)._forceRed(this)),a)},ee.prototype.neg=function(a){return a.isZero()?a.clone():this.m.sub(a)._forceRed(this)},ee.prototype.add=function(a,d){this._verify2(a,d);var h=a.add(d);return h.cmp(this.m)>=0&&h.isub(this.m),h._forceRed(this)},ee.prototype.iadd=function(a,d){this._verify2(a,d);var h=a.iadd(d);return h.cmp(this.m)>=0&&h.isub(this.m),h},ee.prototype.sub=function(a,d){this._verify2(a,d);var h=a.sub(d);return h.cmpn(0)<0&&h.iadd(this.m),h._forceRed(this)},ee.prototype.isub=function(a,d){this._verify2(a,d);var h=a.isub(d);return h.cmpn(0)<0&&h.iadd(this.m),h},ee.prototype.shl=function(a,d){return this._verify1(a),this.imod(a.ushln(d))},ee.prototype.imul=function(a,d){return this._verify2(a,d),this.imod(a.imul(d))},ee.prototype.mul=function(a,d){return this._verify2(a,d),this.imod(a.mul(d))},ee.prototype.isqr=function(a){return this.imul(a,a.clone())},ee.prototype.sqr=function(a){return this.mul(a,a)},ee.prototype.sqrt=function(a){if(a.isZero())return a.clone();var d=this.m.andln(3);if(t(d%2===1),d===3){var h=this.m.add(new i(1)).iushrn(2);return this.pow(a,h)}for(var _=this.m.subn(1),E=0;!_.isZero()&&_.andln(1)===0;)E++,_.iushrn(1);t(!_.isZero());var S=new i(1).toRed(this),C=S.redNeg(),y=this.m.subn(1).iushrn(1),f=this.m.bitLength();for(f=new i(2*f*f).toRed(this);this.pow(f,y).cmp(C)!==0;)f.redIAdd(C);for(var A=this.pow(f,_),G=this.pow(a,_.addn(1).iushrn(1)),q=this.pow(a,_),Y=E;q.cmp(S)!==0;){for(var te=q,oe=0;te.cmp(S)!==0;oe++)te=te.redSqr();t(oe<Y);var ce=this.pow(A,new i(1).iushln(Y-oe-1));G=G.redMul(ce),A=ce.redSqr(),q=q.redMul(A),Y=oe}return G},ee.prototype.invm=function(a){var d=a._invmp(this.m);return d.negative!==0?(d.negative=0,this.imod(d).redNeg()):this.imod(d)},ee.prototype.pow=function(a,d){if(d.isZero())return new i(1).toRed(this);if(d.cmpn(1)===0)return a.clone();var h=4,_=new Array(1<<h);_[0]=new i(1).toRed(this),_[1]=a;for(var E=2;E<_.length;E++)_[E]=this.mul(_[E-1],a);var S=_[0],C=0,y=0,f=d.bitLength()%26;for(f===0&&(f=26),E=d.length-1;E>=0;E--){for(var A=d.words[E],G=f-1;G>=0;G--){var q=A>>G&1;if(S!==_[0]&&(S=this.sqr(S)),q===0&&C===0){y=0;continue}C<<=1,C|=q,y++,!(y!==h&&(E!==0||G!==0))&&(S=this.mul(S,_[C]),y=0,C=0)}f=26}return S},ee.prototype.convertTo=function(a){var d=a.umod(this.m);return d===a?d.clone():d},ee.prototype.convertFrom=function(a){var d=a.clone();return d.red=null,d},i.mont=function(a){return new ae(a)};function ae(b){ee.call(this,b),this.shift=this.m.bitLength(),this.shift%26!==0&&(this.shift+=26-this.shift%26),this.r=new i(1).iushln(this.shift),this.r2=this.imod(this.r.sqr()),this.rinv=this.r._invmp(this.m),this.minv=this.rinv.mul(this.r).isubn(1).div(this.m),this.minv=this.minv.umod(this.r),this.minv=this.r.sub(this.minv)}n(ae,ee),ae.prototype.convertTo=function(a){return this.imod(a.ushln(this.shift))},ae.prototype.convertFrom=function(a){var d=this.imod(a.mul(this.rinv));return d.red=null,d},ae.prototype.imul=function(a,d){if(a.isZero()||d.isZero())return a.words[0]=0,a.length=1,a;var h=a.imul(d),_=h.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),E=h.isub(_).iushrn(this.shift),S=E;return E.cmp(this.m)>=0?S=E.isub(this.m):E.cmpn(0)<0&&(S=E.iadd(this.m)),S._forceRed(this)},ae.prototype.mul=function(a,d){if(a.isZero()||d.isZero())return new i(0)._forceRed(this);var h=a.mul(d),_=h.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),E=h.isub(_).iushrn(this.shift),S=E;return E.cmp(this.m)>=0?S=E.isub(this.m):E.cmpn(0)<0&&(S=E.iadd(this.m)),S._forceRed(this)},ae.prototype.invm=function(a){var d=this.imod(a._invmp(this.m).mul(this.r2));return d._forceRed(this)}})(typeof Oa>"u"||Oa,rf)});var Na=Re((Ia,of)=>{z();var eo=Tn(),pr=eo.Buffer;function nf(r,e){for(var t in r)e[t]=r[t]}pr.from&&pr.alloc&&pr.allocUnsafe&&pr.allocUnsafeSlow?of.exports=eo:(nf(eo,Ia),Ia.Buffer=fn);function fn(r,e,t){return pr(r,e,t)}fn.prototype=Object.create(pr.prototype);nf(pr,fn);fn.from=function(r,e,t){if(typeof r=="number")throw new TypeError("Argument must not be a number");return pr(r,e,t)};fn.alloc=function(r,e,t){if(typeof r!="number")throw new TypeError("Argument must be a number");var n=pr(r);return e!==void 0?typeof t=="string"?n.fill(e,t):n.fill(e):n.fill(0),n};fn.allocUnsafe=function(r){if(typeof r!="number")throw new TypeError("Argument must be a number");return pr(r)};fn.allocUnsafeSlow=function(r){if(typeof r!="number")throw new TypeError("Argument must be a number");return eo.SlowBuffer(r)}});var af=Re((Db,sf)=>{"use strict";z();var to=Na().Buffer;function By(r){if(r.length>=255)throw new TypeError("Alphabet too long");for(var e=new Uint8Array(256),t=0;t<e.length;t++)e[t]=255;for(var n=0;n<r.length;n++){var i=r.charAt(n),o=i.charCodeAt(0);if(e[o]!==255)throw new TypeError(i+" is ambiguous");e[o]=n}var s=r.length,u=r.charAt(0),l=Math.log(s)/Math.log(256),p=Math.log(256)/Math.log(s);function R(m){if((Array.isArray(m)||m instanceof Uint8Array)&&(m=to.from(m)),!to.isBuffer(m))throw new TypeError("Expected Buffer");if(m.length===0)return"";for(var x=0,I=0,O=0,D=m.length;O!==D&&m[O]===0;)O++,x++;for(var k=(D-O)*p+1>>>0,B=new Uint8Array(k);O!==D;){for(var F=m[O],$=0,H=k-1;(F!==0||$<I)&&H!==-1;H--,$++)F+=256*B[H]>>>0,B[H]=F%s>>>0,F=F/s>>>0;if(F!==0)throw new Error("Non-zero carry");I=$,O++}for(var K=k-I;K!==k&&B[K]===0;)K++;for(var j=u.repeat(x);K<k;++K)j+=r.charAt(B[K]);return j}function g(m){if(typeof m!="string")throw new TypeError("Expected String");if(m.length===0)return to.alloc(0);for(var x=0,I=0,O=0;m[x]===u;)I++,x++;for(var D=(m.length-x)*l+1>>>0,k=new Uint8Array(D);x<m.length;){var B=m.charCodeAt(x);if(B>255)return;var F=e[B];if(F===255)return;for(var $=0,H=D-1;(F!==0||$<O)&&H!==-1;H--,$++)F+=s*k[H]>>>0,k[H]=F%256>>>0,F=F/256>>>0;if(F!==0)throw new Error("Non-zero carry");O=$,x++}for(var K=D-O;K!==D&&k[K]===0;)K++;var j=to.allocUnsafe(I+(D-K));j.fill(0,0,I);for(var J=I;K!==D;)j[J++]=k[K++];return j}function v(m){var x=g(m);if(x)return x;throw new Error("Non-base"+s+" character")}return{encode:R,decodeUnsafe:g,decode:v}}sf.exports=By});var uf=Re((Ub,cf)=>{z();var Dy=af(),My="**********************************************************";cf.exports=Dy(My)});var Ca=Re(qr=>{"use strict";z();Object.defineProperty(qr,"__esModule",{value:!0});qr.sha224=qr.sha256=qr.SHA256=void 0;var Ta=js(),ir=Nr(),Uy=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),Hr=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),Kr=new Uint32Array(64),Qn=class extends Ta.HashMD{constructor(){super(64,32,8,!1),this.A=Hr[0]|0,this.B=Hr[1]|0,this.C=Hr[2]|0,this.D=Hr[3]|0,this.E=Hr[4]|0,this.F=Hr[5]|0,this.G=Hr[6]|0,this.H=Hr[7]|0}get(){let{A:e,B:t,C:n,D:i,E:o,F:s,G:u,H:l}=this;return[e,t,n,i,o,s,u,l]}set(e,t,n,i,o,s,u,l){this.A=e|0,this.B=t|0,this.C=n|0,this.D=i|0,this.E=o|0,this.F=s|0,this.G=u|0,this.H=l|0}process(e,t){for(let g=0;g<16;g++,t+=4)Kr[g]=e.getUint32(t,!1);for(let g=16;g<64;g++){let v=Kr[g-15],m=Kr[g-2],x=(0,ir.rotr)(v,7)^(0,ir.rotr)(v,18)^v>>>3,I=(0,ir.rotr)(m,17)^(0,ir.rotr)(m,19)^m>>>10;Kr[g]=I+Kr[g-7]+x+Kr[g-16]|0}let{A:n,B:i,C:o,D:s,E:u,F:l,G:p,H:R}=this;for(let g=0;g<64;g++){let v=(0,ir.rotr)(u,6)^(0,ir.rotr)(u,11)^(0,ir.rotr)(u,25),m=R+v+(0,Ta.Chi)(u,l,p)+Uy[g]+Kr[g]|0,I=((0,ir.rotr)(n,2)^(0,ir.rotr)(n,13)^(0,ir.rotr)(n,22))+(0,Ta.Maj)(n,i,o)|0;R=p,p=l,l=u,u=s+m|0,s=o,o=i,i=n,n=m+I|0}n=n+this.A|0,i=i+this.B|0,o=o+this.C|0,s=s+this.D|0,u=u+this.E|0,l=l+this.F|0,p=p+this.G|0,R=R+this.H|0,this.set(n,i,o,s,u,l,p,R)}roundClean(){Kr.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}};qr.SHA256=Qn;var va=class extends Qn{constructor(){super(),this.A=-1056596264,this.B=914150663,this.C=812702999,this.D=-150054599,this.E=-4191439,this.F=1750603025,this.G=1694076839,this.H=-1090891868,this.outputLen=28}};qr.sha256=(0,ir.wrapConstructor)(()=>new Qn);qr.sha224=(0,ir.wrapConstructor)(()=>new va)});var ff=Re((Vb,lf)=>{"use strict";z();var ro=Na().Buffer;function Py(r){if(r.length>=255)throw new TypeError("Alphabet too long");for(var e=new Uint8Array(256),t=0;t<e.length;t++)e[t]=255;for(var n=0;n<r.length;n++){var i=r.charAt(n),o=i.charCodeAt(0);if(e[o]!==255)throw new TypeError(i+" is ambiguous");e[o]=n}var s=r.length,u=r.charAt(0),l=Math.log(s)/Math.log(256),p=Math.log(256)/Math.log(s);function R(m){if((Array.isArray(m)||m instanceof Uint8Array)&&(m=ro.from(m)),!ro.isBuffer(m))throw new TypeError("Expected Buffer");if(m.length===0)return"";for(var x=0,I=0,O=0,D=m.length;O!==D&&m[O]===0;)O++,x++;for(var k=(D-O)*p+1>>>0,B=new Uint8Array(k);O!==D;){for(var F=m[O],$=0,H=k-1;(F!==0||$<I)&&H!==-1;H--,$++)F+=256*B[H]>>>0,B[H]=F%s>>>0,F=F/s>>>0;if(F!==0)throw new Error("Non-zero carry");I=$,O++}for(var K=k-I;K!==k&&B[K]===0;)K++;for(var j=u.repeat(x);K<k;++K)j+=r.charAt(B[K]);return j}function g(m){if(typeof m!="string")throw new TypeError("Expected String");if(m.length===0)return ro.alloc(0);for(var x=0,I=0,O=0;m[x]===u;)I++,x++;for(var D=(m.length-x)*l+1>>>0,k=new Uint8Array(D);x<m.length;){var B=m.charCodeAt(x);if(B>255)return;var F=e[B];if(F===255)return;for(var $=0,H=D-1;(F!==0||$<O)&&H!==-1;H--,$++)F+=s*k[H]>>>0,k[H]=F%256>>>0,F=F/256>>>0;if(F!==0)throw new Error("Non-zero carry");O=$,x++}for(var K=D-O;K!==D&&k[K]===0;)K++;var j=ro.allocUnsafe(I+(D-K));j.fill(0,0,I);for(var J=I;K!==D;)j[J++]=k[K++];return j}function v(m){var x=g(m);if(x)return x;throw new Error("Non-base"+s+" character")}return{encode:R,decodeUnsafe:g,decode:v}}lf.exports=Py});var hf=Re((Hb,df)=>{z();var Fy=ff(),zy="**********************************************************";df.exports=Fy(zy)});var _f=Re(ka=>{"use strict";z();function vr(r,e,t){return e<=r&&r<=t}function ao(r){if(r===void 0)return{};if(r===Object(r))return r;throw TypeError("Could not convert argument to dictionary")}function Vy(r){for(var e=String(r),t=e.length,n=0,i=[];n<t;){var o=e.charCodeAt(n);if(o<55296||o>57343)i.push(o);else if(56320<=o&&o<=57343)i.push(65533);else if(55296<=o&&o<=56319)if(n===t-1)i.push(65533);else{var s=r.charCodeAt(n+1);if(56320<=s&&s<=57343){var u=o&1023,l=s&1023;i.push(65536+(u<<10)+l),n+=1}else i.push(65533)}n+=1}return i}function Gy(r){for(var e="",t=0;t<r.length;++t){var n=r[t];n<=65535?e+=String.fromCharCode(n):(n-=65536,e+=String.fromCharCode((n>>10)+55296,(n&1023)+56320))}return e}var no=-1;function La(r){this.tokens=[].slice.call(r)}La.prototype={endOfStream:function(){return!this.tokens.length},read:function(){return this.tokens.length?this.tokens.shift():no},prepend:function(r){if(Array.isArray(r))for(var e=r;e.length;)this.tokens.unshift(e.pop());else this.tokens.unshift(r)},push:function(r){if(Array.isArray(r))for(var e=r;e.length;)this.tokens.push(e.shift());else this.tokens.push(r)}};var Ln=-1;function xa(r,e){if(r)throw TypeError("Decoder error");return e||65533}var io="utf-8";function oo(r,e){if(!(this instanceof oo))return new oo(r,e);if(r=r!==void 0?String(r).toLowerCase():io,r!==io)throw new Error("Encoding not supported. Only utf-8 is supported");e=ao(e),this._streaming=!1,this._BOMseen=!1,this._decoder=null,this._fatal=!!e.fatal,this._ignoreBOM=!!e.ignoreBOM,Object.defineProperty(this,"encoding",{value:"utf-8"}),Object.defineProperty(this,"fatal",{value:this._fatal}),Object.defineProperty(this,"ignoreBOM",{value:this._ignoreBOM})}oo.prototype={decode:function(e,t){var n;typeof e=="object"&&e instanceof ArrayBuffer?n=new Uint8Array(e):typeof e=="object"&&"buffer"in e&&e.buffer instanceof ArrayBuffer?n=new Uint8Array(e.buffer,e.byteOffset,e.byteLength):n=new Uint8Array(0),t=ao(t),this._streaming||(this._decoder=new Hy({fatal:this._fatal}),this._BOMseen=!1),this._streaming=!!t.stream;for(var i=new La(n),o=[],s;!i.endOfStream()&&(s=this._decoder.handler(i,i.read()),s!==Ln);)s!==null&&(Array.isArray(s)?o.push.apply(o,s):o.push(s));if(!this._streaming){do{if(s=this._decoder.handler(i,i.read()),s===Ln)break;s!==null&&(Array.isArray(s)?o.push.apply(o,s):o.push(s))}while(!i.endOfStream());this._decoder=null}return o.length&&["utf-8"].indexOf(this.encoding)!==-1&&!this._ignoreBOM&&!this._BOMseen&&(o[0]===65279?(this._BOMseen=!0,o.shift()):this._BOMseen=!0),Gy(o)}};function so(r,e){if(!(this instanceof so))return new so(r,e);if(r=r!==void 0?String(r).toLowerCase():io,r!==io)throw new Error("Encoding not supported. Only utf-8 is supported");e=ao(e),this._streaming=!1,this._encoder=null,this._options={fatal:!!e.fatal},Object.defineProperty(this,"encoding",{value:"utf-8"})}so.prototype={encode:function(e,t){e=e?String(e):"",t=ao(t),this._streaming||(this._encoder=new Ky(this._options)),this._streaming=!!t.stream;for(var n=[],i=new La(Vy(e)),o;!i.endOfStream()&&(o=this._encoder.handler(i,i.read()),o!==Ln);)Array.isArray(o)?n.push.apply(n,o):n.push(o);if(!this._streaming){for(;o=this._encoder.handler(i,i.read()),o!==Ln;)Array.isArray(o)?n.push.apply(n,o):n.push(o);this._encoder=null}return new Uint8Array(n)}};function Hy(r){var e=r.fatal,t=0,n=0,i=0,o=128,s=191;this.handler=function(u,l){if(l===no&&i!==0)return i=0,xa(e);if(l===no)return Ln;if(i===0){if(vr(l,0,127))return l;if(vr(l,194,223))i=1,t=l-192;else if(vr(l,224,239))l===224&&(o=160),l===237&&(s=159),i=2,t=l-224;else if(vr(l,240,244))l===240&&(o=144),l===244&&(s=143),i=3,t=l-240;else return xa(e);return t=t<<6*i,null}if(!vr(l,o,s))return t=i=n=0,o=128,s=191,u.prepend(l),xa(e);if(o=128,s=191,n+=1,t+=l-128<<6*(i-n),n!==i)return null;var p=t;return t=i=n=0,p}}function Ky(r){var e=r.fatal;this.handler=function(t,n){if(n===no)return Ln;if(vr(n,0,127))return n;var i,o;vr(n,128,2047)?(i=1,o=192):vr(n,2048,65535)?(i=2,o=224):vr(n,65536,1114111)&&(i=3,o=240);for(var s=[(n>>6*i)+o];i>0;){var u=n>>6*(i-1);s.push(128|u&63),i-=1}return s}}ka.TextEncoder=so;ka.TextDecoder=oo});var yf=Re(Pe=>{"use strict";z();var qy=Pe&&Pe.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),Wy=Pe&&Pe.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),Rr=Pe&&Pe.__decorate||function(r,e,t,n){var i=arguments.length,o=i<3?e:n===null?n=Object.getOwnPropertyDescriptor(e,t):n,s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")o=Reflect.decorate(r,e,t,n);else for(var u=r.length-1;u>=0;u--)(s=r[u])&&(o=(i<3?s(o):i>3?s(e,t,o):s(e,t))||o);return i>3&&o&&Object.defineProperty(e,t,o),o},$y=Pe&&Pe.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.hasOwnProperty.call(r,t)&&qy(e,r,t);return Wy(e,r),e},pf=Pe&&Pe.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Pe,"__esModule",{value:!0});Pe.deserializeUnchecked=Pe.deserialize=Pe.serialize=Pe.BinaryReader=Pe.BinaryWriter=Pe.BorshError=Pe.baseDecode=Pe.baseEncode=void 0;var Wr=pf(wa()),Rf=pf(hf()),Yy=$y(_f()),jy=typeof TextDecoder!="function"?Yy.TextDecoder:TextDecoder,Xy=new jy("utf-8",{fatal:!0});function Zy(r){return typeof r=="string"&&(r=Buffer.from(r,"utf8")),Rf.default.encode(Buffer.from(r))}Pe.baseEncode=Zy;function Jy(r){return Buffer.from(Rf.default.decode(r))}Pe.baseDecode=Jy;var Ba=1024,Nt=class extends Error{constructor(e){super(e),this.fieldPath=[],this.originalMessage=e}addToFieldPath(e){this.fieldPath.splice(0,0,e),this.message=this.originalMessage+": "+this.fieldPath.join(".")}};Pe.BorshError=Nt;var co=class{constructor(){this.buf=Buffer.alloc(Ba),this.length=0}maybeResize(){this.buf.length<16+this.length&&(this.buf=Buffer.concat([this.buf,Buffer.alloc(Ba)]))}writeU8(e){this.maybeResize(),this.buf.writeUInt8(e,this.length),this.length+=1}writeU16(e){this.maybeResize(),this.buf.writeUInt16LE(e,this.length),this.length+=2}writeU32(e){this.maybeResize(),this.buf.writeUInt32LE(e,this.length),this.length+=4}writeU64(e){this.maybeResize(),this.writeBuffer(Buffer.from(new Wr.default(e).toArray("le",8)))}writeU128(e){this.maybeResize(),this.writeBuffer(Buffer.from(new Wr.default(e).toArray("le",16)))}writeU256(e){this.maybeResize(),this.writeBuffer(Buffer.from(new Wr.default(e).toArray("le",32)))}writeU512(e){this.maybeResize(),this.writeBuffer(Buffer.from(new Wr.default(e).toArray("le",64)))}writeBuffer(e){this.buf=Buffer.concat([Buffer.from(this.buf.subarray(0,this.length)),e,Buffer.alloc(Ba)]),this.length+=e.length}writeString(e){this.maybeResize();let t=Buffer.from(e,"utf8");this.writeU32(t.length),this.writeBuffer(t)}writeFixedArray(e){this.writeBuffer(Buffer.from(e))}writeArray(e,t){this.maybeResize(),this.writeU32(e.length);for(let n of e)this.maybeResize(),t(n)}toArray(){return this.buf.subarray(0,this.length)}};Pe.BinaryWriter=co;function Er(r,e,t){let n=t.value;t.value=function(...i){try{return n.apply(this,i)}catch(o){if(o instanceof RangeError){let s=o.code;if(["ERR_BUFFER_OUT_OF_BOUNDS","ERR_OUT_OF_RANGE"].indexOf(s)>=0)throw new Nt("Reached the end of buffer when deserializing")}throw o}}}var Vt=class{constructor(e){this.buf=e,this.offset=0}readU8(){let e=this.buf.readUInt8(this.offset);return this.offset+=1,e}readU16(){let e=this.buf.readUInt16LE(this.offset);return this.offset+=2,e}readU32(){let e=this.buf.readUInt32LE(this.offset);return this.offset+=4,e}readU64(){let e=this.readBuffer(8);return new Wr.default(e,"le")}readU128(){let e=this.readBuffer(16);return new Wr.default(e,"le")}readU256(){let e=this.readBuffer(32);return new Wr.default(e,"le")}readU512(){let e=this.readBuffer(64);return new Wr.default(e,"le")}readBuffer(e){if(this.offset+e>this.buf.length)throw new Nt(`Expected buffer length ${e} isn't within bounds`);let t=this.buf.slice(this.offset,this.offset+e);return this.offset+=e,t}readString(){let e=this.readU32(),t=this.readBuffer(e);try{return Xy.decode(t)}catch(n){throw new Nt(`Error decoding UTF-8 string: ${n}`)}}readFixedArray(e){return new Uint8Array(this.readBuffer(e))}readArray(e){let t=this.readU32(),n=Array();for(let i=0;i<t;++i)n.push(e());return n}};Rr([Er],Vt.prototype,"readU8",null);Rr([Er],Vt.prototype,"readU16",null);Rr([Er],Vt.prototype,"readU32",null);Rr([Er],Vt.prototype,"readU64",null);Rr([Er],Vt.prototype,"readU128",null);Rr([Er],Vt.prototype,"readU256",null);Rr([Er],Vt.prototype,"readU512",null);Rr([Er],Vt.prototype,"readString",null);Rr([Er],Vt.prototype,"readFixedArray",null);Rr([Er],Vt.prototype,"readArray",null);Pe.BinaryReader=Vt;function Ef(r){return r.charAt(0).toUpperCase()+r.slice(1)}function dn(r,e,t,n,i){try{if(typeof n=="string")i[`write${Ef(n)}`](t);else if(n instanceof Array)if(typeof n[0]=="number"){if(t.length!==n[0])throw new Nt(`Expecting byte array of length ${n[0]}, but got ${t.length} bytes`);i.writeFixedArray(t)}else if(n.length===2&&typeof n[1]=="number"){if(t.length!==n[1])throw new Nt(`Expecting byte array of length ${n[1]}, but got ${t.length} bytes`);for(let o=0;o<n[1];o++)dn(r,null,t[o],n[0],i)}else i.writeArray(t,o=>{dn(r,e,o,n[0],i)});else if(n.kind!==void 0)switch(n.kind){case"option":{t==null?i.writeU8(0):(i.writeU8(1),dn(r,e,t,n.type,i));break}case"map":{i.writeU32(t.size),t.forEach((o,s)=>{dn(r,e,s,n.key,i),dn(r,e,o,n.value,i)});break}default:throw new Nt(`FieldType ${n} unrecognized`)}else gf(r,t,i)}catch(o){throw o instanceof Nt&&o.addToFieldPath(e),o}}function gf(r,e,t){if(typeof e.borshSerialize=="function"){e.borshSerialize(t);return}let n=r.get(e.constructor);if(!n)throw new Nt(`Class ${e.constructor.name} is missing in schema`);if(n.kind==="struct")n.fields.map(([i,o])=>{dn(r,i,e[i],o,t)});else if(n.kind==="enum"){let i=e[n.field];for(let o=0;o<n.values.length;++o){let[s,u]=n.values[o];if(s===i){t.writeU8(o),dn(r,s,e[s],u,t);break}}}else throw new Nt(`Unexpected schema kind: ${n.kind} for ${e.constructor.name}`)}function Qy(r,e,t=co){let n=new t;return gf(r,e,n),n.toArray()}Pe.serialize=Qy;function hn(r,e,t,n){try{if(typeof t=="string")return n[`read${Ef(t)}`]();if(t instanceof Array){if(typeof t[0]=="number")return n.readFixedArray(t[0]);if(typeof t[1]=="number"){let i=[];for(let o=0;o<t[1];o++)i.push(hn(r,null,t[0],n));return i}else return n.readArray(()=>hn(r,e,t[0],n))}if(t.kind==="option")return n.readU8()?hn(r,e,t.type,n):void 0;if(t.kind==="map"){let i=new Map,o=n.readU32();for(let s=0;s<o;s++){let u=hn(r,e,t.key,n),l=hn(r,e,t.value,n);i.set(u,l)}return i}return Da(r,t,n)}catch(i){throw i instanceof Nt&&i.addToFieldPath(e),i}}function Da(r,e,t){if(typeof e.borshDeserialize=="function")return e.borshDeserialize(t);let n=r.get(e);if(!n)throw new Nt(`Class ${e.name} is missing in schema`);if(n.kind==="struct"){let i={};for(let[o,s]of r.get(e).fields)i[o]=hn(r,o,s,t);return new e(i)}if(n.kind==="enum"){let i=t.readU8();if(i>=n.values.length)throw new Nt(`Enum index: ${i} is out of range`);let[o,s]=n.values[i],u=hn(r,o,s,t);return new e({[o]:u})}throw new Nt(`Unexpected schema kind: ${n.kind} for ${e.constructor.name}`)}function eA(r,e,t,n=Vt){let i=new n(t),o=Da(r,e,i);if(i.offset<t.length)throw new Nt(`Unexpected ${t.length-i.offset} bytes after deserialized data`);return o}Pe.deserialize=eA;function tA(r,e,t,n=Vt){let i=new n(t);return Da(r,e,i)}Pe.deserializeUnchecked=tA});var Af=Re(P=>{"use strict";z();Object.defineProperty(P,"__esModule",{value:!0});P.s16=P.s8=P.nu64be=P.u48be=P.u40be=P.u32be=P.u24be=P.u16be=P.nu64=P.u48=P.u40=P.u32=P.u24=P.u16=P.u8=P.offset=P.greedy=P.Constant=P.UTF8=P.CString=P.Blob=P.Boolean=P.BitField=P.BitStructure=P.VariantLayout=P.Union=P.UnionLayoutDiscriminator=P.UnionDiscriminator=P.Structure=P.Sequence=P.DoubleBE=P.Double=P.FloatBE=P.Float=P.NearInt64BE=P.NearInt64=P.NearUInt64BE=P.NearUInt64=P.IntBE=P.Int=P.UIntBE=P.UInt=P.OffsetLayout=P.GreedyCount=P.ExternalLayout=P.bindConstructorLayout=P.nameWithProperty=P.Layout=P.uint8ArrayToBuffer=P.checkUint8Array=void 0;P.constant=P.utf8=P.cstr=P.blob=P.unionLayoutDiscriminator=P.union=P.seq=P.bits=P.struct=P.f64be=P.f64=P.f32be=P.f32=P.ns64be=P.s48be=P.s40be=P.s32be=P.s24be=P.s16be=P.ns64=P.s48=P.s40=P.s32=P.s24=void 0;var Ua=Tn();function Dn(r){if(!(r instanceof Uint8Array))throw new TypeError("b must be a Uint8Array")}P.checkUint8Array=Dn;function Ne(r){return Dn(r),Ua.Buffer.from(r.buffer,r.byteOffset,r.length)}P.uint8ArrayToBuffer=Ne;var xe=class{constructor(e,t){if(!Number.isInteger(e))throw new TypeError("span must be an integer");this.span=e,this.property=t}makeDestinationObject(){return{}}getSpan(e,t){if(0>this.span)throw new RangeError("indeterminate span");return this.span}replicate(e){let t=Object.create(this.constructor.prototype);return Object.assign(t,this),t.property=e,t}fromArray(e){}};P.Layout=xe;function Pa(r,e){return e.property?r+"["+e.property+"]":r}P.nameWithProperty=Pa;function rA(r,e){if(typeof r!="function")throw new TypeError("Class must be constructor");if(Object.prototype.hasOwnProperty.call(r,"layout_"))throw new Error("Class is already bound to a layout");if(!(e&&e instanceof xe))throw new TypeError("layout must be a Layout");if(Object.prototype.hasOwnProperty.call(e,"boundConstructor_"))throw new Error("layout is already bound to a constructor");r.layout_=e,e.boundConstructor_=r,e.makeDestinationObject=()=>new r,Object.defineProperty(r.prototype,"encode",{value(t,n){return e.encode(this,t,n)},writable:!0}),Object.defineProperty(r,"decode",{value(t,n){return e.decode(t,n)},writable:!0})}P.bindConstructorLayout=rA;var Bt=class extends xe{isCount(){throw new Error("ExternalLayout is abstract")}};P.ExternalLayout=Bt;var uo=class extends Bt{constructor(e=1,t){if(!Number.isInteger(e)||0>=e)throw new TypeError("elementSpan must be a (positive) integer");super(-1,t),this.elementSpan=e}isCount(){return!0}decode(e,t=0){Dn(e);let n=e.length-t;return Math.floor(n/this.elementSpan)}encode(e,t,n){return 0}};P.GreedyCount=uo;var ei=class extends Bt{constructor(e,t=0,n){if(!(e instanceof xe))throw new TypeError("layout must be a Layout");if(!Number.isInteger(t))throw new TypeError("offset must be integer or undefined");super(e.span,n||e.property),this.layout=e,this.offset=t}isCount(){return this.layout instanceof $t||this.layout instanceof tr}decode(e,t=0){return this.layout.decode(e,t+this.offset)}encode(e,t,n=0){return this.layout.encode(e,t,n+this.offset)}};P.OffsetLayout=ei;var $t=class extends xe{constructor(e,t){if(super(e,t),6<this.span)throw new RangeError("span must not exceed 6 bytes")}decode(e,t=0){return Ne(e).readUIntLE(t,this.span)}encode(e,t,n=0){return Ne(t).writeUIntLE(e,n,this.span),this.span}};P.UInt=$t;var tr=class extends xe{constructor(e,t){if(super(e,t),6<this.span)throw new RangeError("span must not exceed 6 bytes")}decode(e,t=0){return Ne(e).readUIntBE(t,this.span)}encode(e,t,n=0){return Ne(t).writeUIntBE(e,n,this.span),this.span}};P.UIntBE=tr;var Cr=class extends xe{constructor(e,t){if(super(e,t),6<this.span)throw new RangeError("span must not exceed 6 bytes")}decode(e,t=0){return Ne(e).readIntLE(t,this.span)}encode(e,t,n=0){return Ne(t).writeIntLE(e,n,this.span),this.span}};P.Int=Cr;var $r=class extends xe{constructor(e,t){if(super(e,t),6<this.span)throw new RangeError("span must not exceed 6 bytes")}decode(e,t=0){return Ne(e).readIntBE(t,this.span)}encode(e,t,n=0){return Ne(t).writeIntBE(e,n,this.span),this.span}};P.IntBE=$r;var Ma=Math.pow(2,32);function No(r){let e=Math.floor(r/Ma),t=r-e*Ma;return{hi32:e,lo32:t}}function To(r,e){return r*Ma+e}var lo=class extends xe{constructor(e){super(8,e)}decode(e,t=0){let n=Ne(e),i=n.readUInt32LE(t),o=n.readUInt32LE(t+4);return To(o,i)}encode(e,t,n=0){let i=No(e),o=Ne(t);return o.writeUInt32LE(i.lo32,n),o.writeUInt32LE(i.hi32,n+4),8}};P.NearUInt64=lo;var fo=class extends xe{constructor(e){super(8,e)}decode(e,t=0){let n=Ne(e),i=n.readUInt32BE(t),o=n.readUInt32BE(t+4);return To(i,o)}encode(e,t,n=0){let i=No(e),o=Ne(t);return o.writeUInt32BE(i.hi32,n),o.writeUInt32BE(i.lo32,n+4),8}};P.NearUInt64BE=fo;var ho=class extends xe{constructor(e){super(8,e)}decode(e,t=0){let n=Ne(e),i=n.readUInt32LE(t),o=n.readInt32LE(t+4);return To(o,i)}encode(e,t,n=0){let i=No(e),o=Ne(t);return o.writeUInt32LE(i.lo32,n),o.writeInt32LE(i.hi32,n+4),8}};P.NearInt64=ho;var _o=class extends xe{constructor(e){super(8,e)}decode(e,t=0){let n=Ne(e),i=n.readInt32BE(t),o=n.readUInt32BE(t+4);return To(i,o)}encode(e,t,n=0){let i=No(e),o=Ne(t);return o.writeInt32BE(i.hi32,n),o.writeUInt32BE(i.lo32,n+4),8}};P.NearInt64BE=_o;var po=class extends xe{constructor(e){super(4,e)}decode(e,t=0){return Ne(e).readFloatLE(t)}encode(e,t,n=0){return Ne(t).writeFloatLE(e,n),4}};P.Float=po;var Ro=class extends xe{constructor(e){super(4,e)}decode(e,t=0){return Ne(e).readFloatBE(t)}encode(e,t,n=0){return Ne(t).writeFloatBE(e,n),4}};P.FloatBE=Ro;var Eo=class extends xe{constructor(e){super(8,e)}decode(e,t=0){return Ne(e).readDoubleLE(t)}encode(e,t,n=0){return Ne(t).writeDoubleLE(e,n),8}};P.Double=Eo;var go=class extends xe{constructor(e){super(8,e)}decode(e,t=0){return Ne(e).readDoubleBE(t)}encode(e,t,n=0){return Ne(t).writeDoubleBE(e,n),8}};P.DoubleBE=go;var yo=class extends xe{constructor(e,t,n){if(!(e instanceof xe))throw new TypeError("elementLayout must be a Layout");if(!(t instanceof Bt&&t.isCount()||Number.isInteger(t)&&0<=t))throw new TypeError("count must be non-negative integer or an unsigned integer ExternalLayout");let i=-1;!(t instanceof Bt)&&0<e.span&&(i=t*e.span),super(i,n),this.elementLayout=e,this.count=t}getSpan(e,t=0){if(0<=this.span)return this.span;let n=0,i=this.count;if(i instanceof Bt&&(i=i.decode(e,t)),0<this.elementLayout.span)n=i*this.elementLayout.span;else{let o=0;for(;o<i;)n+=this.elementLayout.getSpan(e,t+n),++o}return n}decode(e,t=0){let n=[],i=0,o=this.count;for(o instanceof Bt&&(o=o.decode(e,t));i<o;)n.push(this.elementLayout.decode(e,t)),t+=this.elementLayout.getSpan(e,t),i+=1;return n}encode(e,t,n=0){let i=this.elementLayout,o=e.reduce((s,u)=>s+i.encode(u,t,n+s),0);return this.count instanceof Bt&&this.count.encode(e.length,t,n),o}};P.Sequence=yo;var Ao=class extends xe{constructor(e,t,n){if(!(Array.isArray(e)&&e.reduce((o,s)=>o&&s instanceof xe,!0)))throw new TypeError("fields must be array of Layout instances");typeof t=="boolean"&&n===void 0&&(n=t,t=void 0);for(let o of e)if(0>o.span&&o.property===void 0)throw new Error("fields cannot contain unnamed variable-length layout");let i=-1;try{i=e.reduce((o,s)=>o+s.getSpan(),0)}catch{}super(i,t),this.fields=e,this.decodePrefixes=!!n}getSpan(e,t=0){if(0<=this.span)return this.span;let n=0;try{n=this.fields.reduce((i,o)=>{let s=o.getSpan(e,t);return t+=s,i+s},0)}catch{throw new RangeError("indeterminate span")}return n}decode(e,t=0){Dn(e);let n=this.makeDestinationObject();for(let i of this.fields)if(i.property!==void 0&&(n[i.property]=i.decode(e,t)),t+=i.getSpan(e,t),this.decodePrefixes&&e.length===t)break;return n}encode(e,t,n=0){let i=n,o=0,s=0;for(let u of this.fields){let l=u.span;if(s=0<l?l:0,u.property!==void 0){let p=e[u.property];p!==void 0&&(s=u.encode(p,t,n),0>l&&(l=u.getSpan(t,n)))}o=n,n+=l}return o+s-i}fromArray(e){let t=this.makeDestinationObject();for(let n of this.fields)n.property!==void 0&&0<e.length&&(t[n.property]=e.shift());return t}layoutFor(e){if(typeof e!="string")throw new TypeError("property must be string");for(let t of this.fields)if(t.property===e)return t}offsetOf(e){if(typeof e!="string")throw new TypeError("property must be string");let t=0;for(let n of this.fields){if(n.property===e)return t;0>n.span?t=-1:0<=t&&(t+=n.span)}}};P.Structure=Ao;var ti=class{constructor(e){this.property=e}decode(e,t){throw new Error("UnionDiscriminator is abstract")}encode(e,t,n){throw new Error("UnionDiscriminator is abstract")}};P.UnionDiscriminator=ti;var Bn=class extends ti{constructor(e,t){if(!(e instanceof Bt&&e.isCount()))throw new TypeError("layout must be an unsigned integer ExternalLayout");super(t||e.property||"variant"),this.layout=e}decode(e,t){return this.layout.decode(e,t)}encode(e,t,n){return this.layout.encode(e,t,n)}};P.UnionLayoutDiscriminator=Bn;var ri=class extends xe{constructor(e,t,n){let i;if(e instanceof $t||e instanceof tr)i=new Bn(new ei(e));else if(e instanceof Bt&&e.isCount())i=new Bn(e);else if(e instanceof ti)i=e;else throw new TypeError("discr must be a UnionDiscriminator or an unsigned integer layout");if(t===void 0&&(t=null),!(t===null||t instanceof xe))throw new TypeError("defaultLayout must be null or a Layout");if(t!==null){if(0>t.span)throw new Error("defaultLayout must have constant span");t.property===void 0&&(t=t.replicate("content"))}let o=-1;t&&(o=t.span,0<=o&&(e instanceof $t||e instanceof tr)&&(o+=i.layout.span)),super(o,n),this.discriminator=i,this.usesPrefixDiscriminator=e instanceof $t||e instanceof tr,this.defaultLayout=t,this.registry={};let s=this.defaultGetSourceVariant.bind(this);this.getSourceVariant=function(u){return s(u)},this.configGetSourceVariant=function(u){s=u.bind(this)}}getSpan(e,t=0){if(0<=this.span)return this.span;let n=this.getVariant(e,t);if(!n)throw new Error("unable to determine span for unrecognized variant");return n.getSpan(e,t)}defaultGetSourceVariant(e){if(Object.prototype.hasOwnProperty.call(e,this.discriminator.property)){if(this.defaultLayout&&this.defaultLayout.property&&Object.prototype.hasOwnProperty.call(e,this.defaultLayout.property))return;let t=this.registry[e[this.discriminator.property]];if(t&&(!t.layout||t.property&&Object.prototype.hasOwnProperty.call(e,t.property)))return t}else for(let t in this.registry){let n=this.registry[t];if(n.property&&Object.prototype.hasOwnProperty.call(e,n.property))return n}throw new Error("unable to infer src variant")}decode(e,t=0){let n,i=this.discriminator,o=i.decode(e,t),s=this.registry[o];if(s===void 0){let u=this.defaultLayout,l=0;this.usesPrefixDiscriminator&&(l=i.layout.span),n=this.makeDestinationObject(),n[i.property]=o,n[u.property]=u.decode(e,t+l)}else n=s.decode(e,t);return n}encode(e,t,n=0){let i=this.getSourceVariant(e);if(i===void 0){let o=this.discriminator,s=this.defaultLayout,u=0;return this.usesPrefixDiscriminator&&(u=o.layout.span),o.encode(e[o.property],t,n),u+s.encode(e[s.property],t,n+u)}return i.encode(e,t,n)}addVariant(e,t,n){let i=new mo(this,e,t,n);return this.registry[e]=i,i}getVariant(e,t=0){let n;return e instanceof Uint8Array?n=this.discriminator.decode(e,t):n=e,this.registry[n]}};P.Union=ri;var mo=class extends xe{constructor(e,t,n,i){if(!(e instanceof ri))throw new TypeError("union must be a Union");if(!Number.isInteger(t)||0>t)throw new TypeError("variant must be a (non-negative) integer");if(typeof n=="string"&&i===void 0&&(i=n,n=null),n){if(!(n instanceof xe))throw new TypeError("layout must be a Layout");if(e.defaultLayout!==null&&0<=n.span&&n.span>e.defaultLayout.span)throw new Error("variant span exceeds span of containing union");if(typeof i!="string")throw new TypeError("variant must have a String property")}let o=e.span;0>e.span&&(o=n?n.span:0,0<=o&&e.usesPrefixDiscriminator&&(o+=e.discriminator.layout.span)),super(o,i),this.union=e,this.variant=t,this.layout=n||null}getSpan(e,t=0){if(0<=this.span)return this.span;let n=0;this.union.usesPrefixDiscriminator&&(n=this.union.discriminator.layout.span);let i=0;return this.layout&&(i=this.layout.getSpan(e,t+n)),n+i}decode(e,t=0){let n=this.makeDestinationObject();if(this!==this.union.getVariant(e,t))throw new Error("variant mismatch");let i=0;return this.union.usesPrefixDiscriminator&&(i=this.union.discriminator.layout.span),this.layout?n[this.property]=this.layout.decode(e,t+i):this.property?n[this.property]=!0:this.union.usesPrefixDiscriminator&&(n[this.union.discriminator.property]=this.variant),n}encode(e,t,n=0){let i=0;if(this.union.usesPrefixDiscriminator&&(i=this.union.discriminator.layout.span),this.layout&&!Object.prototype.hasOwnProperty.call(e,this.property))throw new TypeError("variant lacks property "+this.property);this.union.discriminator.encode(this.variant,t,n);let o=i;if(this.layout&&(this.layout.encode(e[this.property],t,n+i),o+=this.layout.getSpan(t,n+i),0<=this.union.span&&o>this.union.span))throw new Error("encoded variant overruns containing union");return o}fromArray(e){if(this.layout)return this.layout.fromArray(e)}};P.VariantLayout=mo;function kn(r){return 0>r&&(r+=4294967296),r}var ni=class extends xe{constructor(e,t,n){if(!(e instanceof $t||e instanceof tr))throw new TypeError("word must be a UInt or UIntBE layout");if(typeof t=="string"&&n===void 0&&(n=t,t=!1),4<e.span)throw new RangeError("word cannot exceed 32 bits");super(e.span,n),this.word=e,this.msb=!!t,this.fields=[];let i=0;this._packedSetValue=function(o){return i=kn(o),this},this._packedGetValue=function(){return i}}decode(e,t=0){let n=this.makeDestinationObject(),i=this.word.decode(e,t);this._packedSetValue(i);for(let o of this.fields)o.property!==void 0&&(n[o.property]=o.decode(e));return n}encode(e,t,n=0){let i=this.word.decode(t,n);this._packedSetValue(i);for(let o of this.fields)if(o.property!==void 0){let s=e[o.property];s!==void 0&&o.encode(s)}return this.word.encode(this._packedGetValue(),t,n)}addField(e,t){let n=new ii(this,e,t);return this.fields.push(n),n}addBoolean(e){let t=new So(this,e);return this.fields.push(t),t}fieldFor(e){if(typeof e!="string")throw new TypeError("property must be string");for(let t of this.fields)if(t.property===e)return t}};P.BitStructure=ni;var ii=class{constructor(e,t,n){if(!(e instanceof ni))throw new TypeError("container must be a BitStructure");if(!Number.isInteger(t)||0>=t)throw new TypeError("bits must be positive integer");let i=8*e.span,o=e.fields.reduce((s,u)=>s+u.bits,0);if(t+o>i)throw new Error("bits too long for span remainder ("+(i-o)+" of "+i+" remain)");this.container=e,this.bits=t,this.valueMask=(1<<t)-1,t===32&&(this.valueMask=4294967295),this.start=o,this.container.msb&&(this.start=i-o-t),this.wordMask=kn(this.valueMask<<this.start),this.property=n}decode(e,t){let n=this.container._packedGetValue();return kn(n&this.wordMask)>>>this.start}encode(e){if(typeof e!="number"||!Number.isInteger(e)||e!==kn(e&this.valueMask))throw new TypeError(Pa("BitField.encode",this)+" value must be integer not exceeding "+this.valueMask);let t=this.container._packedGetValue(),n=kn(e<<this.start);this.container._packedSetValue(kn(t&~this.wordMask)|n)}};P.BitField=ii;var So=class extends ii{constructor(e,t){super(e,1,t)}decode(e,t){return!!super.decode(e,t)}encode(e){typeof e=="boolean"&&(e=+e),super.encode(e)}};P.Boolean=So;var bo=class extends xe{constructor(e,t){if(!(e instanceof Bt&&e.isCount()||Number.isInteger(e)&&0<=e))throw new TypeError("length must be positive integer or an unsigned integer ExternalLayout");let n=-1;e instanceof Bt||(n=e),super(n,t),this.length=e}getSpan(e,t){let n=this.span;return 0>n&&(n=this.length.decode(e,t)),n}decode(e,t=0){let n=this.span;return 0>n&&(n=this.length.decode(e,t)),Ne(e).slice(t,t+n)}encode(e,t,n){let i=this.length;if(this.length instanceof Bt&&(i=e.length),!(e instanceof Uint8Array&&i===e.length))throw new TypeError(Pa("Blob.encode",this)+" requires (length "+i+") Uint8Array as src");if(n+i>t.length)throw new RangeError("encoding overruns Uint8Array");let o=Ne(e);return Ne(t).write(o.toString("hex"),n,i,"hex"),this.length instanceof Bt&&this.length.encode(i,t,n),i}};P.Blob=bo;var Oo=class extends xe{constructor(e){super(-1,e)}getSpan(e,t=0){Dn(e);let n=t;for(;n<e.length&&e[n]!==0;)n+=1;return 1+n-t}decode(e,t=0){let n=this.getSpan(e,t);return Ne(e).slice(t,t+n-1).toString("utf-8")}encode(e,t,n=0){typeof e!="string"&&(e=String(e));let i=Ua.Buffer.from(e,"utf8"),o=i.length;if(n+o>t.length)throw new RangeError("encoding overruns Buffer");let s=Ne(t);return i.copy(s,n),s[n+o]=0,o+1}};P.CString=Oo;var wo=class extends xe{constructor(e,t){if(typeof e=="string"&&t===void 0&&(t=e,e=void 0),e===void 0)e=-1;else if(!Number.isInteger(e))throw new TypeError("maxSpan must be an integer");super(-1,t),this.maxSpan=e}getSpan(e,t=0){return Dn(e),e.length-t}decode(e,t=0){let n=this.getSpan(e,t);if(0<=this.maxSpan&&this.maxSpan<n)throw new RangeError("text length exceeds maxSpan");return Ne(e).slice(t,t+n).toString("utf-8")}encode(e,t,n=0){typeof e!="string"&&(e=String(e));let i=Ua.Buffer.from(e,"utf8"),o=i.length;if(0<=this.maxSpan&&this.maxSpan<o)throw new RangeError("text length exceeds maxSpan");if(n+o>t.length)throw new RangeError("encoding overruns Buffer");return i.copy(Ne(t),n),o}};P.UTF8=wo;var Io=class extends xe{constructor(e,t){super(0,t),this.value=e}decode(e,t){return this.value}encode(e,t,n){return 0}};P.Constant=Io;P.greedy=(r,e)=>new uo(r,e);P.offset=(r,e,t)=>new ei(r,e,t);P.u8=r=>new $t(1,r);P.u16=r=>new $t(2,r);P.u24=r=>new $t(3,r);P.u32=r=>new $t(4,r);P.u40=r=>new $t(5,r);P.u48=r=>new $t(6,r);P.nu64=r=>new lo(r);P.u16be=r=>new tr(2,r);P.u24be=r=>new tr(3,r);P.u32be=r=>new tr(4,r);P.u40be=r=>new tr(5,r);P.u48be=r=>new tr(6,r);P.nu64be=r=>new fo(r);P.s8=r=>new Cr(1,r);P.s16=r=>new Cr(2,r);P.s24=r=>new Cr(3,r);P.s32=r=>new Cr(4,r);P.s40=r=>new Cr(5,r);P.s48=r=>new Cr(6,r);P.ns64=r=>new ho(r);P.s16be=r=>new $r(2,r);P.s24be=r=>new $r(3,r);P.s32be=r=>new $r(4,r);P.s40be=r=>new $r(5,r);P.s48be=r=>new $r(6,r);P.ns64be=r=>new _o(r);P.f32=r=>new po(r);P.f32be=r=>new Ro(r);P.f64=r=>new Eo(r);P.f64be=r=>new go(r);P.struct=(r,e,t)=>new Ao(r,e,t);P.bits=(r,e,t)=>new ni(r,e,t);P.seq=(r,e,t)=>new yo(r,e,t);P.union=(r,e,t)=>new ri(r,e,t);P.unionLayoutDiscriminator=(r,e)=>new Bn(r,e);P.blob=(r,e)=>new bo(r,e);P.cstr=r=>new Oo(r);P.utf8=(r,e)=>new wo(r,e);P.constant=(r,e)=>new Io(r,e)});var lc=Re(T=>{"use strict";z();var mf=1,Sf=2,bf=3,Of=4,wf=5,If=6,Nf=7,Tf=8,vf=9,Fa=10,za=-32700,Va=-32603,Ga=-32602,Ha=-32601,Ka=-32600,Cf=-32016,qa=-32015,Wa=-32014,xf=-32013,$a=-32012,Lf=-32011,Ya=-32010,ja=-32009,kf=-32008,Xa=-32007,Za=-32006,Bf=-32005,Ja=-32004,Df=-32003,vo=-32002,Qa=-32001,Mf=28e5,Uf=2800001,Pf=2800002,Ff=2800003,zf=2800004,Vf=2800005,Gf=2800006,Hf=2800007,Kf=2800008,qf=2800009,Wf=2800010,$f=323e4,Yf=32300001,jf=3230002,Xf=3230003,Zf=3230004,Jf=361e4,Qf=3610001,ed=3610002,td=3610003,rd=3610004,nd=3610005,id=3610006,od=3610007,sd=3611e3,ad=3704e3,cd=3704001,ud=3704002,ld=3704003,fd=3704004,dd=4128e3,hd=4128001,_d=4128002,ec=4615e3,pd=4615001,Rd=4615002,Ed=4615003,gd=4615004,yd=4615005,Ad=4615006,md=4615007,Sd=4615008,bd=4615009,Od=4615010,wd=4615011,Id=4615012,Nd=4615013,Td=4615014,vd=4615015,Cd=4615016,xd=4615017,Ld=4615018,kd=4615019,Bd=4615020,Dd=4615021,Md=4615022,Ud=4615023,Pd=4615024,Fd=4615025,tc=4615026,zd=4615027,Vd=4615028,Gd=4615029,Hd=4615030,Kd=4615031,qd=4615032,Wd=4615033,$d=4615034,Yd=4615035,jd=4615036,Xd=4615037,Zd=4615038,Jd=4615039,Qd=4615040,eh=4615041,th=4615042,rh=4615043,nh=4615044,rc=4615045,ih=4615046,oh=4615047,sh=4615048,ah=4615049,ch=4615050,uh=4615051,lh=4615052,fh=4615053,dh=4615054,hh=5508e3,_h=5508001,ph=5508002,Rh=5508003,Eh=5508004,gh=5508005,yh=5508006,Ah=5508007,mh=5508008,Sh=5508009,bh=5508010,Oh=5508011,wh=5663e3,Ih=5663001,Nh=5663002,Th=5663003,vh=5663004,Ch=5663005,xh=5663006,Lh=5663007,kh=5663008,Bh=5663009,Dh=5663010,Mh=5663011,Uh=5663012,Ph=5663013,Fh=5663014,zh=5663015,Vh=5663016,Gh=5663017,Hh=5663018,Kh=5663019,nc=705e4,qh=7050001,Wh=7050002,$h=7050003,Yh=7050004,jh=7050005,Xh=7050006,Zh=7050007,Jh=7050008,Qh=7050009,e0=7050010,t0=7050011,r0=7050012,n0=7050013,i0=7050014,o0=7050015,s0=7050016,a0=7050017,c0=7050018,u0=7050019,l0=7050020,f0=7050021,d0=7050022,h0=7050023,_0=7050024,p0=7050025,R0=7050026,E0=7050027,g0=7050028,y0=7050029,ic=7050030,oc=7050031,A0=7050032,m0=7050033,S0=7050034,sc=7050035,b0=7050036,O0=8078e3,w0=8078001,I0=8078002,N0=8078003,T0=8078004,v0=8078005,C0=8078006,x0=8078007,L0=8078008,k0=8078009,B0=8078010,D0=8078011,M0=8078012,U0=8078013,P0=8078014,F0=8078015,z0=8078016,V0=8078017,G0=8078018,H0=8078019,K0=8078020,q0=8078021,W0=8078022,$0=81e5,Y0=8100001,j0=8100002,X0=8100003,Z0=819e4,J0=8190001,Q0=8190002,e_=8190003,t_=8190004,r_=99e5,n_=9900001,i_=9900002,o_=9900003,s_=9900004;function a_(r){return Array.isArray(r)?"%5B"+r.map(a_).join("%2C%20")+"%5D":typeof r=="bigint"?`${r}n`:encodeURIComponent(String(r!=null&&Object.getPrototypeOf(r)===null?{...r}:r))}function nA([r,e]){return`${r}=${a_(e)}`}function iA(r){let e=Object.entries(r).map(nA).join("&");return btoa(e)}var Zb={[$f]:"Account not found at address: $address",[Zf]:"Not all accounts were decoded. Encoded accounts found at addresses: $addresses.",[Xf]:"Expected decoded account at address: $address",[jf]:"Failed to decode account data at address: $address",[Yf]:"Accounts not found at addresses: $addresses",[qf]:"Unable to find a viable program address bump seed.",[Pf]:"$putativeAddress is not a base58-encoded address.",[Mf]:"Expected base58 encoded address to decode to a byte array of length 32. Actual length: $actualLength.",[Ff]:"The `CryptoKey` must be an `Ed25519` public key.",[Kf]:"Invalid seeds; point must fall off the Ed25519 curve.",[zf]:"Expected given program derived address to have the following format: [Address, ProgramDerivedAddressBump].",[Gf]:"A maximum of $maxSeeds seeds, including the bump seed, may be supplied when creating an address. Received: $actual.",[Hf]:"The seed at index $index with length $actual exceeds the maximum length of $maxSeedLength bytes.",[Vf]:"Expected program derived address bump to be in the range [0, 255], got: $bump.",[Wf]:"Program address cannot end with PDA marker.",[Uf]:"Expected base58-encoded address string of length in the range [32, 44]. Actual length: $actualLength.",[Of]:"Expected base58-encoded blockash string of length in the range [32, 44]. Actual length: $actualLength.",[mf]:"The network has progressed past the last block for which this transaction could have been committed.",[O0]:"Codec [$codecDescription] cannot decode empty byte arrays.",[W0]:"Enum codec cannot use lexical values [$stringValues] as discriminators. Either remove all lexical values or set `useValuesAsDiscriminators` to `false`.",[K0]:"Sentinel [$hexSentinel] must not be present in encoded bytes [$hexEncodedBytes].",[v0]:"Encoder and decoder must have the same fixed size, got [$encoderFixedSize] and [$decoderFixedSize].",[C0]:"Encoder and decoder must have the same max size, got [$encoderMaxSize] and [$decoderMaxSize].",[T0]:"Encoder and decoder must either both be fixed-size or variable-size.",[L0]:"Enum discriminator out of range. Expected a number in [$formattedValidDiscriminators], got $discriminator.",[I0]:"Expected a fixed-size codec, got a variable-size one.",[U0]:"Codec [$codecDescription] expected a positive byte length, got $bytesLength.",[N0]:"Expected a variable-size codec, got a fixed-size one.",[H0]:"Codec [$codecDescription] expected zero-value [$hexZeroValue] to have the same size as the provided fixed-size item [$expectedSize bytes].",[w0]:"Codec [$codecDescription] expected $expected bytes, got $bytesLength.",[G0]:"Expected byte array constant [$hexConstant] to be present in data [$hexData] at offset [$offset].",[k0]:"Invalid discriminated union variant. Expected one of [$variants], got $value.",[B0]:"Invalid enum variant. Expected one of [$stringValues] or a number in [$formattedNumericalValues], got $variant.",[F0]:"Invalid literal union variant. Expected one of [$variants], got $value.",[x0]:"Expected [$codecDescription] to have $expected items, got $actual.",[M0]:"Invalid value $value for base $base with alphabet $alphabet.",[z0]:"Literal union discriminator out of range. Expected a number between $minRange and $maxRange, got $discriminator.",[D0]:"Codec [$codecDescription] expected number to be in the range [$min, $max], got $value.",[P0]:"Codec [$codecDescription] expected offset to be in the range [0, $bytesLength], got $offset.",[q0]:"Expected sentinel [$hexSentinel] to be present in decoded bytes [$hexDecodedBytes].",[V0]:"Union variant out of range. Expected an index between $minRange and $maxRange, got $variant.",[sd]:"No random values implementation could be found.",[bd]:"instruction requires an uninitialized account",[Ud]:"instruction tries to borrow reference for an account which is already borrowed",[Pd]:"instruction left account with an outstanding borrowed reference",[Dd]:"program other than the account's owner changed the size of the account data",[yd]:"account data too small for instruction",[Md]:"instruction expected an executable account",[ih]:"An account does not have enough lamports to be rent-exempt",[sh]:"Program arithmetic overflowed",[rc]:"Failed to serialize or deserialize account data: $encodedData",[dh]:"Builtin programs must consume compute units",[qd]:"Cross-program invocation call depth too deep",[Zd]:"Computational budget exceeded",[tc]:"custom program error: #$code",[xd]:"instruction contains duplicate accounts",[Fd]:"instruction modifications of multiply-passed account differ",[Hd]:"executable accounts must be rent exempt",[Vd]:"instruction changed executable accounts data",[Gd]:"instruction changed the balance of an executable account",[Ld]:"instruction changed executable bit of an account",[Td]:"instruction modified data of an account it does not own",[Nd]:"instruction spent from the balance of an account it does not own",[pd]:"generic instruction error",[ch]:"Provided owner is not allowed",[rh]:"Account is immutable",[nh]:"Incorrect authority provided",[md]:"incorrect program id for instruction",[Ad]:"insufficient funds for instruction",[gd]:"invalid account data for instruction",[oh]:"Invalid account owner",[Rd]:"invalid program argument",[zd]:"program returned invalid error code",[Ed]:"invalid instruction data",[Xd]:"Failed to reallocate account data",[jd]:"Provided seeds do not result in a valid address",[uh]:"Accounts data allocations exceeded the maximum allowed per transaction",[lh]:"Max accounts exceeded",[fh]:"Max instruction trace length exceeded",[Yd]:"Length of the seed is too long for address generation",[Wd]:"An account required by the instruction is missing",[Sd]:"missing required signature for instruction",[Id]:"instruction illegally modified the program id of an account",[Bd]:"insufficient account keys for instruction",[Jd]:"Cross-program invocation with unauthorized signer or writable account",[Qd]:"Failed to create program execution environment",[th]:"Program failed to compile",[eh]:"Program failed to complete",[Cd]:"instruction modified data of a read-only account",[vd]:"instruction changed the balance of a read-only account",[$d]:"Cross-program invocation reentrancy not allowed for this instruction",[kd]:"instruction modified rent epoch of an account",[wd]:"sum of account balances before and after instruction do not match",[Od]:"instruction requires an initialized account",[ec]:"",[Kd]:"Unsupported program id",[ah]:"Unsupported sysvar",[dd]:"The instruction does not have any accounts.",[hd]:"The instruction does not have any data.",[_d]:"Expected instruction to have progress address $expectedProgramAddress, got $actualProgramAddress.",[wf]:"Expected base58 encoded blockhash to decode to a byte array of length 32. Actual length: $actualLength.",[Sf]:"The nonce `$expectedNonceValue` is no longer valid. It has advanced to `$actualNonceValue`",[i_]:"Invariant violation: Found no abortable iterable cache entry for key `$cacheKey`. It should be impossible to hit this error; please file an issue at https://sola.na/web3invariant",[s_]:"Invariant violation: This data publisher does not publish to the channel named `$channelName`. Supported channels include $supportedChannelNames.",[n_]:"Invariant violation: WebSocket message iterator state is corrupt; iterated without first resolving existing message promise. It should be impossible to hit this error; please file an issue at https://sola.na/web3invariant",[r_]:"Invariant violation: WebSocket message iterator is missing state storage. It should be impossible to hit this error; please file an issue at https://sola.na/web3invariant",[o_]:"Invariant violation: Switch statement non-exhaustive. Received unexpected value `$unexpectedValue`. It should be impossible to hit this error; please file an issue at https://sola.na/web3invariant",[Va]:"JSON-RPC error: Internal JSON-RPC error ($__serverMessage)",[Ga]:"JSON-RPC error: Invalid method parameter(s) ($__serverMessage)",[Ka]:"JSON-RPC error: The JSON sent is not a valid `Request` object ($__serverMessage)",[Ha]:"JSON-RPC error: The method does not exist / is not available ($__serverMessage)",[za]:"JSON-RPC error: An error occurred on the server while parsing the JSON text ($__serverMessage)",[$a]:"$__serverMessage",[Qa]:"$__serverMessage",[Ja]:"$__serverMessage",[Wa]:"$__serverMessage",[Ya]:"$__serverMessage",[ja]:"$__serverMessage",[Cf]:"Minimum context slot has not been reached",[Bf]:"Node is unhealthy; behind by $numSlotsBehind slots",[kf]:"No snapshot",[vo]:"Transaction simulation failed",[Xa]:"$__serverMessage",[Lf]:"Transaction history is not available from this node",[Za]:"$__serverMessage",[xf]:"Transaction signature length mismatch",[Df]:"Transaction signature verification failure",[qa]:"$__serverMessage",[ad]:"Key pair bytes must be of length 64, got $byteLength.",[cd]:"Expected private key bytes with length 32. Actual length: $actualLength.",[ud]:"Expected base58-encoded signature to decode to a byte array of length 64. Actual length: $actualLength.",[fd]:"The provided private key does not match the provided public key.",[ld]:"Expected base58-encoded signature string of length in the range [64, 88]. Actual length: $actualLength.",[If]:"Lamports value must be in the range [0, 2e64-1]",[Nf]:"`$value` cannot be parsed as a `BigInt`",[Fa]:"$message",[Tf]:"`$value` cannot be parsed as a `Number`",[bf]:"No nonce account could be found at address `$nonceAccountAddress`",[Z0]:"The notification name must end in 'Notifications' and the API must supply a subscription plan creator function for the notification '$notificationName'.",[Q0]:"WebSocket was closed before payload could be added to the send buffer",[e_]:"WebSocket connection closed",[t_]:"WebSocket failed to connect",[J0]:"Failed to obtain a subscription id from the server",[X0]:"Could not find an API plan for RPC method: `$method`",[$0]:"The $argumentLabel argument to the `$methodName` RPC method$optionalPathLabel was `$value`. This number is unsafe for use with the Solana JSON-RPC because it exceeds `Number.MAX_SAFE_INTEGER`.",[j0]:"HTTP error ($statusCode): $message",[Y0]:"HTTP header(s) forbidden: $headers. Learn more at https://developer.mozilla.org/en-US/docs/Glossary/Forbidden_header_name.",[hh]:"Multiple distinct signers were identified for address `$address`. Please ensure that you are using the same signer instance for each address.",[_h]:"The provided value does not implement the `KeyPairSigner` interface",[Rh]:"The provided value does not implement the `MessageModifyingSigner` interface",[Eh]:"The provided value does not implement the `MessagePartialSigner` interface",[ph]:"The provided value does not implement any of the `MessageSigner` interfaces",[yh]:"The provided value does not implement the `TransactionModifyingSigner` interface",[Ah]:"The provided value does not implement the `TransactionPartialSigner` interface",[mh]:"The provided value does not implement the `TransactionSendingSigner` interface",[gh]:"The provided value does not implement any of the `TransactionSigner` interfaces",[Sh]:"More than one `TransactionSendingSigner` was identified.",[bh]:"No `TransactionSendingSigner` was identified. Please provide a valid `ITransactionWithSingleSendingSigner` transaction.",[Oh]:"Wallet account signers do not support signing multiple messages/transactions in a single operation",[od]:"Cannot export a non-extractable key.",[Qf]:"No digest implementation could be found.",[Jf]:"Cryptographic operations are only allowed in secure browser contexts. Read more here: https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts.",[ed]:`This runtime does not support the generation of Ed25519 key pairs.

Install @solana/webcrypto-ed25519-polyfill and call its \`install\` function before generating keys in environments that do not support Ed25519.

For a list of runtimes that currently support Ed25519 operations, visit https://github.com/WICG/webcrypto-secure-curves/issues/20.`,[td]:"No signature verification implementation could be found.",[rd]:"No key generation implementation could be found.",[nd]:"No signing implementation could be found.",[id]:"No key export implementation could be found.",[vf]:"Timestamp value must be in the range [-(2n ** 63n), (2n ** 63n) - 1]. `$value` given",[s0]:"Transaction processing left an account with an outstanding borrowed reference",[qh]:"Account in use",[Wh]:"Account loaded twice",[$h]:"Attempt to debit an account but found no record of a prior credit.",[h0]:"Transaction loads an address table account that doesn't exist",[Zh]:"This transaction has already been processed",[Jh]:"Blockhash not found",[Qh]:"Loader call chain is too deep",[o0]:"Transactions are currently disabled due to cluster maintenance",[ic]:"Transaction contains a duplicate instruction ($index) that is not allowed",[jh]:"Insufficient funds for fee",[oc]:"Transaction results in an account ($accountIndex) with insufficient funds for rent",[Xh]:"This account may not be used to pay transaction fees",[t0]:"Transaction contains an invalid account reference",[p0]:"Transaction loads an address table account with invalid data",[R0]:"Transaction address table lookup uses an invalid index",[_0]:"Transaction loads an address table account with an invalid owner",[m0]:"LoadedAccountsDataSizeLimit set for transaction must be greater than 0.",[n0]:"This program may not be used for executing instructions",[E0]:"Transaction leaves an account with a lower balance than rent-exempt minimum",[u0]:"Transaction loads a writable account that cannot be written",[A0]:"Transaction exceeded max loaded accounts data size cap",[e0]:"Transaction requires a fee but has no signature present",[Yh]:"Attempt to load a program that does not exist",[sc]:"Execution of the program referenced by account at index $accountIndex is temporarily restricted.",[S0]:"ResanitizationNeeded",[i0]:"Transaction failed to sanitize accounts offsets correctly",[r0]:"Transaction did not pass signature verification",[d0]:"Transaction locked too many accounts",[b0]:"Sum of account balances before and after transaction do not match",[nc]:"The transaction failed with the error `$errorName`",[c0]:"Transaction version is unsupported",[f0]:"Transaction would exceed account data limit within the block",[y0]:"Transaction would exceed total account data limit",[l0]:"Transaction would exceed max account limit within the block",[a0]:"Transaction would exceed max Block Cost Limit",[g0]:"Transaction would exceed max Vote Cost Limit",[zh]:"Attempted to sign a transaction with an address that is not a signer for it",[Dh]:"Transaction is missing an address at index: $index.",[Vh]:"Transaction has no expected signers therefore it cannot be encoded",[Nh]:"Transaction does not have a blockhash lifetime",[Th]:"Transaction is not a durable nonce transaction",[Ch]:"Contents of these address lookup tables unknown: $lookupTableAddresses",[xh]:"Lookup of address at index $highestRequestedIndex failed for lookup table `$lookupTableAddress`. Highest known index is $highestKnownIndex. The lookup table may have been extended since its contents were retrieved",[kh]:"No fee payer set in CompiledTransaction",[Lh]:"Could not find program address at index $index",[Hh]:"Failed to estimate the compute unit consumption for this transaction message. This is likely because simulating the transaction failed. Inspect the `cause` property of this error to learn more",[Kh]:"Transaction failed when it was simulated in order to estimate the compute unit consumption. The compute unit estimate provided is for a transaction that failed when simulated and may not be representative of the compute units this transaction would consume if successful. Inspect the `cause` property of this error to learn more",[Mh]:"Transaction is missing a fee payer.",[Uh]:"Could not determine this transaction's signature. Make sure that the transaction has been signed by its fee payer.",[Fh]:"Transaction first instruction is not advance nonce account instruction.",[Ph]:"Transaction with no instructions cannot be durable nonce transaction.",[wh]:"This transaction includes an address (`$programAddress`) which is both invoked and set as the fee payer. Program addresses may not pay fees",[Ih]:"This transaction includes an address (`$programAddress`) which is both invoked and marked writable. Program addresses may not be writable",[Gh]:"The transaction message expected the transaction to have $signerAddressesLength signatures, got $signaturesLength.",[Bh]:"Transaction is missing signatures for addresses: $addresses.",[vh]:"Transaction version must be in the range [0, 127]. `$actualVersion` given"};function oA(r,e={}){{let t=`Solana error #${r}; Decode this error by running \`npx @solana/errors decode -- ${r}`;return Object.keys(e).length&&(t+=` '${iA(e)}'`),`${t}\``}}function sA(r,e){return r instanceof Error&&r.name==="SolanaError"?e!==void 0?r.context.__code===e:!0:!1}var oi=class extends Error{cause=this.cause;context;constructor(...[r,e]){let t,n;if(e){let{cause:o,...s}=e;o&&(n={cause:o}),Object.keys(s).length>0&&(t=s)}let i=oA(r,t);super(i,n),this.context={__code:r,...t},this.name="SolanaError"}};function ac(...r){"captureStackTrace"in Error&&typeof Error.captureStackTrace=="function"&&Error.captureStackTrace(...r)}function c_({errorCodeBaseOffset:r,getErrorContext:e,orderedErrorNames:t,rpcEnumError:n},i){let o,s;typeof n=="string"?o=n:(o=Object.keys(n)[0],s=n[o]);let u=t.indexOf(o),l=r+u,p=e(l,o,s),R=new oi(l,p);return ac(R,i),R}var aA=["GenericError","InvalidArgument","InvalidInstructionData","InvalidAccountData","AccountDataTooSmall","InsufficientFunds","IncorrectProgramId","MissingRequiredSignature","AccountAlreadyInitialized","UninitializedAccount","UnbalancedInstruction","ModifiedProgramId","ExternalAccountLamportSpend","ExternalAccountDataModified","ReadonlyLamportChange","ReadonlyDataModified","DuplicateAccountIndex","ExecutableModified","RentEpochModified","NotEnoughAccountKeys","AccountDataSizeChanged","AccountNotExecutable","AccountBorrowFailed","AccountBorrowOutstanding","DuplicateAccountOutOfSync","Custom","InvalidError","ExecutableDataModified","ExecutableLamportChange","ExecutableAccountNotRentExempt","UnsupportedProgramId","CallDepth","MissingAccount","ReentrancyNotAllowed","MaxSeedLengthExceeded","InvalidSeeds","InvalidRealloc","ComputationalBudgetExceeded","PrivilegeEscalation","ProgramEnvironmentSetupFailure","ProgramFailedToComplete","ProgramFailedToCompile","Immutable","IncorrectAuthority","BorshIoError","AccountNotRentExempt","InvalidAccountOwner","ArithmeticOverflow","UnsupportedSysvar","IllegalOwner","MaxAccountsDataAllocationsExceeded","MaxAccountsExceeded","MaxInstructionTraceLengthExceeded","BuiltinProgramsMustConsumeComputeUnits"];function cc(r,e){let t=Number(r);return c_({errorCodeBaseOffset:4615001,getErrorContext(n,i,o){return n===ec?{errorName:i,index:t,...o!==void 0?{instructionErrorContext:o}:null}:n===tc?{code:Number(o),index:t}:n===rc?{encodedData:o,index:t}:{index:t}},orderedErrorNames:aA,rpcEnumError:e},cc)}var cA=["AccountInUse","AccountLoadedTwice","AccountNotFound","ProgramAccountNotFound","InsufficientFundsForFee","InvalidAccountForFee","AlreadyProcessed","BlockhashNotFound","CallChainTooDeep","MissingSignatureForFee","InvalidAccountIndex","SignatureFailure","InvalidProgramForExecution","SanitizeFailure","ClusterMaintenance","AccountBorrowOutstanding","WouldExceedMaxBlockCostLimit","UnsupportedVersion","InvalidWritableAccount","WouldExceedMaxAccountCostLimit","WouldExceedAccountDataBlockLimit","TooManyAccountLocks","AddressLookupTableNotFound","InvalidAddressLookupTableOwner","InvalidAddressLookupTableData","InvalidAddressLookupTableIndex","InvalidRentPayingAccount","WouldExceedMaxVoteCostLimit","WouldExceedAccountDataTotalLimit","DuplicateInstruction","InsufficientFundsForRent","MaxLoadedAccountsDataSizeExceeded","InvalidLoadedAccountsDataSizeLimit","ResanitizationNeeded","ProgramExecutionTemporarilyRestricted","UnbalancedTransaction"];function uc(r){return typeof r=="object"&&"InstructionError"in r?cc(...r.InstructionError):c_({errorCodeBaseOffset:7050001,getErrorContext(e,t,n){if(e===nc)return{errorName:t,...n!==void 0?{transactionErrorContext:n}:null};if(e===ic)return{index:Number(n)};if(e===oc||e===sc)return{accountIndex:Number(n.account_index)}},orderedErrorNames:cA,rpcEnumError:r},uc)}function u_(r){let e;if(uA(r)){let{code:t,data:n,message:i}=r,o=Number(t);if(o===vo){let{err:s,...u}=n,l=s?{cause:uc(s)}:null;e=new oi(vo,{...u,...l})}else{let s;switch(o){case Va:case Ga:case Ka:case Ha:case za:case $a:case Qa:case Ja:case Wa:case Ya:case ja:case Xa:case Za:case qa:s={__serverMessage:i};break;default:typeof n=="object"&&!Array.isArray(n)&&(s=n)}e=new oi(o,s)}}else{let t=typeof r=="object"&&r!==null&&"message"in r&&typeof r.message=="string"?r.message:"Malformed JSON-RPC error with no message attribute";e=new oi(Fa,{error:r,message:t})}return ac(e,u_),e}function uA(r){return typeof r=="object"&&r!==null&&"code"in r&&"message"in r&&(typeof r.code=="number"||typeof r.code=="bigint")&&typeof r.message=="string"}T.SOLANA_ERROR__ACCOUNTS__ACCOUNT_NOT_FOUND=$f;T.SOLANA_ERROR__ACCOUNTS__EXPECTED_ALL_ACCOUNTS_TO_BE_DECODED=Zf;T.SOLANA_ERROR__ACCOUNTS__EXPECTED_DECODED_ACCOUNT=Xf;T.SOLANA_ERROR__ACCOUNTS__FAILED_TO_DECODE_ACCOUNT=jf;T.SOLANA_ERROR__ACCOUNTS__ONE_OR_MORE_ACCOUNTS_NOT_FOUND=Yf;T.SOLANA_ERROR__ADDRESSES__FAILED_TO_FIND_VIABLE_PDA_BUMP_SEED=qf;T.SOLANA_ERROR__ADDRESSES__INVALID_BASE58_ENCODED_ADDRESS=Pf;T.SOLANA_ERROR__ADDRESSES__INVALID_BYTE_LENGTH=Mf;T.SOLANA_ERROR__ADDRESSES__INVALID_ED25519_PUBLIC_KEY=Ff;T.SOLANA_ERROR__ADDRESSES__INVALID_SEEDS_POINT_ON_CURVE=Kf;T.SOLANA_ERROR__ADDRESSES__MALFORMED_PDA=zf;T.SOLANA_ERROR__ADDRESSES__MAX_NUMBER_OF_PDA_SEEDS_EXCEEDED=Gf;T.SOLANA_ERROR__ADDRESSES__MAX_PDA_SEED_LENGTH_EXCEEDED=Hf;T.SOLANA_ERROR__ADDRESSES__PDA_BUMP_SEED_OUT_OF_RANGE=Vf;T.SOLANA_ERROR__ADDRESSES__PDA_ENDS_WITH_PDA_MARKER=Wf;T.SOLANA_ERROR__ADDRESSES__STRING_LENGTH_OUT_OF_RANGE=Uf;T.SOLANA_ERROR__BLOCKHASH_STRING_LENGTH_OUT_OF_RANGE=Of;T.SOLANA_ERROR__BLOCK_HEIGHT_EXCEEDED=mf;T.SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY=O0;T.SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS=W0;T.SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL=K0;T.SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH=v0;T.SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH=C0;T.SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH=T0;T.SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE=L0;T.SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH=I0;T.SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH=U0;T.SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH=N0;T.SOLANA_ERROR__CODECS__EXPECTED_ZERO_VALUE_TO_MATCH_ITEM_FIXED_SIZE=H0;T.SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH=w0;T.SOLANA_ERROR__CODECS__INVALID_CONSTANT=G0;T.SOLANA_ERROR__CODECS__INVALID_DISCRIMINATED_UNION_VARIANT=k0;T.SOLANA_ERROR__CODECS__INVALID_ENUM_VARIANT=B0;T.SOLANA_ERROR__CODECS__INVALID_LITERAL_UNION_VARIANT=F0;T.SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS=x0;T.SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE=M0;T.SOLANA_ERROR__CODECS__LITERAL_UNION_DISCRIMINATOR_OUT_OF_RANGE=z0;T.SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE=D0;T.SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE=P0;T.SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES=q0;T.SOLANA_ERROR__CODECS__UNION_VARIANT_OUT_OF_RANGE=V0;T.SOLANA_ERROR__CRYPTO__RANDOM_VALUES_FUNCTION_UNIMPLEMENTED=sd;T.SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_ALREADY_INITIALIZED=bd;T.SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_FAILED=Ud;T.SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_OUTSTANDING=Pd;T.SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_SIZE_CHANGED=Dd;T.SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_TOO_SMALL=yd;T.SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_EXECUTABLE=Md;T.SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_RENT_EXEMPT=ih;T.SOLANA_ERROR__INSTRUCTION_ERROR__ARITHMETIC_OVERFLOW=sh;T.SOLANA_ERROR__INSTRUCTION_ERROR__BORSH_IO_ERROR=rc;T.SOLANA_ERROR__INSTRUCTION_ERROR__BUILTIN_PROGRAMS_MUST_CONSUME_COMPUTE_UNITS=dh;T.SOLANA_ERROR__INSTRUCTION_ERROR__CALL_DEPTH=qd;T.SOLANA_ERROR__INSTRUCTION_ERROR__COMPUTATIONAL_BUDGET_EXCEEDED=Zd;T.SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM=tc;T.SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_INDEX=xd;T.SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_OUT_OF_SYNC=Fd;T.SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_ACCOUNT_NOT_RENT_EXEMPT=Hd;T.SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_DATA_MODIFIED=Vd;T.SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_LAMPORT_CHANGE=Gd;T.SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_MODIFIED=Ld;T.SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_DATA_MODIFIED=Td;T.SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_LAMPORT_SPEND=Nd;T.SOLANA_ERROR__INSTRUCTION_ERROR__GENERIC_ERROR=pd;T.SOLANA_ERROR__INSTRUCTION_ERROR__ILLEGAL_OWNER=ch;T.SOLANA_ERROR__INSTRUCTION_ERROR__IMMUTABLE=rh;T.SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_AUTHORITY=nh;T.SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_PROGRAM_ID=md;T.SOLANA_ERROR__INSTRUCTION_ERROR__INSUFFICIENT_FUNDS=Ad;T.SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_DATA=gd;T.SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_OWNER=oh;T.SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ARGUMENT=Rd;T.SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ERROR=zd;T.SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_INSTRUCTION_DATA=Ed;T.SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_REALLOC=Xd;T.SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_SEEDS=jd;T.SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_DATA_ALLOCATIONS_EXCEEDED=uh;T.SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_EXCEEDED=lh;T.SOLANA_ERROR__INSTRUCTION_ERROR__MAX_INSTRUCTION_TRACE_LENGTH_EXCEEDED=fh;T.SOLANA_ERROR__INSTRUCTION_ERROR__MAX_SEED_LENGTH_EXCEEDED=Yd;T.SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_ACCOUNT=Wd;T.SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_REQUIRED_SIGNATURE=Sd;T.SOLANA_ERROR__INSTRUCTION_ERROR__MODIFIED_PROGRAM_ID=Id;T.SOLANA_ERROR__INSTRUCTION_ERROR__NOT_ENOUGH_ACCOUNT_KEYS=Bd;T.SOLANA_ERROR__INSTRUCTION_ERROR__PRIVILEGE_ESCALATION=Jd;T.SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_ENVIRONMENT_SETUP_FAILURE=Qd;T.SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPILE=th;T.SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPLETE=eh;T.SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_DATA_MODIFIED=Cd;T.SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_LAMPORT_CHANGE=vd;T.SOLANA_ERROR__INSTRUCTION_ERROR__REENTRANCY_NOT_ALLOWED=$d;T.SOLANA_ERROR__INSTRUCTION_ERROR__RENT_EPOCH_MODIFIED=kd;T.SOLANA_ERROR__INSTRUCTION_ERROR__UNBALANCED_INSTRUCTION=wd;T.SOLANA_ERROR__INSTRUCTION_ERROR__UNINITIALIZED_ACCOUNT=Od;T.SOLANA_ERROR__INSTRUCTION_ERROR__UNKNOWN=ec;T.SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_PROGRAM_ID=Kd;T.SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_SYSVAR=ah;T.SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_ACCOUNTS=dd;T.SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_DATA=hd;T.SOLANA_ERROR__INSTRUCTION__PROGRAM_ID_MISMATCH=_d;T.SOLANA_ERROR__INVALID_BLOCKHASH_BYTE_LENGTH=wf;T.SOLANA_ERROR__INVALID_NONCE=Sf;T.SOLANA_ERROR__INVARIANT_VIOLATION__CACHED_ABORTABLE_ITERABLE_CACHE_ENTRY_MISSING=i_;T.SOLANA_ERROR__INVARIANT_VIOLATION__DATA_PUBLISHER_CHANNEL_UNIMPLEMENTED=s_;T.SOLANA_ERROR__INVARIANT_VIOLATION__SUBSCRIPTION_ITERATOR_MUST_NOT_POLL_BEFORE_RESOLVING_EXISTING_MESSAGE_PROMISE=n_;T.SOLANA_ERROR__INVARIANT_VIOLATION__SUBSCRIPTION_ITERATOR_STATE_MISSING=r_;T.SOLANA_ERROR__INVARIANT_VIOLATION__SWITCH_MUST_BE_EXHAUSTIVE=o_;T.SOLANA_ERROR__JSON_RPC__INTERNAL_ERROR=Va;T.SOLANA_ERROR__JSON_RPC__INVALID_PARAMS=Ga;T.SOLANA_ERROR__JSON_RPC__INVALID_REQUEST=Ka;T.SOLANA_ERROR__JSON_RPC__METHOD_NOT_FOUND=Ha;T.SOLANA_ERROR__JSON_RPC__PARSE_ERROR=za;T.SOLANA_ERROR__JSON_RPC__SCAN_ERROR=$a;T.SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_CLEANED_UP=Qa;T.SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_NOT_AVAILABLE=Ja;T.SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET=Wa;T.SOLANA_ERROR__JSON_RPC__SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX=Ya;T.SOLANA_ERROR__JSON_RPC__SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED=ja;T.SOLANA_ERROR__JSON_RPC__SERVER_ERROR_MIN_CONTEXT_SLOT_NOT_REACHED=Cf;T.SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NODE_UNHEALTHY=Bf;T.SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NO_SNAPSHOT=kf;T.SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE=vo;T.SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SLOT_SKIPPED=Xa;T.SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_HISTORY_NOT_AVAILABLE=Lf;T.SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE=Za;T.SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_LEN_MISMATCH=xf;T.SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_VERIFICATION_FAILURE=Df;T.SOLANA_ERROR__JSON_RPC__SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION=qa;T.SOLANA_ERROR__KEYS__INVALID_KEY_PAIR_BYTE_LENGTH=ad;T.SOLANA_ERROR__KEYS__INVALID_PRIVATE_KEY_BYTE_LENGTH=cd;T.SOLANA_ERROR__KEYS__INVALID_SIGNATURE_BYTE_LENGTH=ud;T.SOLANA_ERROR__KEYS__PUBLIC_KEY_MUST_MATCH_PRIVATE_KEY=fd;T.SOLANA_ERROR__KEYS__SIGNATURE_STRING_LENGTH_OUT_OF_RANGE=ld;T.SOLANA_ERROR__LAMPORTS_OUT_OF_RANGE=If;T.SOLANA_ERROR__MALFORMED_BIGINT_STRING=Nf;T.SOLANA_ERROR__MALFORMED_JSON_RPC_ERROR=Fa;T.SOLANA_ERROR__MALFORMED_NUMBER_STRING=Tf;T.SOLANA_ERROR__NONCE_ACCOUNT_NOT_FOUND=bf;T.SOLANA_ERROR__RPC_SUBSCRIPTIONS__CANNOT_CREATE_SUBSCRIPTION_PLAN=Z0;T.SOLANA_ERROR__RPC_SUBSCRIPTIONS__CHANNEL_CLOSED_BEFORE_MESSAGE_BUFFERED=Q0;T.SOLANA_ERROR__RPC_SUBSCRIPTIONS__CHANNEL_CONNECTION_CLOSED=e_;T.SOLANA_ERROR__RPC_SUBSCRIPTIONS__CHANNEL_FAILED_TO_CONNECT=t_;T.SOLANA_ERROR__RPC_SUBSCRIPTIONS__EXPECTED_SERVER_SUBSCRIPTION_ID=J0;T.SOLANA_ERROR__RPC__API_PLAN_MISSING_FOR_RPC_METHOD=X0;T.SOLANA_ERROR__RPC__INTEGER_OVERFLOW=$0;T.SOLANA_ERROR__RPC__TRANSPORT_HTTP_ERROR=j0;T.SOLANA_ERROR__RPC__TRANSPORT_HTTP_HEADER_FORBIDDEN=Y0;T.SOLANA_ERROR__SIGNER__ADDRESS_CANNOT_HAVE_MULTIPLE_SIGNERS=hh;T.SOLANA_ERROR__SIGNER__EXPECTED_KEY_PAIR_SIGNER=_h;T.SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_MODIFYING_SIGNER=Rh;T.SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_PARTIAL_SIGNER=Eh;T.SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_SIGNER=ph;T.SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_MODIFYING_SIGNER=yh;T.SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_PARTIAL_SIGNER=Ah;T.SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SENDING_SIGNER=mh;T.SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SIGNER=gh;T.SOLANA_ERROR__SIGNER__TRANSACTION_CANNOT_HAVE_MULTIPLE_SENDING_SIGNERS=Sh;T.SOLANA_ERROR__SIGNER__TRANSACTION_SENDING_SIGNER_MISSING=bh;T.SOLANA_ERROR__SIGNER__WALLET_MULTISIGN_UNIMPLEMENTED=Oh;T.SOLANA_ERROR__SUBTLE_CRYPTO__CANNOT_EXPORT_NON_EXTRACTABLE_KEY=od;T.SOLANA_ERROR__SUBTLE_CRYPTO__DIGEST_UNIMPLEMENTED=Qf;T.SOLANA_ERROR__SUBTLE_CRYPTO__DISALLOWED_IN_INSECURE_CONTEXT=Jf;T.SOLANA_ERROR__SUBTLE_CRYPTO__ED25519_ALGORITHM_UNIMPLEMENTED=ed;T.SOLANA_ERROR__SUBTLE_CRYPTO__EXPORT_FUNCTION_UNIMPLEMENTED=td;T.SOLANA_ERROR__SUBTLE_CRYPTO__GENERATE_FUNCTION_UNIMPLEMENTED=rd;T.SOLANA_ERROR__SUBTLE_CRYPTO__SIGN_FUNCTION_UNIMPLEMENTED=nd;T.SOLANA_ERROR__SUBTLE_CRYPTO__VERIFY_FUNCTION_UNIMPLEMENTED=id;T.SOLANA_ERROR__TIMESTAMP_OUT_OF_RANGE=vf;T.SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_BORROW_OUTSTANDING=s0;T.SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_IN_USE=qh;T.SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_LOADED_TWICE=Wh;T.SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_NOT_FOUND=$h;T.SOLANA_ERROR__TRANSACTION_ERROR__ADDRESS_LOOKUP_TABLE_NOT_FOUND=h0;T.SOLANA_ERROR__TRANSACTION_ERROR__ALREADY_PROCESSED=Zh;T.SOLANA_ERROR__TRANSACTION_ERROR__BLOCKHASH_NOT_FOUND=Jh;T.SOLANA_ERROR__TRANSACTION_ERROR__CALL_CHAIN_TOO_DEEP=Qh;T.SOLANA_ERROR__TRANSACTION_ERROR__CLUSTER_MAINTENANCE=o0;T.SOLANA_ERROR__TRANSACTION_ERROR__DUPLICATE_INSTRUCTION=ic;T.SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_FEE=jh;T.SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_RENT=oc;T.SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_FOR_FEE=Xh;T.SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_INDEX=t0;T.SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_DATA=p0;T.SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_INDEX=R0;T.SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_OWNER=_0;T.SOLANA_ERROR__TRANSACTION_ERROR__INVALID_LOADED_ACCOUNTS_DATA_SIZE_LIMIT=m0;T.SOLANA_ERROR__TRANSACTION_ERROR__INVALID_PROGRAM_FOR_EXECUTION=n0;T.SOLANA_ERROR__TRANSACTION_ERROR__INVALID_RENT_PAYING_ACCOUNT=E0;T.SOLANA_ERROR__TRANSACTION_ERROR__INVALID_WRITABLE_ACCOUNT=u0;T.SOLANA_ERROR__TRANSACTION_ERROR__MAX_LOADED_ACCOUNTS_DATA_SIZE_EXCEEDED=A0;T.SOLANA_ERROR__TRANSACTION_ERROR__MISSING_SIGNATURE_FOR_FEE=e0;T.SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_ACCOUNT_NOT_FOUND=Yh;T.SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_EXECUTION_TEMPORARILY_RESTRICTED=sc;T.SOLANA_ERROR__TRANSACTION_ERROR__RESANITIZATION_NEEDED=S0;T.SOLANA_ERROR__TRANSACTION_ERROR__SANITIZE_FAILURE=i0;T.SOLANA_ERROR__TRANSACTION_ERROR__SIGNATURE_FAILURE=r0;T.SOLANA_ERROR__TRANSACTION_ERROR__TOO_MANY_ACCOUNT_LOCKS=d0;T.SOLANA_ERROR__TRANSACTION_ERROR__UNBALANCED_TRANSACTION=b0;T.SOLANA_ERROR__TRANSACTION_ERROR__UNKNOWN=nc;T.SOLANA_ERROR__TRANSACTION_ERROR__UNSUPPORTED_VERSION=c0;T.SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_BLOCK_LIMIT=f0;T.SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_TOTAL_LIMIT=y0;T.SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_ACCOUNT_COST_LIMIT=l0;T.SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_BLOCK_COST_LIMIT=a0;T.SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_VOTE_COST_LIMIT=g0;T.SOLANA_ERROR__TRANSACTION__ADDRESSES_CANNOT_SIGN_TRANSACTION=zh;T.SOLANA_ERROR__TRANSACTION__ADDRESS_MISSING=Dh;T.SOLANA_ERROR__TRANSACTION__CANNOT_ENCODE_WITH_EMPTY_SIGNATURES=Vh;T.SOLANA_ERROR__TRANSACTION__EXPECTED_BLOCKHASH_LIFETIME=Nh;T.SOLANA_ERROR__TRANSACTION__EXPECTED_NONCE_LIFETIME=Th;T.SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_CONTENTS_MISSING=Ch;T.SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_INDEX_OUT_OF_RANGE=xh;T.SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_FEE_PAYER_MISSING=kh;T.SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_INSTRUCTION_PROGRAM_ADDRESS_NOT_FOUND=Lh;T.SOLANA_ERROR__TRANSACTION__FAILED_TO_ESTIMATE_COMPUTE_LIMIT=Hh;T.SOLANA_ERROR__TRANSACTION__FAILED_WHEN_SIMULATING_TO_ESTIMATE_COMPUTE_LIMIT=Kh;T.SOLANA_ERROR__TRANSACTION__FEE_PAYER_MISSING=Mh;T.SOLANA_ERROR__TRANSACTION__FEE_PAYER_SIGNATURE_MISSING=Uh;T.SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_FIRST_INSTRUCTION_MUST_BE_ADVANCE_NONCE=Fh;T.SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_INSTRUCTIONS_MISSING=Ph;T.SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_CANNOT_PAY_FEES=wh;T.SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_MUST_NOT_BE_WRITABLE=Ih;T.SOLANA_ERROR__TRANSACTION__MESSAGE_SIGNATURES_MISMATCH=Gh;T.SOLANA_ERROR__TRANSACTION__SIGNATURES_MISSING=Bh;T.SOLANA_ERROR__TRANSACTION__VERSION_NUMBER_OUT_OF_RANGE=vh;T.SolanaError=oi;T.getSolanaErrorFromInstructionError=cc;T.getSolanaErrorFromJsonRpcError=u_;T.getSolanaErrorFromTransactionError=uc;T.isSolanaError=sA;T.safeCaptureStackTrace=ac});var x_=Re(de=>{"use strict";z();var Ve=lc(),lA=r=>{let e=r.filter(o=>o.length);if(e.length===0)return r.length?r[0]:new Uint8Array;if(e.length===1)return e[0];let t=e.reduce((o,s)=>o+s.length,0),n=new Uint8Array(t),i=0;return e.forEach(o=>{n.set(o,i),i+=o.length}),n},l_=(r,e)=>{if(r.length>=e)return r;let t=new Uint8Array(e).fill(0);return t.set(r),t},f_=(r,e)=>l_(r.length<=e?r:r.slice(0,e),e);function d_(r,e,t){let n=t===0&&r.length===e.length?r:r.slice(t,t+e.length);return n.length!==e.length?!1:e.every((i,o)=>i===n[o])}function si(r,e){return"fixedSize"in e?e.fixedSize:e.getSizeFromValue(r)}function lr(r){return Object.freeze({...r,encode:e=>{let t=new Uint8Array(si(e,r));return r.write(e,t,0),t}})}function gr(r){return Object.freeze({...r,decode:(e,t=0)=>r.read(e,t)[0]})}function h_(r){return Object.freeze({...r,decode:(e,t=0)=>r.read(e,t)[0],encode:e=>{let t=new Uint8Array(si(e,r));return r.write(e,t,0),t}})}function rt(r){return"fixedSize"in r&&typeof r.fixedSize=="number"}function fc(r){if(!rt(r))throw new Ve.SolanaError(Ve.SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH)}function dc(r){return!rt(r)}function fA(r){if(!dc(r))throw new Ve.SolanaError(Ve.SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH)}function xr(r,e){if(rt(r)!==rt(e))throw new Ve.SolanaError(Ve.SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH);if(rt(r)&&rt(e)&&r.fixedSize!==e.fixedSize)throw new Ve.SolanaError(Ve.SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH,{decoderFixedSize:e.fixedSize,encoderFixedSize:r.fixedSize});if(!rt(r)&&!rt(e)&&r.maxSize!==e.maxSize)throw new Ve.SolanaError(Ve.SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH,{decoderMaxSize:e.maxSize,encoderMaxSize:r.maxSize});return{...e,...r,decode:e.decode,encode:r.encode,read:e.read,write:r.write}}function __(r,e){let t=(n,i,o)=>{let s=r.encode(n);if(R_(s,e)>=0)throw new Ve.SolanaError(Ve.SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL,{encodedBytes:s,hexEncodedBytes:Co(s),hexSentinel:Co(e),sentinel:e});return i.set(s,o),o+=s.length,i.set(e,o),o+=e.length,o};return rt(r)?lr({...r,fixedSize:r.fixedSize+e.length,write:t}):lr({...r,...r.maxSize!=null?{maxSize:r.maxSize+e.length}:{},getSizeFromValue:n=>r.getSizeFromValue(n)+e.length,write:t})}function p_(r,e){let t=(n,i)=>{let o=i===0?n:n.slice(i),s=R_(o,e);if(s===-1)throw new Ve.SolanaError(Ve.SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES,{decodedBytes:o,hexDecodedBytes:Co(o),hexSentinel:Co(e),sentinel:e});let u=o.slice(0,s);return[r.decode(u),i+u.length+e.length]};return rt(r)?gr({...r,fixedSize:r.fixedSize+e.length,read:t}):gr({...r,...r.maxSize!=null?{maxSize:r.maxSize+e.length}:{},read:t})}function dA(r,e){return xr(__(r,e),p_(r,e))}function R_(r,e){return r.findIndex((t,n,i)=>e.length===1?t===e[0]:d_(i,e,n))}function Co(r){return r.reduce((e,t)=>e+t.toString(16).padStart(2,"0"),"")}function hA(r,e,t=0){if(e.length-t<=0)throw new Ve.SolanaError(Ve.SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY,{codecDescription:r})}function hc(r,e,t,n=0){let i=t.length-n;if(i<e)throw new Ve.SolanaError(Ve.SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH,{bytesLength:i,codecDescription:r,expected:e})}function ai(r,e,t){if(e<0||e>t)throw new Ve.SolanaError(Ve.SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE,{bytesLength:t,codecDescription:r,offset:e})}function E_(r,e){let t=(s,u,l)=>{let p=r.encode(s);return l=e.write(p.length,u,l),u.set(p,l),l+p.length};if(rt(e)&&rt(r))return lr({...r,fixedSize:e.fixedSize+r.fixedSize,write:t});let n=rt(e)?e.fixedSize:e.maxSize??null,i=rt(r)?r.fixedSize:r.maxSize??null,o=n!==null&&i!==null?n+i:null;return lr({...r,...o!==null?{maxSize:o}:{},getSizeFromValue:s=>{let u=si(s,r);return si(u,e)+u},write:t})}function g_(r,e){let t=(s,u)=>{let[l,p]=e.read(s,u),R=Number(l);return u=p,(u>0||s.length>R)&&(s=s.slice(u,u+R)),hc("addDecoderSizePrefix",R,s),[r.decode(s),u+R]};if(rt(e)&&rt(r))return gr({...r,fixedSize:e.fixedSize+r.fixedSize,read:t});let n=rt(e)?e.fixedSize:e.maxSize??null,i=rt(r)?r.fixedSize:r.maxSize??null,o=n!==null&&i!==null?n+i:null;return gr({...r,...o!==null?{maxSize:o}:{},read:t})}function _A(r,e){return xr(E_(r,e),g_(r,e))}function y_(r,e){return lr({fixedSize:e,write:(t,n,i)=>{let o=r.encode(t),s=o.length>e?o.slice(0,e):o;return n.set(s,i),i+e}})}function A_(r,e){return gr({fixedSize:e,read:(t,n)=>{hc("fixCodecSize",e,t,n),(n>0||t.length>e)&&(t=t.slice(n,n+e)),rt(r)&&(t=f_(t,r.fixedSize));let[i]=r.read(t,0);return[i,n+e]}})}function pA(r,e){return xr(y_(r,e),A_(r,e))}function xo(r,e){return lr({...r,write:(t,n,i)=>{let o=p=>m_(p,n.length),s=e.preOffset?e.preOffset({bytes:n,preOffset:i,wrapBytes:o}):i;ai("offsetEncoder",s,n.length);let u=r.write(t,n,s),l=e.postOffset?e.postOffset({bytes:n,newPreOffset:s,postOffset:u,preOffset:i,wrapBytes:o}):u;return ai("offsetEncoder",l,n.length),l}})}function Lo(r,e){return gr({...r,read:(t,n)=>{let i=p=>m_(p,t.length),o=e.preOffset?e.preOffset({bytes:t,preOffset:n,wrapBytes:i}):n;ai("offsetDecoder",o,t.length);let[s,u]=r.read(t,o),l=e.postOffset?e.postOffset({bytes:t,newPreOffset:o,postOffset:u,preOffset:n,wrapBytes:i}):u;return ai("offsetDecoder",l,t.length),[s,l]}})}function RA(r,e){return xr(xo(r,e),Lo(r,e))}function m_(r,e){return e===0?0:(r%e+e)%e}function ko(r,e){if(rt(r)){let t=e(r.fixedSize);if(t<0)throw new Ve.SolanaError(Ve.SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH,{bytesLength:t,codecDescription:"resizeEncoder"});return lr({...r,fixedSize:t})}return lr({...r,getSizeFromValue:t=>{let n=e(r.getSizeFromValue(t));if(n<0)throw new Ve.SolanaError(Ve.SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH,{bytesLength:n,codecDescription:"resizeEncoder"});return n}})}function Bo(r,e){if(rt(r)){let t=e(r.fixedSize);if(t<0)throw new Ve.SolanaError(Ve.SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH,{bytesLength:t,codecDescription:"resizeDecoder"});return gr({...r,fixedSize:t})}return r}function EA(r,e){return xr(ko(r,e),Bo(r,e))}function S_(r,e){return xo(ko(r,t=>t+e),{preOffset:({preOffset:t})=>t+e})}function b_(r,e){return xo(ko(r,t=>t+e),{postOffset:({postOffset:t})=>t+e})}function O_(r,e){return Lo(Bo(r,t=>t+e),{preOffset:({preOffset:t})=>t+e})}function w_(r,e){return Lo(Bo(r,t=>t+e),{postOffset:({postOffset:t})=>t+e})}function gA(r,e){return xr(S_(r,e),O_(r,e))}function yA(r,e){return xr(b_(r,e),w_(r,e))}function I_(r,e,t,n,i=0){for(;t<--n;){let o=r[t];e[t+i]=r[n],e[n+i]=o,t++}t===n&&(e[t+i]=r[t])}function N_(r){return fc(r),lr({...r,write:(e,t,n)=>{let i=r.write(e,t,n);return I_(t,t,n,n+r.fixedSize),i}})}function T_(r){return fc(r),gr({...r,read:(e,t)=>{let n=e.slice();return I_(e,n,t,t+r.fixedSize),r.read(n,t)}})}function AA(r){return xr(N_(r),T_(r))}function v_(r,e){return lr({...dc(r)?{...r,getSizeFromValue:t=>r.getSizeFromValue(e(t))}:r,write:(t,n,i)=>r.write(e(t),n,i)})}function C_(r,e){return gr({...r,read:(t,n)=>{let[i,o]=r.read(t,n);return[e(i,t,n),o]}})}function mA(r,e,t){return h_({...v_(r,e),read:t?C_(r,t).read:r.read})}de.addCodecSentinel=dA;de.addCodecSizePrefix=_A;de.addDecoderSentinel=p_;de.addDecoderSizePrefix=g_;de.addEncoderSentinel=__;de.addEncoderSizePrefix=E_;de.assertByteArrayHasEnoughBytesForCodec=hc;de.assertByteArrayIsNotEmptyForCodec=hA;de.assertByteArrayOffsetIsNotOutOfRange=ai;de.assertIsFixedSize=fc;de.assertIsVariableSize=fA;de.combineCodec=xr;de.containsBytes=d_;de.createCodec=h_;de.createDecoder=gr;de.createEncoder=lr;de.fixBytes=f_;de.fixCodecSize=pA;de.fixDecoderSize=A_;de.fixEncoderSize=y_;de.getEncodedSize=si;de.isFixedSize=rt;de.isVariableSize=dc;de.mergeBytes=lA;de.offsetCodec=RA;de.offsetDecoder=Lo;de.offsetEncoder=xo;de.padBytes=l_;de.padLeftCodec=gA;de.padLeftDecoder=O_;de.padLeftEncoder=S_;de.padRightCodec=yA;de.padRightDecoder=w_;de.padRightEncoder=b_;de.resizeCodec=EA;de.resizeDecoder=Bo;de.resizeEncoder=ko;de.reverseCodec=AA;de.reverseDecoder=T_;de.reverseEncoder=N_;de.transformCodec=mA;de.transformDecoder=C_;de.transformEncoder=v_});var ap=Re(pe=>{"use strict";z();var L_=lc(),mt=x_();function _c(r,e,t,n){if(n<e||n>t)throw new L_.SolanaError(L_.SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE,{codecDescription:r,max:t,min:e,value:n})}var k_=(r=>(r[r.Little=0]="Little",r[r.Big=1]="Big",r))(k_||{});function B_(r){return r?.endian!==1}function or(r){return mt.createEncoder({fixedSize:r.size,write(e,t,n){r.range&&_c(r.name,r.range[0],r.range[1],e);let i=new ArrayBuffer(r.size);return r.set(new DataView(i),e,B_(r.config)),t.set(new Uint8Array(i),n),n+r.size}})}function sr(r){return mt.createDecoder({fixedSize:r.size,read(e,t=0){mt.assertByteArrayIsNotEmptyForCodec(r.name,e,t),mt.assertByteArrayHasEnoughBytesForCodec(r.name,r.size,e,t);let n=new DataView(SA(e,t,r.size));return[r.get(n,B_(r.config)),t+r.size]}})}function SA(r,e,t){let n=r.byteOffset+(e??0),i=t??r.byteLength;return r.buffer.slice(n,n+i)}var D_=(r={})=>or({config:r,name:"f32",set:(e,t,n)=>e.setFloat32(0,Number(t),n),size:4}),M_=(r={})=>sr({config:r,get:(e,t)=>e.getFloat32(0,t),name:"f32",size:4}),bA=(r={})=>mt.combineCodec(D_(r),M_(r)),U_=(r={})=>or({config:r,name:"f64",set:(e,t,n)=>e.setFloat64(0,Number(t),n),size:8}),P_=(r={})=>sr({config:r,get:(e,t)=>e.getFloat64(0,t),name:"f64",size:8}),OA=(r={})=>mt.combineCodec(U_(r),P_(r)),F_=(r={})=>or({config:r,name:"i128",range:[-BigInt("0x7fffffffffffffffffffffffffffffff")-1n,BigInt("0x7fffffffffffffffffffffffffffffff")],set:(e,t,n)=>{let i=n?8:0,o=n?0:8,s=0xffffffffffffffffn;e.setBigInt64(i,BigInt(t)>>64n,n),e.setBigUint64(o,BigInt(t)&s,n)},size:16}),z_=(r={})=>sr({config:r,get:(e,t)=>{let n=t?8:0,i=t?0:8,o=e.getBigInt64(n,t),s=e.getBigUint64(i,t);return(o<<64n)+s},name:"i128",size:16}),wA=(r={})=>mt.combineCodec(F_(r),z_(r)),V_=(r={})=>or({config:r,name:"i16",range:[-+"0x7fff"-1,+"0x7fff"],set:(e,t,n)=>e.setInt16(0,Number(t),n),size:2}),G_=(r={})=>sr({config:r,get:(e,t)=>e.getInt16(0,t),name:"i16",size:2}),IA=(r={})=>mt.combineCodec(V_(r),G_(r)),H_=(r={})=>or({config:r,name:"i32",range:[-+"0x7fffffff"-1,+"0x7fffffff"],set:(e,t,n)=>e.setInt32(0,Number(t),n),size:4}),K_=(r={})=>sr({config:r,get:(e,t)=>e.getInt32(0,t),name:"i32",size:4}),NA=(r={})=>mt.combineCodec(H_(r),K_(r)),q_=(r={})=>or({config:r,name:"i64",range:[-BigInt("0x7fffffffffffffff")-1n,BigInt("0x7fffffffffffffff")],set:(e,t,n)=>e.setBigInt64(0,BigInt(t),n),size:8}),W_=(r={})=>sr({config:r,get:(e,t)=>e.getBigInt64(0,t),name:"i64",size:8}),TA=(r={})=>mt.combineCodec(q_(r),W_(r)),$_=()=>or({name:"i8",range:[-+"0x7f"-1,+"0x7f"],set:(r,e)=>r.setInt8(0,Number(e)),size:1}),Y_=()=>sr({get:r=>r.getInt8(0),name:"i8",size:1}),vA=()=>mt.combineCodec($_(),Y_()),j_=()=>mt.createEncoder({getSizeFromValue:r=>r<=127?1:r<=16383?2:3,maxSize:3,write:(r,e,t)=>{_c("shortU16",0,65535,r);let n=[0];for(let i=0;;i+=1){let o=Number(r)>>i*7;if(o===0)break;let s=127&o;n[i]=s,i>0&&(n[i-1]|=128)}return e.set(n,t),t+n.length}}),X_=()=>mt.createDecoder({maxSize:3,read:(r,e)=>{let t=0,n=0;for(;++n;){let i=n-1,o=r[e+i],s=127&o;if(t|=s<<i*7,!(o&128))break}return[t,e+n]}}),CA=()=>mt.combineCodec(j_(),X_()),Z_=(r={})=>or({config:r,name:"u128",range:[0n,BigInt("0xffffffffffffffffffffffffffffffff")],set:(e,t,n)=>{let i=n?8:0,o=n?0:8,s=0xffffffffffffffffn;e.setBigUint64(i,BigInt(t)>>64n,n),e.setBigUint64(o,BigInt(t)&s,n)},size:16}),J_=(r={})=>sr({config:r,get:(e,t)=>{let n=t?8:0,i=t?0:8,o=e.getBigUint64(n,t),s=e.getBigUint64(i,t);return(o<<64n)+s},name:"u128",size:16}),xA=(r={})=>mt.combineCodec(Z_(r),J_(r)),Q_=(r={})=>or({config:r,name:"u16",range:[0,+"0xffff"],set:(e,t,n)=>e.setUint16(0,Number(t),n),size:2}),ep=(r={})=>sr({config:r,get:(e,t)=>e.getUint16(0,t),name:"u16",size:2}),LA=(r={})=>mt.combineCodec(Q_(r),ep(r)),tp=(r={})=>or({config:r,name:"u32",range:[0,+"0xffffffff"],set:(e,t,n)=>e.setUint32(0,Number(t),n),size:4}),rp=(r={})=>sr({config:r,get:(e,t)=>e.getUint32(0,t),name:"u32",size:4}),kA=(r={})=>mt.combineCodec(tp(r),rp(r)),np=(r={})=>or({config:r,name:"u64",range:[0n,BigInt("0xffffffffffffffff")],set:(e,t,n)=>e.setBigUint64(0,BigInt(t),n),size:8}),ip=(r={})=>sr({config:r,get:(e,t)=>e.getBigUint64(0,t),name:"u64",size:8}),BA=(r={})=>mt.combineCodec(np(r),ip(r)),op=()=>or({name:"u8",range:[0,+"0xff"],set:(r,e)=>r.setUint8(0,Number(e)),size:1}),sp=()=>sr({get:r=>r.getUint8(0),name:"u8",size:1}),DA=()=>mt.combineCodec(op(),sp());pe.Endian=k_;pe.assertNumberIsBetweenForCodec=_c;pe.getF32Codec=bA;pe.getF32Decoder=M_;pe.getF32Encoder=D_;pe.getF64Codec=OA;pe.getF64Decoder=P_;pe.getF64Encoder=U_;pe.getI128Codec=wA;pe.getI128Decoder=z_;pe.getI128Encoder=F_;pe.getI16Codec=IA;pe.getI16Decoder=G_;pe.getI16Encoder=V_;pe.getI32Codec=NA;pe.getI32Decoder=K_;pe.getI32Encoder=H_;pe.getI64Codec=TA;pe.getI64Decoder=W_;pe.getI64Encoder=q_;pe.getI8Codec=vA;pe.getI8Decoder=Y_;pe.getI8Encoder=$_;pe.getShortU16Codec=CA;pe.getShortU16Decoder=X_;pe.getShortU16Encoder=j_;pe.getU128Codec=xA;pe.getU128Decoder=J_;pe.getU128Encoder=Z_;pe.getU16Codec=LA;pe.getU16Decoder=ep;pe.getU16Encoder=Q_;pe.getU32Codec=kA;pe.getU32Decoder=rp;pe.getU32Encoder=tp;pe.getU64Codec=BA;pe.getU64Decoder=ip;pe.getU64Encoder=np;pe.getU8Codec=DA;pe.getU8Decoder=sp;pe.getU8Encoder=op});var up=Re((Do,cp)=>{z();(function(r,e){typeof Do=="object"&&typeof cp<"u"?e(Do):typeof define=="function"&&define.amd?define(["exports"],e):(r=typeof globalThis<"u"?globalThis:r||self,e(r.Superstruct={}))})(Do,function(r){"use strict";class e extends TypeError{constructor(L,M){let U,{message:W,explanation:ie,...ne}=L,{path:_e}=L,Je=_e.length===0?W:`At path: ${_e.join(".")} -- ${W}`;super(ie??Je),ie!=null&&(this.cause=Je),Object.assign(this,ne),this.name=this.constructor.name,this.failures=()=>U??(U=[L,...M()])}}function t(N){return n(N)&&typeof N[Symbol.iterator]=="function"}function n(N){return typeof N=="object"&&N!=null}function i(N){return n(N)&&!Array.isArray(N)}function o(N){if(Object.prototype.toString.call(N)!=="[object Object]")return!1;let L=Object.getPrototypeOf(N);return L===null||L===Object.prototype}function s(N){return typeof N=="symbol"?N.toString():typeof N=="string"?JSON.stringify(N):`${N}`}function u(N){let{done:L,value:M}=N.next();return L?void 0:M}function l(N,L,M,U){if(N===!0)return;N===!1?N={}:typeof N=="string"&&(N={message:N});let{path:W,branch:ie}=L,{type:ne}=M,{refinement:_e,message:Je=`Expected a value of type \`${ne}\`${_e?` with refinement \`${_e}\``:""}, but received: \`${s(U)}\``}=N;return{value:U,type:ne,refinement:_e,key:W[W.length-1],path:W,branch:ie,...N,message:Je}}function*p(N,L,M,U){t(N)||(N=[N]);for(let W of N){let ie=l(W,L,M,U);ie&&(yield ie)}}function*R(N,L,M={}){let{path:U=[],branch:W=[N],coerce:ie=!1,mask:ne=!1}=M,_e={path:U,branch:W,mask:ne};ie&&(N=L.coercer(N,_e));let Je="valid";for(let Ee of L.validator(N,_e))Ee.explanation=M.message,Je="not_valid",yield[Ee,void 0];for(let[Ee,Ie,xi]of L.entries(N,_e)){let tt=R(Ie,xi,{path:Ee===void 0?U:[...U,Ee],branch:Ee===void 0?W:[...W,Ie],coerce:ie,mask:ne,message:M.message});for(let Ye of tt)Ye[0]?(Je=Ye[0].refinement!=null?"not_refined":"not_valid",yield[Ye[0],void 0]):ie&&(Ie=Ye[1],Ee===void 0?N=Ie:N instanceof Map?N.set(Ee,Ie):N instanceof Set?N.add(Ie):n(N)&&(Ie!==void 0||Ee in N)&&(N[Ee]=Ie))}if(Je!=="not_valid")for(let Ee of L.refiner(N,_e))Ee.explanation=M.message,Je="not_refined",yield[Ee,void 0];Je==="valid"&&(yield[void 0,N])}class g{constructor(L){let{type:M,schema:U,validator:W,refiner:ie,coercer:ne=Je=>Je,entries:_e=function*(){}}=L;this.type=M,this.schema=U,this.entries=_e,this.coercer=ne,W?this.validator=(Je,Ee)=>{let Ie=W(Je,Ee);return p(Ie,Ee,this,Je)}:this.validator=()=>[],ie?this.refiner=(Je,Ee)=>{let Ie=ie(Je,Ee);return p(Ie,Ee,this,Je)}:this.refiner=()=>[]}assert(L,M){return v(L,this,M)}create(L,M){return m(L,this,M)}is(L){return I(L,this)}mask(L,M){return x(L,this,M)}validate(L,M={}){return O(L,this,M)}}function v(N,L,M){let U=O(N,L,{message:M});if(U[0])throw U[0]}function m(N,L,M){let U=O(N,L,{coerce:!0,message:M});if(U[0])throw U[0];return U[1]}function x(N,L,M){let U=O(N,L,{coerce:!0,mask:!0,message:M});if(U[0])throw U[0];return U[1]}function I(N,L){return!O(N,L)[0]}function O(N,L,M={}){let U=R(N,L,M),W=u(U);return W[0]?[new e(W[0],function*(){for(let ne of U)ne[0]&&(yield ne[0])}),void 0]:[void 0,W[1]]}function D(...N){let L=N[0].type==="type",M=N.map(W=>W.schema),U=Object.assign({},...M);return L?ue(U):q(U)}function k(N,L){return new g({type:N,schema:null,validator:L})}function B(N,L){return new g({...N,refiner:(M,U)=>M===void 0||N.refiner(M,U),validator(M,U){return M===void 0?!0:(L(M,U),N.validator(M,U))}})}function F(N){return new g({type:"dynamic",schema:null,*entries(L,M){yield*N(L,M).entries(L,M)},validator(L,M){return N(L,M).validator(L,M)},coercer(L,M){return N(L,M).coercer(L,M)},refiner(L,M){return N(L,M).refiner(L,M)}})}function $(N){let L;return new g({type:"lazy",schema:null,*entries(M,U){L??(L=N()),yield*L.entries(M,U)},validator(M,U){return L??(L=N()),L.validator(M,U)},coercer(M,U){return L??(L=N()),L.coercer(M,U)},refiner(M,U){return L??(L=N()),L.refiner(M,U)}})}function H(N,L){let{schema:M}=N,U={...M};for(let W of L)delete U[W];switch(N.type){case"type":return ue(U);default:return q(U)}}function K(N){let L=N instanceof g,M=L?{...N.schema}:{...N};for(let U in M)M[U]=Y(M[U]);return L&&N.type==="type"?ue(M):q(M)}function j(N,L){let{schema:M}=N,U={};for(let W of L)U[W]=M[W];switch(N.type){case"type":return ue(U);default:return q(U)}}function J(N,L){return console.warn("superstruct@0.11 - The `struct` helper has been renamed to `define`."),k(N,L)}function me(){return k("any",()=>!0)}function ee(N){return new g({type:"array",schema:N,*entries(L){if(N&&Array.isArray(L))for(let[M,U]of L.entries())yield[M,U,N]},coercer(L){return Array.isArray(L)?L.slice():L},validator(L){return Array.isArray(L)||`Expected an array value, but received: ${s(L)}`}})}function ae(){return k("bigint",N=>typeof N=="bigint")}function b(){return k("boolean",N=>typeof N=="boolean")}function a(){return k("date",N=>N instanceof Date&&!isNaN(N.getTime())||`Expected a valid \`Date\` object, but received: ${s(N)}`)}function d(N){let L={},M=N.map(U=>s(U)).join();for(let U of N)L[U]=U;return new g({type:"enums",schema:L,validator(U){return N.includes(U)||`Expected one of \`${M}\`, but received: ${s(U)}`}})}function h(){return k("func",N=>typeof N=="function"||`Expected a function, but received: ${s(N)}`)}function _(N){return k("instance",L=>L instanceof N||`Expected a \`${N.name}\` instance, but received: ${s(L)}`)}function E(){return k("integer",N=>typeof N=="number"&&!isNaN(N)&&Number.isInteger(N)||`Expected an integer, but received: ${s(N)}`)}function S(N){return new g({type:"intersection",schema:null,*entries(L,M){for(let U of N)yield*U.entries(L,M)},*validator(L,M){for(let U of N)yield*U.validator(L,M)},*refiner(L,M){for(let U of N)yield*U.refiner(L,M)}})}function C(N){let L=s(N),M=typeof N;return new g({type:"literal",schema:M==="string"||M==="number"||M==="boolean"?N:null,validator(U){return U===N||`Expected the literal \`${L}\`, but received: ${s(U)}`}})}function y(N,L){return new g({type:"map",schema:null,*entries(M){if(N&&L&&M instanceof Map)for(let[U,W]of M.entries())yield[U,U,N],yield[U,W,L]},coercer(M){return M instanceof Map?new Map(M):M},validator(M){return M instanceof Map||`Expected a \`Map\` object, but received: ${s(M)}`}})}function f(){return k("never",()=>!1)}function A(N){return new g({...N,validator:(L,M)=>L===null||N.validator(L,M),refiner:(L,M)=>L===null||N.refiner(L,M)})}function G(){return k("number",N=>typeof N=="number"&&!isNaN(N)||`Expected a number, but received: ${s(N)}`)}function q(N){let L=N?Object.keys(N):[],M=f();return new g({type:"object",schema:N||null,*entries(U){if(N&&n(U)){let W=new Set(Object.keys(U));for(let ie of L)W.delete(ie),yield[ie,U[ie],N[ie]];for(let ie of W)yield[ie,U[ie],M]}},validator(U){return i(U)||`Expected an object, but received: ${s(U)}`},coercer(U,W){if(!i(U))return U;let ie={...U};if(W.mask&&N)for(let ne in ie)N[ne]===void 0&&delete ie[ne];return ie}})}function Y(N){return new g({...N,validator:(L,M)=>L===void 0||N.validator(L,M),refiner:(L,M)=>L===void 0||N.refiner(L,M)})}function te(N,L){return new g({type:"record",schema:null,*entries(M){if(n(M))for(let U in M){let W=M[U];yield[U,U,N],yield[U,W,L]}},validator(M){return i(M)||`Expected an object, but received: ${s(M)}`},coercer(M){return i(M)?{...M}:M}})}function oe(){return k("regexp",N=>N instanceof RegExp)}function ce(N){return new g({type:"set",schema:null,*entries(L){if(N&&L instanceof Set)for(let M of L)yield[M,M,N]},coercer(L){return L instanceof Set?new Set(L):L},validator(L){return L instanceof Set||`Expected a \`Set\` object, but received: ${s(L)}`}})}function Ue(){return k("string",N=>typeof N=="string"||`Expected a string, but received: ${s(N)}`)}function he(N){let L=f();return new g({type:"tuple",schema:null,*entries(M){if(Array.isArray(M)){let U=Math.max(N.length,M.length);for(let W=0;W<U;W++)yield[W,M[W],N[W]||L]}},validator(M){return Array.isArray(M)||`Expected an array, but received: ${s(M)}`},coercer(M){return Array.isArray(M)?M.slice():M}})}function ue(N){let L=Object.keys(N);return new g({type:"type",schema:N,*entries(M){if(n(M))for(let U of L)yield[U,M[U],N[U]]},validator(M){return i(M)||`Expected an object, but received: ${s(M)}`},coercer(M){return i(M)?{...M}:M}})}function zt(N){let L=N.map(M=>M.type).join(" | ");return new g({type:"union",schema:null,coercer(M,U){for(let W of N){let[ie,ne]=W.validate(M,{coerce:!0,mask:U.mask});if(!ie)return ne}return M},validator(M,U){let W=[];for(let ie of N){let[...ne]=R(M,ie,U),[_e]=ne;if(_e[0])for(let[Je]of ne)Je&&W.push(Je);else return[]}return[`Expected the value to satisfy a union of \`${L}\`, but received: ${s(M)}`,...W]}})}function we(){return k("unknown",()=>!0)}function Le(N,L,M){return new g({...N,coercer:(U,W)=>I(U,L)?N.coercer(M(U,W),W):N.coercer(U,W)})}function bn(N,L,M={}){return Le(N,we(),U=>{let W=typeof L=="function"?L():L;if(U===void 0)return W;if(!M.strict&&o(U)&&o(W)){let ie={...U},ne=!1;for(let _e in W)ie[_e]===void 0&&(ie[_e]=W[_e],ne=!0);if(ne)return ie}return U})}function Xe(N){return Le(N,Ue(),L=>L.trim())}function Ze(N){return Jt(N,"empty",L=>{let M=rn(L);return M===0||`Expected an empty ${N.type} but received one with a size of \`${M}\``})}function rn(N){return N instanceof Map||N instanceof Set?N.size:N.length}function Ke(N,L,M={}){let{exclusive:U}=M;return Jt(N,"max",W=>U?W<L:W<=L||`Expected a ${N.type} less than ${U?"":"or equal to "}${L} but received \`${W}\``)}function qe(N,L,M={}){let{exclusive:U}=M;return Jt(N,"min",W=>U?W>L:W>=L||`Expected a ${N.type} greater than ${U?"":"or equal to "}${L} but received \`${W}\``)}function Dr(N){return Jt(N,"nonempty",L=>rn(L)>0||`Expected a nonempty ${N.type} but received an empty one`)}function We(N,L){return Jt(N,"pattern",M=>L.test(M)||`Expected a ${N.type} matching \`/${L.source}/\` but received "${M}"`)}function $e(N,L,M=L){let U=`Expected a ${N.type}`,W=L===M?`of \`${L}\``:`between \`${L}\` and \`${M}\``;return Jt(N,"size",ie=>{if(typeof ie=="number"||ie instanceof Date)return L<=ie&&ie<=M||`${U} ${W} but received \`${ie}\``;if(ie instanceof Map||ie instanceof Set){let{size:ne}=ie;return L<=ne&&ne<=M||`${U} with a size ${W} but received one with a size of \`${ne}\``}else{let{length:ne}=ie;return L<=ne&&ne<=M||`${U} with a length ${W} but received one with a length of \`${ne}\``}})}function Jt(N,L,M){return new g({...N,*refiner(U,W){yield*N.refiner(U,W);let ie=M(U,W),ne=p(ie,W,N,U);for(let _e of ne)yield{..._e,refinement:L}}})}r.Struct=g,r.StructError=e,r.any=me,r.array=ee,r.assert=v,r.assign=D,r.bigint=ae,r.boolean=b,r.coerce=Le,r.create=m,r.date=a,r.defaulted=bn,r.define=k,r.deprecated=B,r.dynamic=F,r.empty=Ze,r.enums=d,r.func=h,r.instance=_,r.integer=E,r.intersection=S,r.is=I,r.lazy=$,r.literal=C,r.map=y,r.mask=x,r.max=Ke,r.min=qe,r.never=f,r.nonempty=Dr,r.nullable=A,r.number=G,r.object=q,r.omit=H,r.optional=Y,r.partial=K,r.pattern=We,r.pick=j,r.record=te,r.refine=Jt,r.regexp=oe,r.set=ce,r.size=$e,r.string=Ue,r.struct=J,r.trimmed=Xe,r.tuple=he,r.type=ue,r.union=zt,r.unknown=we,r.validate=O})});function ci(){if(!Mo&&(Mo=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||typeof msCrypto<"u"&&typeof msCrypto.getRandomValues=="function"&&msCrypto.getRandomValues.bind(msCrypto),!Mo))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Mo(MA)}var Mo,MA,pc=Lt(()=>{z();MA=new Uint8Array(16)});var lp,fp=Lt(()=>{z();lp=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i});function UA(r){return typeof r=="string"&&lp.test(r)}var Yr,ui=Lt(()=>{z();fp();Yr=UA});function PA(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,t=(Tt[r[e+0]]+Tt[r[e+1]]+Tt[r[e+2]]+Tt[r[e+3]]+"-"+Tt[r[e+4]]+Tt[r[e+5]]+"-"+Tt[r[e+6]]+Tt[r[e+7]]+"-"+Tt[r[e+8]]+Tt[r[e+9]]+"-"+Tt[r[e+10]]+Tt[r[e+11]]+Tt[r[e+12]]+Tt[r[e+13]]+Tt[r[e+14]]+Tt[r[e+15]]).toLowerCase();if(!Yr(t))throw TypeError("Stringified UUID is invalid");return t}var Tt,Uo,jr,li=Lt(()=>{z();ui();Tt=[];for(Uo=0;Uo<256;++Uo)Tt.push((Uo+256).toString(16).substr(1));jr=PA});function FA(r,e,t){var n=e&&t||0,i=e||new Array(16);r=r||{};var o=r.node||dp,s=r.clockseq!==void 0?r.clockseq:Rc;if(o==null||s==null){var u=r.random||(r.rng||ci)();o==null&&(o=dp=[u[0]|1,u[1],u[2],u[3],u[4],u[5]]),s==null&&(s=Rc=(u[6]<<8|u[7])&16383)}var l=r.msecs!==void 0?r.msecs:Date.now(),p=r.nsecs!==void 0?r.nsecs:gc+1,R=l-Ec+(p-gc)/1e4;if(R<0&&r.clockseq===void 0&&(s=s+1&16383),(R<0||l>Ec)&&r.nsecs===void 0&&(p=0),p>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");Ec=l,gc=p,Rc=s,l+=122192928e5;var g=((l&268435455)*1e4+p)%4294967296;i[n++]=g>>>24&255,i[n++]=g>>>16&255,i[n++]=g>>>8&255,i[n++]=g&255;var v=l/4294967296*1e4&268435455;i[n++]=v>>>8&255,i[n++]=v&255,i[n++]=v>>>24&15|16,i[n++]=v>>>16&255,i[n++]=s>>>8|128,i[n++]=s&255;for(var m=0;m<6;++m)i[n+m]=o[m];return e||jr(i)}var dp,Rc,Ec,gc,hp,_p=Lt(()=>{z();pc();li();Ec=0,gc=0;hp=FA});function zA(r){if(!Yr(r))throw TypeError("Invalid UUID");var e,t=new Uint8Array(16);return t[0]=(e=parseInt(r.slice(0,8),16))>>>24,t[1]=e>>>16&255,t[2]=e>>>8&255,t[3]=e&255,t[4]=(e=parseInt(r.slice(9,13),16))>>>8,t[5]=e&255,t[6]=(e=parseInt(r.slice(14,18),16))>>>8,t[7]=e&255,t[8]=(e=parseInt(r.slice(19,23),16))>>>8,t[9]=e&255,t[10]=(e=parseInt(r.slice(24,36),16))/1099511627776&255,t[11]=e/4294967296&255,t[12]=e>>>24&255,t[13]=e>>>16&255,t[14]=e>>>8&255,t[15]=e&255,t}var Po,yc=Lt(()=>{z();ui();Po=zA});function VA(r){r=unescape(encodeURIComponent(r));for(var e=[],t=0;t<r.length;++t)e.push(r.charCodeAt(t));return e}function Fo(r,e,t){function n(i,o,s,u){if(typeof i=="string"&&(i=VA(i)),typeof o=="string"&&(o=Po(o)),o.length!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var l=new Uint8Array(16+i.length);if(l.set(o),l.set(i,o.length),l=t(l),l[6]=l[6]&15|e,l[8]=l[8]&63|128,s){u=u||0;for(var p=0;p<16;++p)s[u+p]=l[p];return s}return jr(l)}try{n.name=r}catch{}return n.DNS=GA,n.URL=HA,n}var GA,HA,Ac=Lt(()=>{z();li();yc();GA="6ba7b810-9dad-11d1-80b4-00c04fd430c8",HA="6ba7b811-9dad-11d1-80b4-00c04fd430c8"});function KA(r){if(typeof r=="string"){var e=unescape(encodeURIComponent(r));r=new Uint8Array(e.length);for(var t=0;t<e.length;++t)r[t]=e.charCodeAt(t)}return qA(WA($A(r),r.length*8))}function qA(r){for(var e=[],t=r.length*32,n="0123456789abcdef",i=0;i<t;i+=8){var o=r[i>>5]>>>i%32&255,s=parseInt(n.charAt(o>>>4&15)+n.charAt(o&15),16);e.push(s)}return e}function pp(r){return(r+64>>>9<<4)+14+1}function WA(r,e){r[e>>5]|=128<<e%32,r[pp(e)-1]=e;for(var t=1732584193,n=-271733879,i=-1732584194,o=271733878,s=0;s<r.length;s+=16){var u=t,l=n,p=i,R=o;t=Dt(t,n,i,o,r[s],7,-680876936),o=Dt(o,t,n,i,r[s+1],12,-389564586),i=Dt(i,o,t,n,r[s+2],17,606105819),n=Dt(n,i,o,t,r[s+3],22,-1044525330),t=Dt(t,n,i,o,r[s+4],7,-176418897),o=Dt(o,t,n,i,r[s+5],12,1200080426),i=Dt(i,o,t,n,r[s+6],17,-1473231341),n=Dt(n,i,o,t,r[s+7],22,-45705983),t=Dt(t,n,i,o,r[s+8],7,1770035416),o=Dt(o,t,n,i,r[s+9],12,-1958414417),i=Dt(i,o,t,n,r[s+10],17,-42063),n=Dt(n,i,o,t,r[s+11],22,-1990404162),t=Dt(t,n,i,o,r[s+12],7,1804603682),o=Dt(o,t,n,i,r[s+13],12,-40341101),i=Dt(i,o,t,n,r[s+14],17,-1502002290),n=Dt(n,i,o,t,r[s+15],22,1236535329),t=Mt(t,n,i,o,r[s+1],5,-165796510),o=Mt(o,t,n,i,r[s+6],9,-1069501632),i=Mt(i,o,t,n,r[s+11],14,643717713),n=Mt(n,i,o,t,r[s],20,-373897302),t=Mt(t,n,i,o,r[s+5],5,-701558691),o=Mt(o,t,n,i,r[s+10],9,38016083),i=Mt(i,o,t,n,r[s+15],14,-660478335),n=Mt(n,i,o,t,r[s+4],20,-405537848),t=Mt(t,n,i,o,r[s+9],5,568446438),o=Mt(o,t,n,i,r[s+14],9,-1019803690),i=Mt(i,o,t,n,r[s+3],14,-187363961),n=Mt(n,i,o,t,r[s+8],20,1163531501),t=Mt(t,n,i,o,r[s+13],5,-1444681467),o=Mt(o,t,n,i,r[s+2],9,-51403784),i=Mt(i,o,t,n,r[s+7],14,1735328473),n=Mt(n,i,o,t,r[s+12],20,-1926607734),t=Ut(t,n,i,o,r[s+5],4,-378558),o=Ut(o,t,n,i,r[s+8],11,-2022574463),i=Ut(i,o,t,n,r[s+11],16,1839030562),n=Ut(n,i,o,t,r[s+14],23,-35309556),t=Ut(t,n,i,o,r[s+1],4,-1530992060),o=Ut(o,t,n,i,r[s+4],11,1272893353),i=Ut(i,o,t,n,r[s+7],16,-155497632),n=Ut(n,i,o,t,r[s+10],23,-1094730640),t=Ut(t,n,i,o,r[s+13],4,681279174),o=Ut(o,t,n,i,r[s],11,-358537222),i=Ut(i,o,t,n,r[s+3],16,-722521979),n=Ut(n,i,o,t,r[s+6],23,76029189),t=Ut(t,n,i,o,r[s+9],4,-640364487),o=Ut(o,t,n,i,r[s+12],11,-421815835),i=Ut(i,o,t,n,r[s+15],16,530742520),n=Ut(n,i,o,t,r[s+2],23,-995338651),t=Pt(t,n,i,o,r[s],6,-198630844),o=Pt(o,t,n,i,r[s+7],10,1126891415),i=Pt(i,o,t,n,r[s+14],15,-1416354905),n=Pt(n,i,o,t,r[s+5],21,-57434055),t=Pt(t,n,i,o,r[s+12],6,1700485571),o=Pt(o,t,n,i,r[s+3],10,-1894986606),i=Pt(i,o,t,n,r[s+10],15,-1051523),n=Pt(n,i,o,t,r[s+1],21,-2054922799),t=Pt(t,n,i,o,r[s+8],6,1873313359),o=Pt(o,t,n,i,r[s+15],10,-30611744),i=Pt(i,o,t,n,r[s+6],15,-1560198380),n=Pt(n,i,o,t,r[s+13],21,1309151649),t=Pt(t,n,i,o,r[s+4],6,-145523070),o=Pt(o,t,n,i,r[s+11],10,-1120210379),i=Pt(i,o,t,n,r[s+2],15,718787259),n=Pt(n,i,o,t,r[s+9],21,-343485551),t=Xr(t,u),n=Xr(n,l),i=Xr(i,p),o=Xr(o,R)}return[t,n,i,o]}function $A(r){if(r.length===0)return[];for(var e=r.length*8,t=new Uint32Array(pp(e)),n=0;n<e;n+=8)t[n>>5]|=(r[n/8]&255)<<n%32;return t}function Xr(r,e){var t=(r&65535)+(e&65535),n=(r>>16)+(e>>16)+(t>>16);return n<<16|t&65535}function YA(r,e){return r<<e|r>>>32-e}function zo(r,e,t,n,i,o){return Xr(YA(Xr(Xr(e,r),Xr(n,o)),i),t)}function Dt(r,e,t,n,i,o,s){return zo(e&t|~e&n,r,e,i,o,s)}function Mt(r,e,t,n,i,o,s){return zo(e&n|t&~n,r,e,i,o,s)}function Ut(r,e,t,n,i,o,s){return zo(e^t^n,r,e,i,o,s)}function Pt(r,e,t,n,i,o,s){return zo(t^(e|~n),r,e,i,o,s)}var Rp,Ep=Lt(()=>{z();Rp=KA});var jA,gp,yp=Lt(()=>{z();Ac();Ep();jA=Fo("v3",48,Rp),gp=jA});function XA(r,e,t){r=r||{};var n=r.random||(r.rng||ci)();if(n[6]=n[6]&15|64,n[8]=n[8]&63|128,e){t=t||0;for(var i=0;i<16;++i)e[t+i]=n[i];return e}return jr(n)}var Ap,mp=Lt(()=>{z();pc();li();Ap=XA});function ZA(r,e,t,n){switch(r){case 0:return e&t^~e&n;case 1:return e^t^n;case 2:return e&t^e&n^t&n;case 3:return e^t^n}}function mc(r,e){return r<<e|r>>>32-e}function JA(r){var e=[1518500249,1859775393,2400959708,3395469782],t=[1732584193,4023233417,2562383102,271733878,3285377520];if(typeof r=="string"){var n=unescape(encodeURIComponent(r));r=[];for(var i=0;i<n.length;++i)r.push(n.charCodeAt(i))}else Array.isArray(r)||(r=Array.prototype.slice.call(r));r.push(128);for(var o=r.length/4+2,s=Math.ceil(o/16),u=new Array(s),l=0;l<s;++l){for(var p=new Uint32Array(16),R=0;R<16;++R)p[R]=r[l*64+R*4]<<24|r[l*64+R*4+1]<<16|r[l*64+R*4+2]<<8|r[l*64+R*4+3];u[l]=p}u[s-1][14]=(r.length-1)*8/Math.pow(2,32),u[s-1][14]=Math.floor(u[s-1][14]),u[s-1][15]=(r.length-1)*8&4294967295;for(var g=0;g<s;++g){for(var v=new Uint32Array(80),m=0;m<16;++m)v[m]=u[g][m];for(var x=16;x<80;++x)v[x]=mc(v[x-3]^v[x-8]^v[x-14]^v[x-16],1);for(var I=t[0],O=t[1],D=t[2],k=t[3],B=t[4],F=0;F<80;++F){var $=Math.floor(F/20),H=mc(I,5)+ZA($,O,D,k)+B+e[$]+v[F]>>>0;B=k,k=D,D=mc(O,30)>>>0,O=I,I=H}t[0]=t[0]+I>>>0,t[1]=t[1]+O>>>0,t[2]=t[2]+D>>>0,t[3]=t[3]+k>>>0,t[4]=t[4]+B>>>0}return[t[0]>>24&255,t[0]>>16&255,t[0]>>8&255,t[0]&255,t[1]>>24&255,t[1]>>16&255,t[1]>>8&255,t[1]&255,t[2]>>24&255,t[2]>>16&255,t[2]>>8&255,t[2]&255,t[3]>>24&255,t[3]>>16&255,t[3]>>8&255,t[3]&255,t[4]>>24&255,t[4]>>16&255,t[4]>>8&255,t[4]&255]}var Sp,bp=Lt(()=>{z();Sp=JA});var QA,Op,wp=Lt(()=>{z();Ac();bp();QA=Fo("v5",80,Sp),Op=QA});var Ip,Np=Lt(()=>{z();Ip="00000000-0000-0000-0000-000000000000"});function em(r){if(!Yr(r))throw TypeError("Invalid UUID");return parseInt(r.substr(14,1),16)}var Tp,vp=Lt(()=>{z();ui();Tp=em});var Sc={};EE(Sc,{NIL:()=>Ip,parse:()=>Po,stringify:()=>jr,v1:()=>hp,v3:()=>gp,v4:()=>Ap,v5:()=>Op,validate:()=>Yr,version:()=>Tp});var bc=Lt(()=>{z();_p();yp();mp();wp();Np();vp();ui();li();yc()});var xp=Re((iw,Cp)=>{"use strict";z();var tm=(bc(),mu(Sc)).v4,rm=function(r,e,t,n){if(typeof r!="string")throw new TypeError(r+" must be a string");n=n||{};let i=typeof n.version=="number"?n.version:2;if(i!==1&&i!==2)throw new TypeError(i+" must be 1 or 2");let o={method:r};if(i===2&&(o.jsonrpc="2.0"),e){if(typeof e!="object"&&!Array.isArray(e))throw new TypeError(e+" must be an object, array or omitted");o.params=e}if(typeof t>"u"){let s=typeof n.generator=="function"?n.generator:function(){return tm()};o.id=s(o,n)}else i===2&&t===null?n.notificationIdNull&&(o.id=null):o.id=t;return o};Cp.exports=rm});var kp=Re((sw,Lp)=>{"use strict";z();var nm=(bc(),mu(Sc)).v4,im=xp(),fi=function(r,e){if(!(this instanceof fi))return new fi(r,e);e||(e={}),this.options={reviver:typeof e.reviver<"u"?e.reviver:null,replacer:typeof e.replacer<"u"?e.replacer:null,generator:typeof e.generator<"u"?e.generator:function(){return nm()},version:typeof e.version<"u"?e.version:2,notificationIdNull:typeof e.notificationIdNull=="boolean"?e.notificationIdNull:!1},this.callServer=r};Lp.exports=fi;fi.prototype.request=function(r,e,t,n){let i=this,o=null,s=Array.isArray(r)&&typeof e=="function";if(this.options.version===1&&s)throw new TypeError("JSON-RPC 1.0 does not support batching");if(s||!s&&r&&typeof r=="object"&&typeof e=="function")n=e,o=r;else{typeof t=="function"&&(n=t,t=void 0);let p=typeof n=="function";try{o=im(r,e,t,{generator:this.options.generator,version:this.options.version,notificationIdNull:this.options.notificationIdNull})}catch(R){if(p)return n(R);throw R}if(!p)return o}let l;try{l=JSON.stringify(o,this.options.replacer)}catch(p){return n(p)}return this.callServer(l,function(p,R){i._parseResponse(p,R,n)}),o};fi.prototype._parseResponse=function(r,e,t){if(r){t(r);return}if(!e)return t();let n;try{n=JSON.parse(e,this.options.reviver)}catch(i){return t(i)}if(t.length===3)if(Array.isArray(n)){let i=function(s){return typeof s.error<"u"},o=function(s){return!i(s)};return t(null,n.filter(i),n.filter(o))}else return t(null,n.error,n.result);t(null,n)}});var Dp=Re((cw,Oc)=>{"use strict";z();var om=Object.prototype.hasOwnProperty,Gt="~";function di(){}Object.create&&(di.prototype=Object.create(null),new di().__proto__||(Gt=!1));function sm(r,e,t){this.fn=r,this.context=e,this.once=t||!1}function Bp(r,e,t,n,i){if(typeof t!="function")throw new TypeError("The listener must be a function");var o=new sm(t,n||r,i),s=Gt?Gt+e:e;return r._events[s]?r._events[s].fn?r._events[s]=[r._events[s],o]:r._events[s].push(o):(r._events[s]=o,r._eventsCount++),r}function Vo(r,e){--r._eventsCount===0?r._events=new di:delete r._events[e]}function Ft(){this._events=new di,this._eventsCount=0}Ft.prototype.eventNames=function(){var e=[],t,n;if(this._eventsCount===0)return e;for(n in t=this._events)om.call(t,n)&&e.push(Gt?n.slice(1):n);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(t)):e};Ft.prototype.listeners=function(e){var t=Gt?Gt+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,o=n.length,s=new Array(o);i<o;i++)s[i]=n[i].fn;return s};Ft.prototype.listenerCount=function(e){var t=Gt?Gt+e:e,n=this._events[t];return n?n.fn?1:n.length:0};Ft.prototype.emit=function(e,t,n,i,o,s){var u=Gt?Gt+e:e;if(!this._events[u])return!1;var l=this._events[u],p=arguments.length,R,g;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),p){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,n),!0;case 4:return l.fn.call(l.context,t,n,i),!0;case 5:return l.fn.call(l.context,t,n,i,o),!0;case 6:return l.fn.call(l.context,t,n,i,o,s),!0}for(g=1,R=new Array(p-1);g<p;g++)R[g-1]=arguments[g];l.fn.apply(l.context,R)}else{var v=l.length,m;for(g=0;g<v;g++)switch(l[g].once&&this.removeListener(e,l[g].fn,void 0,!0),p){case 1:l[g].fn.call(l[g].context);break;case 2:l[g].fn.call(l[g].context,t);break;case 3:l[g].fn.call(l[g].context,t,n);break;case 4:l[g].fn.call(l[g].context,t,n,i);break;default:if(!R)for(m=1,R=new Array(p-1);m<p;m++)R[m-1]=arguments[m];l[g].fn.apply(l[g].context,R)}}return!0};Ft.prototype.on=function(e,t,n){return Bp(this,e,t,n,!1)};Ft.prototype.once=function(e,t,n){return Bp(this,e,t,n,!0)};Ft.prototype.removeListener=function(e,t,n,i){var o=Gt?Gt+e:e;if(!this._events[o])return this;if(!t)return Vo(this,o),this;var s=this._events[o];if(s.fn)s.fn===t&&(!i||s.once)&&(!n||s.context===n)&&Vo(this,o);else{for(var u=0,l=[],p=s.length;u<p;u++)(s[u].fn!==t||i&&!s[u].once||n&&s[u].context!==n)&&l.push(s[u]);l.length?this._events[o]=l.length===1?l[0]:l:Vo(this,o)}return this};Ft.prototype.removeAllListeners=function(e){var t;return e?(t=Gt?Gt+e:e,this._events[t]&&Vo(this,t)):(this._events=new di,this._eventsCount=0),this};Ft.prototype.off=Ft.prototype.removeListener;Ft.prototype.addListener=Ft.prototype.on;Ft.prefixed=Gt;Ft.EventEmitter=Ft;typeof Oc<"u"&&(Oc.exports=Ft)});var zp=Re(hi=>{"use strict";z();var am=Tn(),Mp=Dp(),cm=class extends Mp.EventEmitter{socket;constructor(r,e,t){super(),this.socket=new window.WebSocket(r,t),this.socket.onopen=()=>this.emit("open"),this.socket.onmessage=n=>this.emit("message",n.data),this.socket.onerror=n=>this.emit("error",n),this.socket.onclose=n=>{this.emit("close",n.code,n.reason)}}send(r,e,t){let n=t||e;try{this.socket.send(r),n()}catch(i){n(i)}}close(r,e){this.socket.close(r,e)}addEventListener(r,e,t){this.socket.addEventListener(r,e,t)}};function Up(r,e){return new cm(r,e)}var Pp=class{encode(r){return JSON.stringify(r)}decode(r){return JSON.parse(r)}},Fp=class extends Mp.EventEmitter{address;rpc_id;queue;options;autoconnect;ready;reconnect;reconnect_timer_id;reconnect_interval;max_reconnects;rest_options;current_reconnects;generate_request_id;socket;webSocketFactory;dataPack;constructor(r,e="ws://localhost:8080",{autoconnect:t=!0,reconnect:n=!0,reconnect_interval:i=1e3,max_reconnects:o=5,...s}={},u,l){super(),this.webSocketFactory=r,this.queue={},this.rpc_id=0,this.address=e,this.autoconnect=t,this.ready=!1,this.reconnect=n,this.reconnect_timer_id=void 0,this.reconnect_interval=i,this.max_reconnects=o,this.rest_options=s,this.current_reconnects=0,this.generate_request_id=u||(()=>typeof this.rpc_id=="number"?++this.rpc_id:Number(this.rpc_id)+1),l?this.dataPack=l:this.dataPack=new Pp,this.autoconnect&&this._connect(this.address,{autoconnect:this.autoconnect,reconnect:this.reconnect,reconnect_interval:this.reconnect_interval,max_reconnects:this.max_reconnects,...this.rest_options})}connect(){this.socket||this._connect(this.address,{autoconnect:this.autoconnect,reconnect:this.reconnect,reconnect_interval:this.reconnect_interval,max_reconnects:this.max_reconnects,...this.rest_options})}call(r,e,t,n){return!n&&typeof t=="object"&&(n=t,t=null),new Promise((i,o)=>{if(!this.ready)return o(new Error("socket not ready"));let s=this.generate_request_id(r,e),u={jsonrpc:"2.0",method:r,params:e||void 0,id:s};this.socket.send(this.dataPack.encode(u),n,l=>{if(l)return o(l);this.queue[s]={promise:[i,o]},t&&(this.queue[s].timeout=setTimeout(()=>{delete this.queue[s],o(new Error("reply timeout"))},t))})})}async login(r){let e=await this.call("rpc.login",r);if(!e)throw new Error("authentication failed");return e}async listMethods(){return await this.call("__listMethods")}notify(r,e){return new Promise((t,n)=>{if(!this.ready)return n(new Error("socket not ready"));let i={jsonrpc:"2.0",method:r,params:e};this.socket.send(this.dataPack.encode(i),o=>{if(o)return n(o);t()})})}async subscribe(r){typeof r=="string"&&(r=[r]);let e=await this.call("rpc.on",r);if(typeof r=="string"&&e[r]!=="ok")throw new Error("Failed subscribing to an event '"+r+"' with: "+e[r]);return e}async unsubscribe(r){typeof r=="string"&&(r=[r]);let e=await this.call("rpc.off",r);if(typeof r=="string"&&e[r]!=="ok")throw new Error("Failed unsubscribing from an event with: "+e);return e}close(r,e){this.socket.close(r||1e3,e)}setAutoReconnect(r){this.reconnect=r}setReconnectInterval(r){this.reconnect_interval=r}setMaxReconnects(r){this.max_reconnects=r}_connect(r,e){clearTimeout(this.reconnect_timer_id),this.socket=this.webSocketFactory(r,e),this.socket.addEventListener("open",()=>{this.ready=!0,this.emit("open"),this.current_reconnects=0}),this.socket.addEventListener("message",({data:t})=>{t instanceof ArrayBuffer&&(t=am.Buffer.from(t).toString());try{t=this.dataPack.decode(t)}catch{return}if(t.notification&&this.listeners(t.notification).length){if(!Object.keys(t.params).length)return this.emit(t.notification);let n=[t.notification];if(t.params.constructor===Object)n.push(t.params);else for(let i=0;i<t.params.length;i++)n.push(t.params[i]);return Promise.resolve().then(()=>{this.emit.apply(this,n)})}if(!this.queue[t.id])return t.method?Promise.resolve().then(()=>{this.emit(t.method,t?.params)}):void 0;"error"in t=="result"in t&&this.queue[t.id].promise[1](new Error('Server response malformed. Response must include either "result" or "error", but not both.')),this.queue[t.id].timeout&&clearTimeout(this.queue[t.id].timeout),t.error?this.queue[t.id].promise[1](t.error):this.queue[t.id].promise[0](t.result),delete this.queue[t.id]}),this.socket.addEventListener("error",t=>this.emit("error",t)),this.socket.addEventListener("close",({code:t,reason:n})=>{this.ready&&setTimeout(()=>this.emit("close",t,n),0),this.ready=!1,this.socket=void 0,t!==1e3&&(this.current_reconnects++,this.reconnect&&(this.max_reconnects>this.current_reconnects||this.max_reconnects===0)&&(this.reconnect_timer_id=setTimeout(()=>this._connect(r,e),this.reconnect_interval)))})}},um=class extends Fp{constructor(r="ws://localhost:8080",{autoconnect:e=!0,reconnect:t=!0,reconnect_interval:n=1e3,max_reconnects:i=5}={},o){super(Up,r,{autoconnect:e,reconnect:t,reconnect_interval:n,max_reconnects:i},o)}};hi.Client=um;hi.CommonClient=Fp;hi.DefaultDataPack=Pp;hi.WebSocket=Up});var Yp=Re(je=>{"use strict";z();Object.defineProperty(je,"__esModule",{value:!0});je.shake256=je.shake128=je.keccak_512=je.keccak_384=je.keccak_256=je.keccak_224=je.sha3_512=je.sha3_384=je.sha3_256=je.sha3_224=je.Keccak=void 0;je.keccakP=Wp;var Mn=$n(),pi=Js(),Lr=Nr(),Hp=[],Kp=[],qp=[],lm=BigInt(0),_i=BigInt(1),fm=BigInt(2),dm=BigInt(7),hm=BigInt(256),_m=BigInt(113);for(let r=0,e=_i,t=1,n=0;r<24;r++){[t,n]=[n,(2*t+3*n)%5],Hp.push(2*(5*n+t)),Kp.push((r+1)*(r+2)/2%64);let i=lm;for(let o=0;o<7;o++)e=(e<<_i^(e>>dm)*_m)%hm,e&fm&&(i^=_i<<(_i<<BigInt(o))-_i);qp.push(i)}var[pm,Rm]=(0,pi.split)(qp,!0),Vp=(r,e,t)=>t>32?(0,pi.rotlBH)(r,e,t):(0,pi.rotlSH)(r,e,t),Gp=(r,e,t)=>t>32?(0,pi.rotlBL)(r,e,t):(0,pi.rotlSL)(r,e,t);function Wp(r,e=24){let t=new Uint32Array(10);for(let n=24-e;n<24;n++){for(let s=0;s<10;s++)t[s]=r[s]^r[s+10]^r[s+20]^r[s+30]^r[s+40];for(let s=0;s<10;s+=2){let u=(s+8)%10,l=(s+2)%10,p=t[l],R=t[l+1],g=Vp(p,R,1)^t[u],v=Gp(p,R,1)^t[u+1];for(let m=0;m<50;m+=10)r[s+m]^=g,r[s+m+1]^=v}let i=r[2],o=r[3];for(let s=0;s<24;s++){let u=Kp[s],l=Vp(i,o,u),p=Gp(i,o,u),R=Hp[s];i=r[R],o=r[R+1],r[R]=l,r[R+1]=p}for(let s=0;s<50;s+=10){for(let u=0;u<10;u++)t[u]=r[s+u];for(let u=0;u<10;u++)r[s+u]^=~t[(u+2)%10]&t[(u+4)%10]}r[0]^=pm[n],r[1]^=Rm[n]}t.fill(0)}var Ri=class r extends Lr.Hash{constructor(e,t,n,i=!1,o=24){if(super(),this.blockLen=e,this.suffix=t,this.outputLen=n,this.enableXOF=i,this.rounds=o,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,(0,Mn.anumber)(n),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=(0,Lr.u32)(this.state)}keccak(){Lr.isLE||(0,Lr.byteSwap32)(this.state32),Wp(this.state32,this.rounds),Lr.isLE||(0,Lr.byteSwap32)(this.state32),this.posOut=0,this.pos=0}update(e){(0,Mn.aexists)(this);let{blockLen:t,state:n}=this;e=(0,Lr.toBytes)(e);let i=e.length;for(let o=0;o<i;){let s=Math.min(t-this.pos,i-o);for(let u=0;u<s;u++)n[this.pos++]^=e[o++];this.pos===t&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;let{state:e,suffix:t,pos:n,blockLen:i}=this;e[n]^=t,t&128&&n===i-1&&this.keccak(),e[i-1]^=128,this.keccak()}writeInto(e){(0,Mn.aexists)(this,!1),(0,Mn.abytes)(e),this.finish();let t=this.state,{blockLen:n}=this;for(let i=0,o=e.length;i<o;){this.posOut>=n&&this.keccak();let s=Math.min(n-this.posOut,o-i);e.set(t.subarray(this.posOut,this.posOut+s),i),this.posOut+=s,i+=s}return e}xofInto(e){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(e)}xof(e){return(0,Mn.anumber)(e),this.xofInto(new Uint8Array(e))}digestInto(e){if((0,Mn.aoutput)(e,this),this.finished)throw new Error("digest() was already called");return this.writeInto(e),this.destroy(),e}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(e){let{blockLen:t,suffix:n,outputLen:i,rounds:o,enableXOF:s}=this;return e||(e=new r(t,n,i,s,o)),e.state32.set(this.state32),e.pos=this.pos,e.posOut=this.posOut,e.finished=this.finished,e.rounds=o,e.suffix=n,e.outputLen=i,e.enableXOF=s,e.destroyed=this.destroyed,e}};je.Keccak=Ri;var Zr=(r,e,t)=>(0,Lr.wrapConstructor)(()=>new Ri(e,r,t));je.sha3_224=Zr(6,144,224/8);je.sha3_256=Zr(6,136,256/8);je.sha3_384=Zr(6,104,384/8);je.sha3_512=Zr(6,72,512/8);je.keccak_224=Zr(1,144,224/8);je.keccak_256=Zr(1,136,256/8);je.keccak_384=Zr(1,104,384/8);je.keccak_512=Zr(1,72,512/8);var $p=(r,e,t)=>(0,Lr.wrapXOFConstructorWithOpts)((n={})=>new Ri(e,r,n.dkLen===void 0?t:n.dkLen,!0));je.shake128=$p(31,168,128/8);je.shake256=$p(31,136,256/8)});var Xp=Re(_n=>{"use strict";z();Object.defineProperty(_n,"__esModule",{value:!0});_n.hmac=_n.HMAC=void 0;var Go=$n(),jp=Nr(),Ei=class extends jp.Hash{constructor(e,t){super(),this.finished=!1,this.destroyed=!1,(0,Go.ahash)(e);let n=(0,jp.toBytes)(t);if(this.iHash=e.create(),typeof this.iHash.update!="function")throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;let i=this.blockLen,o=new Uint8Array(i);o.set(n.length>i?e.create().update(n).digest():n);for(let s=0;s<o.length;s++)o[s]^=54;this.iHash.update(o),this.oHash=e.create();for(let s=0;s<o.length;s++)o[s]^=106;this.oHash.update(o),o.fill(0)}update(e){return(0,Go.aexists)(this),this.iHash.update(e),this}digestInto(e){(0,Go.aexists)(this),(0,Go.abytes)(e,this.outputLen),this.finished=!0,this.iHash.digestInto(e),this.oHash.update(e),this.oHash.digestInto(e),this.destroy()}digest(){let e=new Uint8Array(this.oHash.outputLen);return this.digestInto(e),e}_cloneInto(e){e||(e=Object.create(Object.getPrototypeOf(this),{}));let{oHash:t,iHash:n,finished:i,destroyed:o,blockLen:s,outputLen:u}=this;return e=e,e.finished=i,e.destroyed=o,e.blockLen=s,e.outputLen=u,e.oHash=t._cloneInto(e.oHash),e.iHash=n._cloneInto(e.iHash),e}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}};_n.HMAC=Ei;var Em=(r,e,t)=>new Ei(r,e).update(t).digest();_n.hmac=Em;_n.hmac.create=(r,e)=>new Ei(r,e)});var wc=Re(St=>{"use strict";z();Object.defineProperty(St,"__esModule",{value:!0});St.DER=St.DERErr=void 0;St.weierstrassPoints=Qp;St.weierstrass=Sm;St.SWUFpSqrtRatio=eR;St.mapToCurveSimpleSWU=bm;var Ho=Xi(),pn=zr(),be=ar(),vt=ar();function Zp(r){r.lowS!==void 0&&(0,vt.abool)("lowS",r.lowS),r.prehash!==void 0&&(0,vt.abool)("prehash",r.prehash)}function gm(r){let e=(0,Ho.validateBasic)(r);be.validateObject(e,{a:"field",b:"field"},{allowedPrivateKeyLengths:"array",wrapPrivateKey:"boolean",isTorsionFree:"function",clearCofactor:"function",allowInfinityPoint:"boolean",fromBytes:"function",toBytes:"function"});let{endo:t,Fp:n,a:i}=e;if(t){if(!n.eql(i,n.ZERO))throw new Error("invalid endomorphism, can only be defined for Koblitz curves that have a=0");if(typeof t!="object"||typeof t.beta!="bigint"||typeof t.splitScalar!="function")throw new Error("invalid endomorphism, expected beta: bigint and splitScalar: function")}return Object.freeze({...e})}var{bytesToNumberBE:ym,hexToBytes:Am}=be,Ko=class extends Error{constructor(e=""){super(e)}};St.DERErr=Ko;St.DER={Err:Ko,_tlv:{encode:(r,e)=>{let{Err:t}=St.DER;if(r<0||r>256)throw new t("tlv.encode: wrong tag");if(e.length&1)throw new t("tlv.encode: unpadded data");let n=e.length/2,i=be.numberToHexUnpadded(n);if(i.length/2&128)throw new t("tlv.encode: long form length too big");let o=n>127?be.numberToHexUnpadded(i.length/2|128):"";return be.numberToHexUnpadded(r)+o+i+e},decode(r,e){let{Err:t}=St.DER,n=0;if(r<0||r>256)throw new t("tlv.encode: wrong tag");if(e.length<2||e[n++]!==r)throw new t("tlv.decode: wrong tlv");let i=e[n++],o=!!(i&128),s=0;if(!o)s=i;else{let l=i&127;if(!l)throw new t("tlv.decode(long): indefinite length not supported");if(l>4)throw new t("tlv.decode(long): byte length is too big");let p=e.subarray(n,n+l);if(p.length!==l)throw new t("tlv.decode: length bytes not complete");if(p[0]===0)throw new t("tlv.decode(long): zero leftmost byte");for(let R of p)s=s<<8|R;if(n+=l,s<128)throw new t("tlv.decode(long): not minimal encoding")}let u=e.subarray(n,n+s);if(u.length!==s)throw new t("tlv.decode: wrong value length");return{v:u,l:e.subarray(n+s)}}},_int:{encode(r){let{Err:e}=St.DER;if(r<fr)throw new e("integer: negative integers are not allowed");let t=be.numberToHexUnpadded(r);if(Number.parseInt(t[0],16)&8&&(t="00"+t),t.length&1)throw new e("unexpected DER parsing assertion: unpadded hex");return t},decode(r){let{Err:e}=St.DER;if(r[0]&128)throw new e("invalid signature integer: negative");if(r[0]===0&&!(r[1]&128))throw new e("invalid signature integer: unnecessary leading zero");return ym(r)}},toSig(r){let{Err:e,_int:t,_tlv:n}=St.DER,i=typeof r=="string"?Am(r):r;be.abytes(i);let{v:o,l:s}=n.decode(48,i);if(s.length)throw new e("invalid signature: left bytes after parsing");let{v:u,l}=n.decode(2,o),{v:p,l:R}=n.decode(2,l);if(R.length)throw new e("invalid signature: left bytes after parsing");return{r:t.decode(u),s:t.decode(p)}},hexFromSig(r){let{_tlv:e,_int:t}=St.DER,n=e.encode(2,t.encode(r.r)),i=e.encode(2,t.encode(r.s)),o=n+i;return e.encode(48,o)}};var fr=BigInt(0),Fe=BigInt(1),Jr=BigInt(2),qo=BigInt(3),Jp=BigInt(4);function Qp(r){let e=gm(r),{Fp:t}=e,n=(0,pn.Field)(e.n,e.nBitLength),i=e.toBytes||((I,O,D)=>{let k=O.toAffine();return be.concatBytes(Uint8Array.from([4]),t.toBytes(k.x),t.toBytes(k.y))}),o=e.fromBytes||(I=>{let O=I.subarray(1),D=t.fromBytes(O.subarray(0,t.BYTES)),k=t.fromBytes(O.subarray(t.BYTES,2*t.BYTES));return{x:D,y:k}});function s(I){let{a:O,b:D}=e,k=t.sqr(I),B=t.mul(k,I);return t.add(t.add(B,t.mul(I,O)),D)}if(!t.eql(t.sqr(e.Gy),s(e.Gx)))throw new Error("bad generator point: equation left != right");function u(I){return be.inRange(I,Fe,e.n)}function l(I){let{allowedPrivateKeyLengths:O,nByteLength:D,wrapPrivateKey:k,n:B}=e;if(O&&typeof I!="bigint"){if(be.isBytes(I)&&(I=be.bytesToHex(I)),typeof I!="string"||!O.includes(I.length))throw new Error("invalid private key");I=I.padStart(D*2,"0")}let F;try{F=typeof I=="bigint"?I:be.bytesToNumberBE((0,vt.ensureBytes)("private key",I,D))}catch{throw new Error("invalid private key, expected hex or "+D+" bytes, got "+typeof I)}return k&&(F=(0,pn.mod)(F,B)),be.aInRange("private key",F,Fe,B),F}function p(I){if(!(I instanceof v))throw new Error("ProjectivePoint expected")}let R=(0,vt.memoized)((I,O)=>{let{px:D,py:k,pz:B}=I;if(t.eql(B,t.ONE))return{x:D,y:k};let F=I.is0();O==null&&(O=F?t.ONE:t.inv(B));let $=t.mul(D,O),H=t.mul(k,O),K=t.mul(B,O);if(F)return{x:t.ZERO,y:t.ZERO};if(!t.eql(K,t.ONE))throw new Error("invZ was invalid");return{x:$,y:H}}),g=(0,vt.memoized)(I=>{if(I.is0()){if(e.allowInfinityPoint&&!t.is0(I.py))return;throw new Error("bad point: ZERO")}let{x:O,y:D}=I.toAffine();if(!t.isValid(O)||!t.isValid(D))throw new Error("bad point: x or y not FE");let k=t.sqr(D),B=s(O);if(!t.eql(k,B))throw new Error("bad point: equation left != right");if(!I.isTorsionFree())throw new Error("bad point: not in prime-order subgroup");return!0});class v{constructor(O,D,k){if(this.px=O,this.py=D,this.pz=k,O==null||!t.isValid(O))throw new Error("x required");if(D==null||!t.isValid(D))throw new Error("y required");if(k==null||!t.isValid(k))throw new Error("z required");Object.freeze(this)}static fromAffine(O){let{x:D,y:k}=O||{};if(!O||!t.isValid(D)||!t.isValid(k))throw new Error("invalid affine point");if(O instanceof v)throw new Error("projective point not allowed");let B=F=>t.eql(F,t.ZERO);return B(D)&&B(k)?v.ZERO:new v(D,k,t.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(O){let D=t.invertBatch(O.map(k=>k.pz));return O.map((k,B)=>k.toAffine(D[B])).map(v.fromAffine)}static fromHex(O){let D=v.fromAffine(o((0,vt.ensureBytes)("pointHex",O)));return D.assertValidity(),D}static fromPrivateKey(O){return v.BASE.multiply(l(O))}static msm(O,D){return(0,Ho.pippenger)(v,n,O,D)}_setWindowSize(O){x.setWindowSize(this,O)}assertValidity(){g(this)}hasEvenY(){let{y:O}=this.toAffine();if(t.isOdd)return!t.isOdd(O);throw new Error("Field doesn't support isOdd")}equals(O){p(O);let{px:D,py:k,pz:B}=this,{px:F,py:$,pz:H}=O,K=t.eql(t.mul(D,H),t.mul(F,B)),j=t.eql(t.mul(k,H),t.mul($,B));return K&&j}negate(){return new v(this.px,t.neg(this.py),this.pz)}double(){let{a:O,b:D}=e,k=t.mul(D,qo),{px:B,py:F,pz:$}=this,H=t.ZERO,K=t.ZERO,j=t.ZERO,J=t.mul(B,B),me=t.mul(F,F),ee=t.mul($,$),ae=t.mul(B,F);return ae=t.add(ae,ae),j=t.mul(B,$),j=t.add(j,j),H=t.mul(O,j),K=t.mul(k,ee),K=t.add(H,K),H=t.sub(me,K),K=t.add(me,K),K=t.mul(H,K),H=t.mul(ae,H),j=t.mul(k,j),ee=t.mul(O,ee),ae=t.sub(J,ee),ae=t.mul(O,ae),ae=t.add(ae,j),j=t.add(J,J),J=t.add(j,J),J=t.add(J,ee),J=t.mul(J,ae),K=t.add(K,J),ee=t.mul(F,$),ee=t.add(ee,ee),J=t.mul(ee,ae),H=t.sub(H,J),j=t.mul(ee,me),j=t.add(j,j),j=t.add(j,j),new v(H,K,j)}add(O){p(O);let{px:D,py:k,pz:B}=this,{px:F,py:$,pz:H}=O,K=t.ZERO,j=t.ZERO,J=t.ZERO,me=e.a,ee=t.mul(e.b,qo),ae=t.mul(D,F),b=t.mul(k,$),a=t.mul(B,H),d=t.add(D,k),h=t.add(F,$);d=t.mul(d,h),h=t.add(ae,b),d=t.sub(d,h),h=t.add(D,B);let _=t.add(F,H);return h=t.mul(h,_),_=t.add(ae,a),h=t.sub(h,_),_=t.add(k,B),K=t.add($,H),_=t.mul(_,K),K=t.add(b,a),_=t.sub(_,K),J=t.mul(me,h),K=t.mul(ee,a),J=t.add(K,J),K=t.sub(b,J),J=t.add(b,J),j=t.mul(K,J),b=t.add(ae,ae),b=t.add(b,ae),a=t.mul(me,a),h=t.mul(ee,h),b=t.add(b,a),a=t.sub(ae,a),a=t.mul(me,a),h=t.add(h,a),ae=t.mul(b,h),j=t.add(j,ae),ae=t.mul(_,h),K=t.mul(d,K),K=t.sub(K,ae),ae=t.mul(d,b),J=t.mul(_,J),J=t.add(J,ae),new v(K,j,J)}subtract(O){return this.add(O.negate())}is0(){return this.equals(v.ZERO)}wNAF(O){return x.wNAFCached(this,O,v.normalizeZ)}multiplyUnsafe(O){let{endo:D,n:k}=e;be.aInRange("scalar",O,fr,k);let B=v.ZERO;if(O===fr)return B;if(this.is0()||O===Fe)return this;if(!D||x.hasPrecomputes(this))return x.wNAFCachedUnsafe(this,O,v.normalizeZ);let{k1neg:F,k1:$,k2neg:H,k2:K}=D.splitScalar(O),j=B,J=B,me=this;for(;$>fr||K>fr;)$&Fe&&(j=j.add(me)),K&Fe&&(J=J.add(me)),me=me.double(),$>>=Fe,K>>=Fe;return F&&(j=j.negate()),H&&(J=J.negate()),J=new v(t.mul(J.px,D.beta),J.py,J.pz),j.add(J)}multiply(O){let{endo:D,n:k}=e;be.aInRange("scalar",O,Fe,k);let B,F;if(D){let{k1neg:$,k1:H,k2neg:K,k2:j}=D.splitScalar(O),{p:J,f:me}=this.wNAF(H),{p:ee,f:ae}=this.wNAF(j);J=x.constTimeNegate($,J),ee=x.constTimeNegate(K,ee),ee=new v(t.mul(ee.px,D.beta),ee.py,ee.pz),B=J.add(ee),F=me.add(ae)}else{let{p:$,f:H}=this.wNAF(O);B=$,F=H}return v.normalizeZ([B,F])[0]}multiplyAndAddUnsafe(O,D,k){let B=v.BASE,F=(H,K)=>K===fr||K===Fe||!H.equals(B)?H.multiplyUnsafe(K):H.multiply(K),$=F(this,D).add(F(O,k));return $.is0()?void 0:$}toAffine(O){return R(this,O)}isTorsionFree(){let{h:O,isTorsionFree:D}=e;if(O===Fe)return!0;if(D)return D(v,this);throw new Error("isTorsionFree() has not been declared for the elliptic curve")}clearCofactor(){let{h:O,clearCofactor:D}=e;return O===Fe?this:D?D(v,this):this.multiplyUnsafe(e.h)}toRawBytes(O=!0){return(0,vt.abool)("isCompressed",O),this.assertValidity(),i(v,this,O)}toHex(O=!0){return(0,vt.abool)("isCompressed",O),be.bytesToHex(this.toRawBytes(O))}}v.BASE=new v(e.Gx,e.Gy,t.ONE),v.ZERO=new v(t.ZERO,t.ONE,t.ZERO);let m=e.nBitLength,x=(0,Ho.wNAF)(v,e.endo?Math.ceil(m/2):m);return{CURVE:e,ProjectivePoint:v,normPrivateKeyToScalar:l,weierstrassEquation:s,isWithinCurveOrder:u}}function mm(r){let e=(0,Ho.validateBasic)(r);return be.validateObject(e,{hash:"hash",hmac:"function",randomBytes:"function"},{bits2int:"function",bits2int_modN:"function",lowS:"boolean"}),Object.freeze({lowS:!0,...e})}function Sm(r){let e=mm(r),{Fp:t,n}=e,i=t.BYTES+1,o=2*t.BYTES+1;function s(a){return(0,pn.mod)(a,n)}function u(a){return(0,pn.invert)(a,n)}let{ProjectivePoint:l,normPrivateKeyToScalar:p,weierstrassEquation:R,isWithinCurveOrder:g}=Qp({...e,toBytes(a,d,h){let _=d.toAffine(),E=t.toBytes(_.x),S=be.concatBytes;return(0,vt.abool)("isCompressed",h),h?S(Uint8Array.from([d.hasEvenY()?2:3]),E):S(Uint8Array.from([4]),E,t.toBytes(_.y))},fromBytes(a){let d=a.length,h=a[0],_=a.subarray(1);if(d===i&&(h===2||h===3)){let E=be.bytesToNumberBE(_);if(!be.inRange(E,Fe,t.ORDER))throw new Error("Point is not on curve");let S=R(E),C;try{C=t.sqrt(S)}catch(A){let G=A instanceof Error?": "+A.message:"";throw new Error("Point is not on curve"+G)}let y=(C&Fe)===Fe;return(h&1)===1!==y&&(C=t.neg(C)),{x:E,y:C}}else if(d===o&&h===4){let E=t.fromBytes(_.subarray(0,t.BYTES)),S=t.fromBytes(_.subarray(t.BYTES,2*t.BYTES));return{x:E,y:S}}else{let E=i,S=o;throw new Error("invalid Point, expected length of "+E+", or uncompressed "+S+", got "+d)}}}),v=a=>be.bytesToHex(be.numberToBytesBE(a,e.nByteLength));function m(a){let d=n>>Fe;return a>d}function x(a){return m(a)?s(-a):a}let I=(a,d,h)=>be.bytesToNumberBE(a.slice(d,h));class O{constructor(d,h,_){this.r=d,this.s=h,this.recovery=_,this.assertValidity()}static fromCompact(d){let h=e.nByteLength;return d=(0,vt.ensureBytes)("compactSignature",d,h*2),new O(I(d,0,h),I(d,h,2*h))}static fromDER(d){let{r:h,s:_}=St.DER.toSig((0,vt.ensureBytes)("DER",d));return new O(h,_)}assertValidity(){be.aInRange("r",this.r,Fe,n),be.aInRange("s",this.s,Fe,n)}addRecoveryBit(d){return new O(this.r,this.s,d)}recoverPublicKey(d){let{r:h,s:_,recovery:E}=this,S=H((0,vt.ensureBytes)("msgHash",d));if(E==null||![0,1,2,3].includes(E))throw new Error("recovery id invalid");let C=E===2||E===3?h+e.n:h;if(C>=t.ORDER)throw new Error("recovery id 2 or 3 invalid");let y=E&1?"03":"02",f=l.fromHex(y+v(C)),A=u(C),G=s(-S*A),q=s(_*A),Y=l.BASE.multiplyAndAddUnsafe(f,G,q);if(!Y)throw new Error("point at infinify");return Y.assertValidity(),Y}hasHighS(){return m(this.s)}normalizeS(){return this.hasHighS()?new O(this.r,s(-this.s),this.recovery):this}toDERRawBytes(){return be.hexToBytes(this.toDERHex())}toDERHex(){return St.DER.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return be.hexToBytes(this.toCompactHex())}toCompactHex(){return v(this.r)+v(this.s)}}let D={isValidPrivateKey(a){try{return p(a),!0}catch{return!1}},normPrivateKeyToScalar:p,randomPrivateKey:()=>{let a=(0,pn.getMinHashLength)(e.n);return(0,pn.mapHashToField)(e.randomBytes(a),e.n)},precompute(a=8,d=l.BASE){return d._setWindowSize(a),d.multiply(BigInt(3)),d}};function k(a,d=!0){return l.fromPrivateKey(a).toRawBytes(d)}function B(a){let d=be.isBytes(a),h=typeof a=="string",_=(d||h)&&a.length;return d?_===i||_===o:h?_===2*i||_===2*o:a instanceof l}function F(a,d,h=!0){if(B(a))throw new Error("first arg must be private key");if(!B(d))throw new Error("second arg must be public key");return l.fromHex(d).multiply(p(a)).toRawBytes(h)}let $=e.bits2int||function(a){if(a.length>8192)throw new Error("input is too large");let d=be.bytesToNumberBE(a),h=a.length*8-e.nBitLength;return h>0?d>>BigInt(h):d},H=e.bits2int_modN||function(a){return s($(a))},K=be.bitMask(e.nBitLength);function j(a){return be.aInRange("num < 2^"+e.nBitLength,a,fr,K),be.numberToBytesBE(a,e.nByteLength)}function J(a,d,h=me){if(["recovered","canonical"].some(oe=>oe in h))throw new Error("sign() legacy options not supported");let{hash:_,randomBytes:E}=e,{lowS:S,prehash:C,extraEntropy:y}=h;S==null&&(S=!0),a=(0,vt.ensureBytes)("msgHash",a),Zp(h),C&&(a=(0,vt.ensureBytes)("prehashed msgHash",_(a)));let f=H(a),A=p(d),G=[j(A),j(f)];if(y!=null&&y!==!1){let oe=y===!0?E(t.BYTES):y;G.push((0,vt.ensureBytes)("extraEntropy",oe))}let q=be.concatBytes(...G),Y=f;function te(oe){let ce=$(oe);if(!g(ce))return;let Ue=u(ce),he=l.BASE.multiply(ce).toAffine(),ue=s(he.x);if(ue===fr)return;let zt=s(Ue*s(Y+ue*A));if(zt===fr)return;let we=(he.x===ue?0:2)|Number(he.y&Fe),Le=zt;return S&&m(zt)&&(Le=x(zt),we^=1),new O(ue,Le,we)}return{seed:q,k2sig:te}}let me={lowS:e.lowS,prehash:!1},ee={lowS:e.lowS,prehash:!1};function ae(a,d,h=me){let{seed:_,k2sig:E}=J(a,d,h),S=e;return be.createHmacDrbg(S.hash.outputLen,S.nByteLength,S.hmac)(_,E)}l.BASE._setWindowSize(8);function b(a,d,h,_=ee){let E=a;d=(0,vt.ensureBytes)("msgHash",d),h=(0,vt.ensureBytes)("publicKey",h);let{lowS:S,prehash:C,format:y}=_;if(Zp(_),"strict"in _)throw new Error("options.strict was renamed to lowS");if(y!==void 0&&y!=="compact"&&y!=="der")throw new Error("format must be compact or der");let f=typeof E=="string"||be.isBytes(E),A=!f&&!y&&typeof E=="object"&&E!==null&&typeof E.r=="bigint"&&typeof E.s=="bigint";if(!f&&!A)throw new Error("invalid signature, expected Uint8Array, hex string or Signature instance");let G,q;try{if(A&&(G=new O(E.r,E.s)),f){try{y!=="compact"&&(G=O.fromDER(E))}catch(we){if(!(we instanceof St.DER.Err))throw we}!G&&y!=="der"&&(G=O.fromCompact(E))}q=l.fromHex(h)}catch{return!1}if(!G||S&&G.hasHighS())return!1;C&&(d=e.hash(d));let{r:Y,s:te}=G,oe=H(d),ce=u(te),Ue=s(oe*ce),he=s(Y*ce),ue=l.BASE.multiplyAndAddUnsafe(q,Ue,he)?.toAffine();return ue?s(ue.x)===Y:!1}return{CURVE:e,getPublicKey:k,getSharedSecret:F,sign:ae,verify:b,ProjectivePoint:l,Signature:O,utils:D}}function eR(r,e){let t=r.ORDER,n=fr;for(let x=t-Fe;x%Jr===fr;x/=Jr)n+=Fe;let i=n,o=Jr<<i-Fe-Fe,s=o*Jr,u=(t-Fe)/s,l=(u-Fe)/Jr,p=s-Fe,R=o,g=r.pow(e,u),v=r.pow(e,(u+Fe)/Jr),m=(x,I)=>{let O=g,D=r.pow(I,p),k=r.sqr(D);k=r.mul(k,I);let B=r.mul(x,k);B=r.pow(B,l),B=r.mul(B,D),D=r.mul(B,I),k=r.mul(B,x);let F=r.mul(k,D);B=r.pow(F,R);let $=r.eql(B,r.ONE);D=r.mul(k,v),B=r.mul(F,O),k=r.cmov(D,k,$),F=r.cmov(B,F,$);for(let H=i;H>Fe;H--){let K=H-Jr;K=Jr<<K-Fe;let j=r.pow(F,K),J=r.eql(j,r.ONE);D=r.mul(k,O),O=r.mul(O,O),j=r.mul(F,O),k=r.cmov(D,k,J),F=r.cmov(j,F,J)}return{isValid:$,value:k}};if(r.ORDER%Jp===qo){let x=(r.ORDER-qo)/Jp,I=r.sqrt(r.neg(e));m=(O,D)=>{let k=r.sqr(D),B=r.mul(O,D);k=r.mul(k,B);let F=r.pow(k,x);F=r.mul(F,B);let $=r.mul(F,I),H=r.mul(r.sqr(F),D),K=r.eql(H,O),j=r.cmov($,F,K);return{isValid:K,value:j}}}return m}function bm(r,e){if((0,pn.validateField)(r),!r.isValid(e.A)||!r.isValid(e.B)||!r.isValid(e.Z))throw new Error("mapToCurveSimpleSWU: invalid opts");let t=eR(r,e.Z);if(!r.isOdd)throw new Error("Fp.isOdd is not implemented!");return n=>{let i,o,s,u,l,p,R,g;i=r.sqr(n),i=r.mul(i,e.Z),o=r.sqr(i),o=r.add(o,i),s=r.add(o,r.ONE),s=r.mul(s,e.B),u=r.cmov(e.Z,r.neg(o),!r.eql(o,r.ZERO)),u=r.mul(u,e.A),o=r.sqr(s),p=r.sqr(u),l=r.mul(p,e.A),o=r.add(o,l),o=r.mul(o,s),p=r.mul(p,u),l=r.mul(p,e.B),o=r.add(o,l),R=r.mul(i,s);let{isValid:v,value:m}=t(o,p);g=r.mul(i,n),g=r.mul(g,m),R=r.cmov(R,s,v),g=r.cmov(g,m,v);let x=r.isOdd(n)===r.isOdd(g);return g=r.cmov(r.neg(g),g,x),R=r.div(R,u),{x:R,y:g}}}});var nR=Re(Wo=>{"use strict";z();Object.defineProperty(Wo,"__esModule",{value:!0});Wo.getHash=rR;Wo.createCurve=Im;var Om=Xp(),tR=Nr(),wm=wc();function rR(r){return{hash:r,hmac:(e,...t)=>(0,Om.hmac)(r,e,(0,tR.concatBytes)(...t)),randomBytes:tR.randomBytes}}function Im(r,e){let t=n=>(0,wm.weierstrass)({...r,...rR(n)});return{...t(e),create:t}}});var hR=Re(jt=>{"use strict";z();Object.defineProperty(jt,"__esModule",{value:!0});jt.encodeToCurve=jt.hashToCurve=jt.schnorr=jt.secp256k1=void 0;var $o=Ca(),Nm=Nr(),Tm=nR(),sR=_a(),wt=zr(),Yt=ar(),vm=wc(),Ai=BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"),Yo=BigInt("0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"),gi=BigInt(1),jo=BigInt(2),iR=(r,e)=>(r+e/jo)/e;function aR(r){let e=Ai,t=BigInt(3),n=BigInt(6),i=BigInt(11),o=BigInt(22),s=BigInt(23),u=BigInt(44),l=BigInt(88),p=r*r*r%e,R=p*p*r%e,g=(0,wt.pow2)(R,t,e)*R%e,v=(0,wt.pow2)(g,t,e)*R%e,m=(0,wt.pow2)(v,jo,e)*p%e,x=(0,wt.pow2)(m,i,e)*m%e,I=(0,wt.pow2)(x,o,e)*x%e,O=(0,wt.pow2)(I,u,e)*I%e,D=(0,wt.pow2)(O,l,e)*O%e,k=(0,wt.pow2)(D,u,e)*I%e,B=(0,wt.pow2)(k,t,e)*R%e,F=(0,wt.pow2)(B,s,e)*x%e,$=(0,wt.pow2)(F,n,e)*p%e,H=(0,wt.pow2)($,jo,e);if(!Qr.eql(Qr.sqr(H),r))throw new Error("Cannot find square root");return H}var Qr=(0,wt.Field)(Ai,void 0,void 0,{sqrt:aR});jt.secp256k1=(0,Tm.createCurve)({a:BigInt(0),b:BigInt(7),Fp:Qr,n:Yo,Gx:BigInt("55066263022277343669578718895168534326250603453777594175500187360389116729240"),Gy:BigInt("32670510020758816978083085130507043184471273380659243275938904335757337482424"),h:BigInt(1),lowS:!0,endo:{beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee"),splitScalar:r=>{let e=Yo,t=BigInt("0x3086d221a7d46bcde86c90e49284eb15"),n=-gi*BigInt("0xe4437ed6010e88286f547fa90abfe4c3"),i=BigInt("0x114ca50f7a8e2f3f657c1108d9d44cfd8"),o=t,s=BigInt("0x100000000000000000000000000000000"),u=iR(o*r,e),l=iR(-n*r,e),p=(0,wt.mod)(r-u*t-l*i,e),R=(0,wt.mod)(-u*n-l*o,e),g=p>s,v=R>s;if(g&&(p=e-p),v&&(R=e-R),p>s||R>s)throw new Error("splitScalar: Endomorphism failed, k="+r);return{k1neg:g,k1:p,k2neg:v,k2:R}}}},$o.sha256);var cR=BigInt(0),oR={};function Xo(r,...e){let t=oR[r];if(t===void 0){let n=(0,$o.sha256)(Uint8Array.from(r,i=>i.charCodeAt(0)));t=(0,Yt.concatBytes)(n,n),oR[r]=t}return(0,$o.sha256)((0,Yt.concatBytes)(t,...e))}var vc=r=>r.toRawBytes(!0).slice(1),Nc=r=>(0,Yt.numberToBytesBE)(r,32),Ic=r=>(0,wt.mod)(r,Ai),yi=r=>(0,wt.mod)(r,Yo),Cc=jt.secp256k1.ProjectivePoint,Cm=(r,e,t)=>Cc.BASE.multiplyAndAddUnsafe(r,e,t);function Tc(r){let e=jt.secp256k1.utils.normPrivateKeyToScalar(r),t=Cc.fromPrivateKey(e);return{scalar:t.hasEvenY()?e:yi(-e),bytes:vc(t)}}function uR(r){(0,Yt.aInRange)("x",r,gi,Ai);let e=Ic(r*r),t=Ic(e*r+BigInt(7)),n=aR(t);n%jo!==cR&&(n=Ic(-n));let i=new Cc(r,n,gi);return i.assertValidity(),i}var Un=Yt.bytesToNumberBE;function lR(...r){return yi(Un(Xo("BIP0340/challenge",...r)))}function xm(r){return Tc(r).bytes}function Lm(r,e,t=(0,Nm.randomBytes)(32)){let n=(0,Yt.ensureBytes)("message",r),{bytes:i,scalar:o}=Tc(e),s=(0,Yt.ensureBytes)("auxRand",t,32),u=Nc(o^Un(Xo("BIP0340/aux",s))),l=Xo("BIP0340/nonce",u,i,n),p=yi(Un(l));if(p===cR)throw new Error("sign failed: k is zero");let{bytes:R,scalar:g}=Tc(p),v=lR(R,i,n),m=new Uint8Array(64);if(m.set(R,0),m.set(Nc(yi(g+v*o)),32),!fR(m,n,i))throw new Error("sign: Invalid signature produced");return m}function fR(r,e,t){let n=(0,Yt.ensureBytes)("signature",r,64),i=(0,Yt.ensureBytes)("message",e),o=(0,Yt.ensureBytes)("publicKey",t,32);try{let s=uR(Un(o)),u=Un(n.subarray(0,32));if(!(0,Yt.inRange)(u,gi,Ai))return!1;let l=Un(n.subarray(32,64));if(!(0,Yt.inRange)(l,gi,Yo))return!1;let p=lR(Nc(u),vc(s),i),R=Cm(s,l,yi(-p));return!(!R||!R.hasEvenY()||R.toAffine().x!==u)}catch{return!1}}jt.schnorr={getPublicKey:xm,sign:Lm,verify:fR,utils:{randomPrivateKey:jt.secp256k1.utils.randomPrivateKey,lift_x:uR,pointToBytes:vc,numberToBytesBE:Yt.numberToBytesBE,bytesToNumberBE:Yt.bytesToNumberBE,taggedHash:Xo,mod:wt.mod}};var km=(0,sR.isogenyMap)(Qr,[["0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7","0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581","0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262","0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c"],["0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b","0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14","0x0000000000000000000000000000000000000000000000000000000000000001"],["0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c","0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3","0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931","0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84"],["0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b","0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573","0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f","0x0000000000000000000000000000000000000000000000000000000000000001"]].map(r=>r.map(e=>BigInt(e)))),Bm=(0,vm.mapToCurveSimpleSWU)(Qr,{A:BigInt("0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533"),B:BigInt("1771"),Z:Qr.create(BigInt("-11"))}),dR=(0,sR.createHasher)(jt.secp256k1.ProjectivePoint,r=>{let{x:e,y:t}=Bm(Qr.create(r[0]));return km(e,t)},{DST:"secp256k1_XMD:SHA-256_SSWU_RO_",encodeDST:"secp256k1_XMD:SHA-256_SSWU_NU_",p:Qr.ORDER,m:1,k:128,expand:"xmd",hash:$o.sha256});jt.hashToCurve=dR.hashToCurve;jt.encodeToCurve=dR.encodeToCurve});var oE=Re(Z=>{"use strict";z();var se=Tn(),Kn=ef(),Dm=wa(),Mm=uf(),_R=Ca(),xc=yf(),MR=Af(),UR=ap(),c=up(),Um=kp(),pR=zp(),RR=Yp(),nu=hR();function iu(r){return r&&typeof r=="object"&&"default"in r?r:{default:r}}function Pm(r){if(r&&typeof r=="object"&&"default"in r)return r;var e=Object.create(null);return r&&Object.keys(r).forEach(function(t){if(t!=="default"){var n=Object.getOwnPropertyDescriptor(r,t);Object.defineProperty(e,t,n.get?n:{enumerable:!0,get:function(){return r[t]}})}}),e.default=r,Object.freeze(e)}var ER=iu(Dm),Ct=iu(Mm),w=Pm(MR),Fm=iu(Um),zm=Kn.ed25519.utils.randomPrivateKey,gR=()=>{let r=Kn.ed25519.utils.randomPrivateKey(),e=es(r),t=new Uint8Array(64);return t.set(r),t.set(e,32),{publicKey:e,secretKey:t}},es=Kn.ed25519.getPublicKey;function yR(r){try{return Kn.ed25519.ExtendedPoint.fromHex(r),!0}catch{return!1}}var ou=(r,e)=>Kn.ed25519.sign(r,e.slice(0,32)),Vm=Kn.ed25519.verify,Ae=r=>se.Buffer.isBuffer(r)?r:r instanceof Uint8Array?se.Buffer.from(r.buffer,r.byteOffset,r.byteLength):se.Buffer.from(r),bi=class{constructor(e){Object.assign(this,e)}encode(){return se.Buffer.from(xc.serialize(Si,this))}static decode(e){return xc.deserialize(Si,this,e)}static decodeUnchecked(e){return xc.deserializeUnchecked(Si,this,e)}},Pc=class extends bi{constructor(e){if(super(e),this.enum="",Object.keys(e).length!==1)throw new Error("Enum can only take single value");Object.keys(e).map(t=>{this.enum=t})}},Si=new Map,PR,FR=32,br=32;function Gm(r){return r._bn!==void 0}var AR=1,X=class r extends bi{constructor(e){if(super({}),this._bn=void 0,Gm(e))this._bn=e._bn;else{if(typeof e=="string"){let t=Ct.default.decode(e);if(t.length!=br)throw new Error("Invalid public key input");this._bn=new ER.default(t)}else this._bn=new ER.default(e);if(this._bn.byteLength()>br)throw new Error("Invalid public key input")}}static unique(){let e=new r(AR);return AR+=1,new r(e.toBuffer())}equals(e){return this._bn.eq(e._bn)}toBase58(){return Ct.default.encode(this.toBytes())}toJSON(){return this.toBase58()}toBytes(){let e=this.toBuffer();return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}toBuffer(){let e=this._bn.toArrayLike(se.Buffer);if(e.length===br)return e;let t=se.Buffer.alloc(32);return e.copy(t,32-e.length),t}get[Symbol.toStringTag](){return`PublicKey(${this.toString()})`}toString(){return this.toBase58()}static async createWithSeed(e,t,n){let i=se.Buffer.concat([e.toBuffer(),se.Buffer.from(t),n.toBuffer()]),o=_R.sha256(i);return new r(o)}static createProgramAddressSync(e,t){let n=se.Buffer.alloc(0);e.forEach(function(o){if(o.length>FR)throw new TypeError("Max seed length exceeded");n=se.Buffer.concat([n,Ae(o)])}),n=se.Buffer.concat([n,t.toBuffer(),se.Buffer.from("ProgramDerivedAddress")]);let i=_R.sha256(n);if(yR(i))throw new Error("Invalid seeds, address must fall off the curve");return new r(i)}static async createProgramAddress(e,t){return this.createProgramAddressSync(e,t)}static findProgramAddressSync(e,t){let n=255,i;for(;n!=0;){try{let o=e.concat(se.Buffer.from([n]));i=this.createProgramAddressSync(o,t)}catch(o){if(o instanceof TypeError)throw o;n--;continue}return[i,n]}throw new Error("Unable to find a viable program address nonce")}static async findProgramAddress(e,t){return this.findProgramAddressSync(e,t)}static isOnCurve(e){let t=new r(e);return yR(t.toBytes())}};PR=X;X.default=new PR("11111111111111111111111111111111");Si.set(X,{kind:"struct",fields:[["_bn","u256"]]});var Fc=class{constructor(e){if(this._publicKey=void 0,this._secretKey=void 0,e){let t=Ae(e);if(e.length!==64)throw new Error("bad secret key size");this._publicKey=t.slice(32,64),this._secretKey=t.slice(0,32)}else this._secretKey=Ae(zm()),this._publicKey=Ae(es(this._secretKey))}get publicKey(){return new X(this._publicKey)}get secretKey(){return se.Buffer.concat([this._secretKey,this._publicKey],64)}},Hm=new X("BPFLoader1111111111111111111111111111111111"),tn=1232,ls=127,Oi=64,wi=class extends Error{constructor(e){super(`Signature ${e} has expired: block height exceeded.`),this.signature=void 0,this.signature=e}};Object.defineProperty(wi.prototype,"name",{value:"TransactionExpiredBlockheightExceededError"});var Ii=class extends Error{constructor(e,t){super(`Transaction was not confirmed in ${t.toFixed(2)} seconds. It is unknown if it succeeded or failed. Check signature ${e} using the Solana Explorer or CLI tools.`),this.signature=void 0,this.signature=e}};Object.defineProperty(Ii.prototype,"name",{value:"TransactionExpiredTimeoutError"});var en=class extends Error{constructor(e){super(`Signature ${e} has expired: the nonce is no longer valid.`),this.signature=void 0,this.signature=e}};Object.defineProperty(en.prototype,"name",{value:"TransactionExpiredNonceInvalidError"});var gn=class{constructor(e,t){this.staticAccountKeys=void 0,this.accountKeysFromLookups=void 0,this.staticAccountKeys=e,this.accountKeysFromLookups=t}keySegments(){let e=[this.staticAccountKeys];return this.accountKeysFromLookups&&(e.push(this.accountKeysFromLookups.writable),e.push(this.accountKeysFromLookups.readonly)),e}get(e){for(let t of this.keySegments()){if(e<t.length)return t[e];e-=t.length}}get length(){return this.keySegments().flat().length}compileInstructions(e){if(this.length>256)throw new Error("Account index overflow encountered during compilation");let n=new Map;this.keySegments().flat().forEach((o,s)=>{n.set(o.toBase58(),s)});let i=o=>{let s=n.get(o.toBase58());if(s===void 0)throw new Error("Encountered an unknown instruction account key during compilation");return s};return e.map(o=>({programIdIndex:i(o.programId),accountKeyIndexes:o.keys.map(s=>i(s.pubkey)),data:o.data}))}},Se=(r="publicKey")=>w.blob(32,r),Km=(r="signature")=>w.blob(64,r),En=(r="string")=>{let e=w.struct([w.u32("length"),w.u32("lengthPadding"),w.blob(w.offset(w.u32(),-8),"chars")],r),t=e.decode.bind(e),n=e.encode.bind(e),i=e;return i.decode=(o,s)=>t(o,s).chars.toString(),i.encode=(o,s,u)=>{let l={chars:se.Buffer.from(o,"utf8")};return n(l,s,u)},i.alloc=o=>w.u32().span+w.u32().span+se.Buffer.from(o,"utf8").length,i},qm=(r="authorized")=>w.struct([Se("staker"),Se("withdrawer")],r),Wm=(r="lockup")=>w.struct([w.ns64("unixTimestamp"),w.ns64("epoch"),Se("custodian")],r),$m=(r="voteInit")=>w.struct([Se("nodePubkey"),Se("authorizedVoter"),Se("authorizedWithdrawer"),w.u8("commission")],r),Ym=(r="voteAuthorizeWithSeedArgs")=>w.struct([w.u32("voteAuthorizationType"),Se("currentAuthorityDerivedKeyOwnerPubkey"),En("currentAuthorityDerivedKeySeed"),Se("newAuthorized")],r);function zR(r,e){let t=i=>{if(i.span>=0)return i.span;if(typeof i.alloc=="function")return i.alloc(e[i.property]);if("count"in i&&"elementLayout"in i){let o=e[i.property];if(Array.isArray(o))return o.length*t(i.elementLayout)}else if("fields"in i)return zR({layout:i},e[i.property]);return 0},n=0;return r.layout.fields.forEach(i=>{n+=t(i)}),n}function Xt(r){let e=0,t=0;for(;;){let n=r.shift();if(e|=(n&127)<<t*7,t+=1,!(n&128))break}return e}function rr(r,e){let t=e;for(;;){let n=t&127;if(t>>=7,t==0){r.push(n);break}else n|=128,r.push(n)}}function Te(r,e){if(!r)throw new Error(e||"Assertion failed")}var ts=class r{constructor(e,t){this.payer=void 0,this.keyMetaMap=void 0,this.payer=e,this.keyMetaMap=t}static compile(e,t){let n=new Map,i=s=>{let u=s.toBase58(),l=n.get(u);return l===void 0&&(l={isSigner:!1,isWritable:!1,isInvoked:!1},n.set(u,l)),l},o=i(t);o.isSigner=!0,o.isWritable=!0;for(let s of e){i(s.programId).isInvoked=!0;for(let u of s.keys){let l=i(u.pubkey);l.isSigner||=u.isSigner,l.isWritable||=u.isWritable}}return new r(t,n)}getMessageComponents(){let e=[...this.keyMetaMap.entries()];Te(e.length<=256,"Max static account keys length exceeded");let t=e.filter(([,l])=>l.isSigner&&l.isWritable),n=e.filter(([,l])=>l.isSigner&&!l.isWritable),i=e.filter(([,l])=>!l.isSigner&&l.isWritable),o=e.filter(([,l])=>!l.isSigner&&!l.isWritable),s={numRequiredSignatures:t.length+n.length,numReadonlySignedAccounts:n.length,numReadonlyUnsignedAccounts:o.length};{Te(t.length>0,"Expected at least one writable signer key");let[l]=t[0];Te(l===this.payer.toBase58(),"Expected first writable signer key to be the fee payer")}let u=[...t.map(([l])=>new X(l)),...n.map(([l])=>new X(l)),...i.map(([l])=>new X(l)),...o.map(([l])=>new X(l))];return[s,u]}extractTableLookup(e){let[t,n]=this.drainKeysFoundInLookupTable(e.state.addresses,s=>!s.isSigner&&!s.isInvoked&&s.isWritable),[i,o]=this.drainKeysFoundInLookupTable(e.state.addresses,s=>!s.isSigner&&!s.isInvoked&&!s.isWritable);if(!(t.length===0&&i.length===0))return[{accountKey:e.key,writableIndexes:t,readonlyIndexes:i},{writable:n,readonly:o}]}drainKeysFoundInLookupTable(e,t){let n=new Array,i=new Array;for(let[o,s]of this.keyMetaMap.entries())if(t(s)){let u=new X(o),l=e.findIndex(p=>p.equals(u));l>=0&&(Te(l<256,"Max lookup table index exceeded"),n.push(l),i.push(u),this.keyMetaMap.delete(o))}return[n,i]}},VR="Reached end of buffer unexpectedly";function Ar(r){if(r.length===0)throw new Error(VR);return r.shift()}function Zt(r,...e){let[t]=e;if(e.length===2?t+(e[1]??0)>r.length:t>=r.length)throw new Error(VR);return r.splice(...e)}var Or=class r{constructor(e){this.header=void 0,this.accountKeys=void 0,this.recentBlockhash=void 0,this.instructions=void 0,this.indexToProgramIds=new Map,this.header=e.header,this.accountKeys=e.accountKeys.map(t=>new X(t)),this.recentBlockhash=e.recentBlockhash,this.instructions=e.instructions,this.instructions.forEach(t=>this.indexToProgramIds.set(t.programIdIndex,this.accountKeys[t.programIdIndex]))}get version(){return"legacy"}get staticAccountKeys(){return this.accountKeys}get compiledInstructions(){return this.instructions.map(e=>({programIdIndex:e.programIdIndex,accountKeyIndexes:e.accounts,data:Ct.default.decode(e.data)}))}get addressTableLookups(){return[]}getAccountKeys(){return new gn(this.staticAccountKeys)}static compile(e){let t=ts.compile(e.instructions,e.payerKey),[n,i]=t.getMessageComponents(),s=new gn(i).compileInstructions(e.instructions).map(u=>({programIdIndex:u.programIdIndex,accounts:u.accountKeyIndexes,data:Ct.default.encode(u.data)}));return new r({header:n,accountKeys:i,recentBlockhash:e.recentBlockhash,instructions:s})}isAccountSigner(e){return e<this.header.numRequiredSignatures}isAccountWritable(e){let t=this.header.numRequiredSignatures;if(e>=this.header.numRequiredSignatures){let n=e-t,o=this.accountKeys.length-t-this.header.numReadonlyUnsignedAccounts;return n<o}else{let n=t-this.header.numReadonlySignedAccounts;return e<n}}isProgramId(e){return this.indexToProgramIds.has(e)}programIds(){return[...this.indexToProgramIds.values()]}nonProgramIds(){return this.accountKeys.filter((e,t)=>!this.isProgramId(t))}serialize(){let e=this.accountKeys.length,t=[];rr(t,e);let n=this.instructions.map(g=>{let{accounts:v,programIdIndex:m}=g,x=Array.from(Ct.default.decode(g.data)),I=[];rr(I,v.length);let O=[];return rr(O,x.length),{programIdIndex:m,keyIndicesCount:se.Buffer.from(I),keyIndices:v,dataLength:se.Buffer.from(O),data:x}}),i=[];rr(i,n.length);let o=se.Buffer.alloc(tn);se.Buffer.from(i).copy(o);let s=i.length;n.forEach(g=>{let m=w.struct([w.u8("programIdIndex"),w.blob(g.keyIndicesCount.length,"keyIndicesCount"),w.seq(w.u8("keyIndex"),g.keyIndices.length,"keyIndices"),w.blob(g.dataLength.length,"dataLength"),w.seq(w.u8("userdatum"),g.data.length,"data")]).encode(g,o,s);s+=m}),o=o.slice(0,s);let u=w.struct([w.blob(1,"numRequiredSignatures"),w.blob(1,"numReadonlySignedAccounts"),w.blob(1,"numReadonlyUnsignedAccounts"),w.blob(t.length,"keyCount"),w.seq(Se("key"),e,"keys"),Se("recentBlockhash")]),l={numRequiredSignatures:se.Buffer.from([this.header.numRequiredSignatures]),numReadonlySignedAccounts:se.Buffer.from([this.header.numReadonlySignedAccounts]),numReadonlyUnsignedAccounts:se.Buffer.from([this.header.numReadonlyUnsignedAccounts]),keyCount:se.Buffer.from(t),keys:this.accountKeys.map(g=>Ae(g.toBytes())),recentBlockhash:Ct.default.decode(this.recentBlockhash)},p=se.Buffer.alloc(2048),R=u.encode(l,p);return o.copy(p,R),p.slice(0,R+o.length)}static from(e){let t=[...e],n=Ar(t);if(n!==(n&ls))throw new Error("Versioned messages must be deserialized with VersionedMessage.deserialize()");let i=Ar(t),o=Ar(t),s=Xt(t),u=[];for(let v=0;v<s;v++){let m=Zt(t,0,br);u.push(new X(se.Buffer.from(m)))}let l=Zt(t,0,br),p=Xt(t),R=[];for(let v=0;v<p;v++){let m=Ar(t),x=Xt(t),I=Zt(t,0,x),O=Xt(t),D=Zt(t,0,O),k=Ct.default.encode(se.Buffer.from(D));R.push({programIdIndex:m,accounts:I,data:k})}let g={header:{numRequiredSignatures:n,numReadonlySignedAccounts:i,numReadonlyUnsignedAccounts:o},recentBlockhash:Ct.default.encode(se.Buffer.from(l)),accountKeys:u,instructions:R};return new r(g)}},Pn=class r{constructor(e){this.header=void 0,this.staticAccountKeys=void 0,this.recentBlockhash=void 0,this.compiledInstructions=void 0,this.addressTableLookups=void 0,this.header=e.header,this.staticAccountKeys=e.staticAccountKeys,this.recentBlockhash=e.recentBlockhash,this.compiledInstructions=e.compiledInstructions,this.addressTableLookups=e.addressTableLookups}get version(){return 0}get numAccountKeysFromLookups(){let e=0;for(let t of this.addressTableLookups)e+=t.readonlyIndexes.length+t.writableIndexes.length;return e}getAccountKeys(e){let t;if(e&&"accountKeysFromLookups"in e&&e.accountKeysFromLookups){if(this.numAccountKeysFromLookups!=e.accountKeysFromLookups.writable.length+e.accountKeysFromLookups.readonly.length)throw new Error("Failed to get account keys because of a mismatch in the number of account keys from lookups");t=e.accountKeysFromLookups}else if(e&&"addressLookupTableAccounts"in e&&e.addressLookupTableAccounts)t=this.resolveAddressTableLookups(e.addressLookupTableAccounts);else if(this.addressTableLookups.length>0)throw new Error("Failed to get account keys because address table lookups were not resolved");return new gn(this.staticAccountKeys,t)}isAccountSigner(e){return e<this.header.numRequiredSignatures}isAccountWritable(e){let t=this.header.numRequiredSignatures,n=this.staticAccountKeys.length;if(e>=n){let i=e-n,o=this.addressTableLookups.reduce((s,u)=>s+u.writableIndexes.length,0);return i<o}else if(e>=this.header.numRequiredSignatures){let i=e-t,s=n-t-this.header.numReadonlyUnsignedAccounts;return i<s}else{let i=t-this.header.numReadonlySignedAccounts;return e<i}}resolveAddressTableLookups(e){let t={writable:[],readonly:[]};for(let n of this.addressTableLookups){let i=e.find(o=>o.key.equals(n.accountKey));if(!i)throw new Error(`Failed to find address lookup table account for table key ${n.accountKey.toBase58()}`);for(let o of n.writableIndexes)if(o<i.state.addresses.length)t.writable.push(i.state.addresses[o]);else throw new Error(`Failed to find address for index ${o} in address lookup table ${n.accountKey.toBase58()}`);for(let o of n.readonlyIndexes)if(o<i.state.addresses.length)t.readonly.push(i.state.addresses[o]);else throw new Error(`Failed to find address for index ${o} in address lookup table ${n.accountKey.toBase58()}`)}return t}static compile(e){let t=ts.compile(e.instructions,e.payerKey),n=new Array,i={writable:new Array,readonly:new Array},o=e.addressLookupTableAccounts||[];for(let R of o){let g=t.extractTableLookup(R);if(g!==void 0){let[v,{writable:m,readonly:x}]=g;n.push(v),i.writable.push(...m),i.readonly.push(...x)}}let[s,u]=t.getMessageComponents(),p=new gn(u,i).compileInstructions(e.instructions);return new r({header:s,staticAccountKeys:u,recentBlockhash:e.recentBlockhash,compiledInstructions:p,addressTableLookups:n})}serialize(){let e=Array();rr(e,this.staticAccountKeys.length);let t=this.serializeInstructions(),n=Array();rr(n,this.compiledInstructions.length);let i=this.serializeAddressTableLookups(),o=Array();rr(o,this.addressTableLookups.length);let s=w.struct([w.u8("prefix"),w.struct([w.u8("numRequiredSignatures"),w.u8("numReadonlySignedAccounts"),w.u8("numReadonlyUnsignedAccounts")],"header"),w.blob(e.length,"staticAccountKeysLength"),w.seq(Se(),this.staticAccountKeys.length,"staticAccountKeys"),Se("recentBlockhash"),w.blob(n.length,"instructionsLength"),w.blob(t.length,"serializedInstructions"),w.blob(o.length,"addressTableLookupsLength"),w.blob(i.length,"serializedAddressTableLookups")]),u=new Uint8Array(tn),p=s.encode({prefix:128,header:this.header,staticAccountKeysLength:new Uint8Array(e),staticAccountKeys:this.staticAccountKeys.map(R=>R.toBytes()),recentBlockhash:Ct.default.decode(this.recentBlockhash),instructionsLength:new Uint8Array(n),serializedInstructions:t,addressTableLookupsLength:new Uint8Array(o),serializedAddressTableLookups:i},u);return u.slice(0,p)}serializeInstructions(){let e=0,t=new Uint8Array(tn);for(let n of this.compiledInstructions){let i=Array();rr(i,n.accountKeyIndexes.length);let o=Array();rr(o,n.data.length);let s=w.struct([w.u8("programIdIndex"),w.blob(i.length,"encodedAccountKeyIndexesLength"),w.seq(w.u8(),n.accountKeyIndexes.length,"accountKeyIndexes"),w.blob(o.length,"encodedDataLength"),w.blob(n.data.length,"data")]);e+=s.encode({programIdIndex:n.programIdIndex,encodedAccountKeyIndexesLength:new Uint8Array(i),accountKeyIndexes:n.accountKeyIndexes,encodedDataLength:new Uint8Array(o),data:n.data},t,e)}return t.slice(0,e)}serializeAddressTableLookups(){let e=0,t=new Uint8Array(tn);for(let n of this.addressTableLookups){let i=Array();rr(i,n.writableIndexes.length);let o=Array();rr(o,n.readonlyIndexes.length);let s=w.struct([Se("accountKey"),w.blob(i.length,"encodedWritableIndexesLength"),w.seq(w.u8(),n.writableIndexes.length,"writableIndexes"),w.blob(o.length,"encodedReadonlyIndexesLength"),w.seq(w.u8(),n.readonlyIndexes.length,"readonlyIndexes")]);e+=s.encode({accountKey:n.accountKey.toBytes(),encodedWritableIndexesLength:new Uint8Array(i),writableIndexes:n.writableIndexes,encodedReadonlyIndexesLength:new Uint8Array(o),readonlyIndexes:n.readonlyIndexes},t,e)}return t.slice(0,e)}static deserialize(e){let t=[...e],n=Ar(t),i=n&ls;Te(n!==i,"Expected versioned message but received legacy message");let o=i;Te(o===0,`Expected versioned message with version 0 but found version ${o}`);let s={numRequiredSignatures:Ar(t),numReadonlySignedAccounts:Ar(t),numReadonlyUnsignedAccounts:Ar(t)},u=[],l=Xt(t);for(let x=0;x<l;x++)u.push(new X(Zt(t,0,br)));let p=Ct.default.encode(Zt(t,0,br)),R=Xt(t),g=[];for(let x=0;x<R;x++){let I=Ar(t),O=Xt(t),D=Zt(t,0,O),k=Xt(t),B=new Uint8Array(Zt(t,0,k));g.push({programIdIndex:I,accountKeyIndexes:D,data:B})}let v=Xt(t),m=[];for(let x=0;x<v;x++){let I=new X(Zt(t,0,br)),O=Xt(t),D=Zt(t,0,O),k=Xt(t),B=Zt(t,0,k);m.push({accountKey:I,writableIndexes:D,readonlyIndexes:B})}return new r({header:s,staticAccountKeys:u,recentBlockhash:p,compiledInstructions:g,addressTableLookups:m})}},su={deserializeMessageVersion(r){let e=r[0],t=e&ls;return t===e?"legacy":t},deserialize:r=>{let e=su.deserializeMessageVersion(r);if(e==="legacy")return Or.from(r);if(e===0)return Pn.deserialize(r);throw new Error(`Transaction message version ${e} deserialization is not supported`)}},kr=function(r){return r[r.BLOCKHEIGHT_EXCEEDED=0]="BLOCKHEIGHT_EXCEEDED",r[r.PROCESSED=1]="PROCESSED",r[r.TIMED_OUT=2]="TIMED_OUT",r[r.NONCE_INVALID=3]="NONCE_INVALID",r}({}),jm=se.Buffer.alloc(Oi).fill(0),Me=class{constructor(e){this.keys=void 0,this.programId=void 0,this.data=se.Buffer.alloc(0),this.programId=e.programId,this.keys=e.keys,e.data&&(this.data=e.data)}toJSON(){return{keys:this.keys.map(({pubkey:e,isSigner:t,isWritable:n})=>({pubkey:e.toJSON(),isSigner:t,isWritable:n})),programId:this.programId.toJSON(),data:[...this.data]}}},De=class r{get signature(){return this.signatures.length>0?this.signatures[0].signature:null}constructor(e){if(this.signatures=[],this.feePayer=void 0,this.instructions=[],this.recentBlockhash=void 0,this.lastValidBlockHeight=void 0,this.nonceInfo=void 0,this.minNonceContextSlot=void 0,this._message=void 0,this._json=void 0,!!e)if(e.feePayer&&(this.feePayer=e.feePayer),e.signatures&&(this.signatures=e.signatures),Object.prototype.hasOwnProperty.call(e,"nonceInfo")){let{minContextSlot:t,nonceInfo:n}=e;this.minNonceContextSlot=t,this.nonceInfo=n}else if(Object.prototype.hasOwnProperty.call(e,"lastValidBlockHeight")){let{blockhash:t,lastValidBlockHeight:n}=e;this.recentBlockhash=t,this.lastValidBlockHeight=n}else{let{recentBlockhash:t,nonceInfo:n}=e;n&&(this.nonceInfo=n),this.recentBlockhash=t}}toJSON(){return{recentBlockhash:this.recentBlockhash||null,feePayer:this.feePayer?this.feePayer.toJSON():null,nonceInfo:this.nonceInfo?{nonce:this.nonceInfo.nonce,nonceInstruction:this.nonceInfo.nonceInstruction.toJSON()}:null,instructions:this.instructions.map(e=>e.toJSON()),signers:this.signatures.map(({publicKey:e})=>e.toJSON())}}add(...e){if(e.length===0)throw new Error("No instructions");return e.forEach(t=>{"instructions"in t?this.instructions=this.instructions.concat(t.instructions):"data"in t&&"programId"in t&&"keys"in t?this.instructions.push(t):this.instructions.push(new Me(t))}),this}compileMessage(){if(this._message&&JSON.stringify(this.toJSON())===JSON.stringify(this._json))return this._message;let e,t;if(this.nonceInfo?(e=this.nonceInfo.nonce,this.instructions[0]!=this.nonceInfo.nonceInstruction?t=[this.nonceInfo.nonceInstruction,...this.instructions]:t=this.instructions):(e=this.recentBlockhash,t=this.instructions),!e)throw new Error("Transaction recentBlockhash required");t.length<1&&console.warn("No instructions provided");let n;if(this.feePayer)n=this.feePayer;else if(this.signatures.length>0&&this.signatures[0].publicKey)n=this.signatures[0].publicKey;else throw new Error("Transaction fee payer required");for(let I=0;I<t.length;I++)if(t[I].programId===void 0)throw new Error(`Transaction instruction index ${I} has undefined program id`);let i=[],o=[];t.forEach(I=>{I.keys.forEach(D=>{o.push({...D})});let O=I.programId.toString();i.includes(O)||i.push(O)}),i.forEach(I=>{o.push({pubkey:new X(I),isSigner:!1,isWritable:!1})});let s=[];o.forEach(I=>{let O=I.pubkey.toString(),D=s.findIndex(k=>k.pubkey.toString()===O);D>-1?(s[D].isWritable=s[D].isWritable||I.isWritable,s[D].isSigner=s[D].isSigner||I.isSigner):s.push(I)}),s.sort(function(I,O){if(I.isSigner!==O.isSigner)return I.isSigner?-1:1;if(I.isWritable!==O.isWritable)return I.isWritable?-1:1;let D={localeMatcher:"best fit",usage:"sort",sensitivity:"variant",ignorePunctuation:!1,numeric:!1,caseFirst:"lower"};return I.pubkey.toBase58().localeCompare(O.pubkey.toBase58(),"en",D)});let u=s.findIndex(I=>I.pubkey.equals(n));if(u>-1){let[I]=s.splice(u,1);I.isSigner=!0,I.isWritable=!0,s.unshift(I)}else s.unshift({pubkey:n,isSigner:!0,isWritable:!0});for(let I of this.signatures){let O=s.findIndex(D=>D.pubkey.equals(I.publicKey));if(O>-1)s[O].isSigner||(s[O].isSigner=!0,console.warn("Transaction references a signature that is unnecessary, only the fee payer and instruction signer accounts should sign a transaction. This behavior is deprecated and will throw an error in the next major version release."));else throw new Error(`unknown signer: ${I.publicKey.toString()}`)}let l=0,p=0,R=0,g=[],v=[];s.forEach(({pubkey:I,isSigner:O,isWritable:D})=>{O?(g.push(I.toString()),l+=1,D||(p+=1)):(v.push(I.toString()),D||(R+=1))});let m=g.concat(v),x=t.map(I=>{let{data:O,programId:D}=I;return{programIdIndex:m.indexOf(D.toString()),accounts:I.keys.map(k=>m.indexOf(k.pubkey.toString())),data:Ct.default.encode(O)}});return x.forEach(I=>{Te(I.programIdIndex>=0),I.accounts.forEach(O=>Te(O>=0))}),new Or({header:{numRequiredSignatures:l,numReadonlySignedAccounts:p,numReadonlyUnsignedAccounts:R},accountKeys:m,recentBlockhash:e,instructions:x})}_compile(){let e=this.compileMessage(),t=e.accountKeys.slice(0,e.header.numRequiredSignatures);return this.signatures.length===t.length&&this.signatures.every((i,o)=>t[o].equals(i.publicKey))||(this.signatures=t.map(n=>({signature:null,publicKey:n}))),e}serializeMessage(){return this._compile().serialize()}async getEstimatedFee(e){return(await e.getFeeForMessage(this.compileMessage())).value}setSigners(...e){if(e.length===0)throw new Error("No signers");let t=new Set;this.signatures=e.filter(n=>{let i=n.toString();return t.has(i)?!1:(t.add(i),!0)}).map(n=>({signature:null,publicKey:n}))}sign(...e){if(e.length===0)throw new Error("No signers");let t=new Set,n=[];for(let o of e){let s=o.publicKey.toString();t.has(s)||(t.add(s),n.push(o))}this.signatures=n.map(o=>({signature:null,publicKey:o.publicKey}));let i=this._compile();this._partialSign(i,...n)}partialSign(...e){if(e.length===0)throw new Error("No signers");let t=new Set,n=[];for(let o of e){let s=o.publicKey.toString();t.has(s)||(t.add(s),n.push(o))}let i=this._compile();this._partialSign(i,...n)}_partialSign(e,...t){let n=e.serialize();t.forEach(i=>{let o=ou(n,i.secretKey);this._addSignature(i.publicKey,Ae(o))})}addSignature(e,t){this._compile(),this._addSignature(e,t)}_addSignature(e,t){Te(t.length===64);let n=this.signatures.findIndex(i=>e.equals(i.publicKey));if(n<0)throw new Error(`unknown signer: ${e.toString()}`);this.signatures[n].signature=se.Buffer.from(t)}verifySignatures(e=!0){return!this._getMessageSignednessErrors(this.serializeMessage(),e)}_getMessageSignednessErrors(e,t){let n={};for(let{signature:i,publicKey:o}of this.signatures)i===null?t&&(n.missing||=[]).push(o):Vm(i,e,o.toBytes())||(n.invalid||=[]).push(o);return n.invalid||n.missing?n:void 0}serialize(e){let{requireAllSignatures:t,verifySignatures:n}=Object.assign({requireAllSignatures:!0,verifySignatures:!0},e),i=this.serializeMessage();if(n){let o=this._getMessageSignednessErrors(i,t);if(o){let s="Signature verification failed.";throw o.invalid&&(s+=`
Invalid signature for public key${o.invalid.length===1?"":"(s)"} [\`${o.invalid.map(u=>u.toBase58()).join("`, `")}\`].`),o.missing&&(s+=`
Missing signature for public key${o.missing.length===1?"":"(s)"} [\`${o.missing.map(u=>u.toBase58()).join("`, `")}\`].`),new Error(s)}}return this._serialize(i)}_serialize(e){let{signatures:t}=this,n=[];rr(n,t.length);let i=n.length+t.length*64+e.length,o=se.Buffer.alloc(i);return Te(t.length<256),se.Buffer.from(n).copy(o,0),t.forEach(({signature:s},u)=>{s!==null&&(Te(s.length===64,"signature has invalid length"),se.Buffer.from(s).copy(o,n.length+u*64))}),e.copy(o,n.length+t.length*64),Te(o.length<=tn,`Transaction too large: ${o.length} > ${tn}`),o}get keys(){return Te(this.instructions.length===1),this.instructions[0].keys.map(e=>e.pubkey)}get programId(){return Te(this.instructions.length===1),this.instructions[0].programId}get data(){return Te(this.instructions.length===1),this.instructions[0].data}static from(e){let t=[...e],n=Xt(t),i=[];for(let o=0;o<n;o++){let s=Zt(t,0,Oi);i.push(Ct.default.encode(se.Buffer.from(s)))}return r.populate(Or.from(t),i)}static populate(e,t=[]){let n=new r;return n.recentBlockhash=e.recentBlockhash,e.header.numRequiredSignatures>0&&(n.feePayer=e.accountKeys[0]),t.forEach((i,o)=>{let s={signature:i==Ct.default.encode(jm)?null:Ct.default.decode(i),publicKey:e.accountKeys[o]};n.signatures.push(s)}),e.instructions.forEach(i=>{let o=i.accounts.map(s=>{let u=e.accountKeys[s];return{pubkey:u,isSigner:n.signatures.some(l=>l.publicKey.toString()===u.toString())||e.isAccountSigner(s),isWritable:e.isAccountWritable(s)}});n.instructions.push(new Me({keys:o,programId:e.accountKeys[i.programIdIndex],data:Ct.default.decode(i.data)}))}),n._message=e,n._json=n.toJSON(),n}},zc=class r{constructor(e){this.payerKey=void 0,this.instructions=void 0,this.recentBlockhash=void 0,this.payerKey=e.payerKey,this.instructions=e.instructions,this.recentBlockhash=e.recentBlockhash}static decompile(e,t){let{header:n,compiledInstructions:i,recentBlockhash:o}=e,{numRequiredSignatures:s,numReadonlySignedAccounts:u,numReadonlyUnsignedAccounts:l}=n,p=s-u;Te(p>0,"Message header is invalid");let R=e.staticAccountKeys.length-s-l;Te(R>=0,"Message header is invalid");let g=e.getAccountKeys(t),v=g.get(0);if(v===void 0)throw new Error("Failed to decompile message because no account keys were found");let m=[];for(let x of i){let I=[];for(let D of x.accountKeyIndexes){let k=g.get(D);if(k===void 0)throw new Error(`Failed to find key for account key index ${D}`);let B=D<s,F;B?F=D<p:D<g.staticAccountKeys.length?F=D-s<R:F=D-g.staticAccountKeys.length<g.accountKeysFromLookups.writable.length,I.push({pubkey:k,isSigner:D<n.numRequiredSignatures,isWritable:F})}let O=g.get(x.programIdIndex);if(O===void 0)throw new Error(`Failed to find program id for program id index ${x.programIdIndex}`);m.push(new Me({programId:O,data:Ae(x.data),keys:I}))}return new r({payerKey:v,instructions:m,recentBlockhash:o})}compileToLegacyMessage(){return Or.compile({payerKey:this.payerKey,recentBlockhash:this.recentBlockhash,instructions:this.instructions})}compileToV0Message(e){return Pn.compile({payerKey:this.payerKey,recentBlockhash:this.recentBlockhash,instructions:this.instructions,addressLookupTableAccounts:e})}},Vc=class r{get version(){return this.message.version}constructor(e,t){if(this.signatures=void 0,this.message=void 0,t!==void 0)Te(t.length===e.header.numRequiredSignatures,"Expected signatures length to be equal to the number of required signatures"),this.signatures=t;else{let n=[];for(let i=0;i<e.header.numRequiredSignatures;i++)n.push(new Uint8Array(Oi));this.signatures=n}this.message=e}serialize(){let e=this.message.serialize(),t=Array();rr(t,this.signatures.length);let n=w.struct([w.blob(t.length,"encodedSignaturesLength"),w.seq(Km(),this.signatures.length,"signatures"),w.blob(e.length,"serializedMessage")]),i=new Uint8Array(2048),o=n.encode({encodedSignaturesLength:new Uint8Array(t),signatures:this.signatures,serializedMessage:e},i);return i.slice(0,o)}static deserialize(e){let t=[...e],n=[],i=Xt(t);for(let s=0;s<i;s++)n.push(new Uint8Array(Zt(t,0,Oi)));let o=su.deserialize(new Uint8Array(t));return new r(o,n)}sign(e){let t=this.message.serialize(),n=this.message.staticAccountKeys.slice(0,this.message.header.numRequiredSignatures);for(let i of e){let o=n.findIndex(s=>s.equals(i.publicKey));Te(o>=0,`Cannot sign with non signer key ${i.publicKey.toBase58()}`),this.signatures[o]=ou(t,i.secretKey)}}addSignature(e,t){Te(t.byteLength===64,"Signature must be 64 bytes long");let i=this.message.staticAccountKeys.slice(0,this.message.header.numRequiredSignatures).findIndex(o=>o.equals(e));Te(i>=0,`Can not add signature; \`${e.toBase58()}\` is not required to sign this transaction`),this.signatures[i]=t}},Xm=160,Zm=64,Jm=Xm/Zm,GR=1e3/Jm,yr=new X("SysvarC1ock11111111111111111111111111111111"),Qm=new X("SysvarEpochSchedu1e111111111111111111111111"),eS=new X("Sysvar1nstructions1111111111111111111111111"),Jo=new X("SysvarRecentB1ockHashes11111111111111111111"),Fn=new X("SysvarRent111111111111111111111111111111111"),tS=new X("SysvarRewards111111111111111111111111111111"),rS=new X("SysvarS1otHashes111111111111111111111111111"),nS=new X("SysvarS1otHistory11111111111111111111111111"),Qo=new X("SysvarStakeHistory1111111111111111111111111"),yn=class extends Error{constructor({action:e,signature:t,transactionMessage:n,logs:i}){let o=i?`Logs: 
${JSON.stringify(i.slice(-10),null,2)}. `:"",s="\nCatch the `SendTransactionError` and call `getLogs()` on it for full details.",u;switch(e){case"send":u=`Transaction ${t} resulted in an error. 
${n}. `+o+s;break;case"simulate":u=`Simulation failed. 
Message: ${n}. 
`+o+s;break;default:u=`Unknown action '${(l=>l)(e)}'`}super(u),this.signature=void 0,this.transactionMessage=void 0,this.transactionLogs=void 0,this.signature=t,this.transactionMessage=n,this.transactionLogs=i||void 0}get transactionError(){return{message:this.transactionMessage,logs:Array.isArray(this.transactionLogs)?this.transactionLogs:void 0}}get logs(){let e=this.transactionLogs;if(!(e!=null&&typeof e=="object"&&"then"in e))return e}async getLogs(e){return Array.isArray(this.transactionLogs)||(this.transactionLogs=new Promise((t,n)=>{e.getTransaction(this.signature).then(i=>{if(i&&i.meta&&i.meta.logMessages){let o=i.meta.logMessages;this.transactionLogs=o,t(o)}else n(new Error("Log messages not found"))}).catch(n)})),await this.transactionLogs}},iS={JSON_RPC_SERVER_ERROR_BLOCK_CLEANED_UP:-32001,JSON_RPC_SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE:-32002,JSON_RPC_SERVER_ERROR_TRANSACTION_SIGNATURE_VERIFICATION_FAILURE:-32003,JSON_RPC_SERVER_ERROR_BLOCK_NOT_AVAILABLE:-32004,JSON_RPC_SERVER_ERROR_NODE_UNHEALTHY:-32005,JSON_RPC_SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE:-32006,JSON_RPC_SERVER_ERROR_SLOT_SKIPPED:-32007,JSON_RPC_SERVER_ERROR_NO_SNAPSHOT:-32008,JSON_RPC_SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED:-32009,JSON_RPC_SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX:-32010,JSON_RPC_SERVER_ERROR_TRANSACTION_HISTORY_NOT_AVAILABLE:-32011,JSON_RPC_SCAN_ERROR:-32012,JSON_RPC_SERVER_ERROR_TRANSACTION_SIGNATURE_LEN_MISMATCH:-32013,JSON_RPC_SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET:-32014,JSON_RPC_SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION:-32015,JSON_RPC_SERVER_ERROR_MIN_CONTEXT_SLOT_NOT_REACHED:-32016},re=class extends Error{constructor({code:e,message:t,data:n},i){super(i!=null?`${i}: ${t}`:t),this.code=void 0,this.data=void 0,this.code=e,this.data=n,this.name="SolanaJSONRPCError"}};async function Gc(r,e,t,n){let i=n&&{skipPreflight:n.skipPreflight,preflightCommitment:n.preflightCommitment||n.commitment,maxRetries:n.maxRetries,minContextSlot:n.minContextSlot},o=await r.sendTransaction(e,t,i),s;if(e.recentBlockhash!=null&&e.lastValidBlockHeight!=null)s=(await r.confirmTransaction({abortSignal:n?.abortSignal,signature:o,blockhash:e.recentBlockhash,lastValidBlockHeight:e.lastValidBlockHeight},n&&n.commitment)).value;else if(e.minNonceContextSlot!=null&&e.nonceInfo!=null){let{nonceInstruction:u}=e.nonceInfo,l=u.keys[0].pubkey;s=(await r.confirmTransaction({abortSignal:n?.abortSignal,minContextSlot:e.minNonceContextSlot,nonceAccountPubkey:l,nonceValue:e.nonceInfo.nonce,signature:o},n&&n.commitment)).value}else n?.abortSignal!=null&&console.warn("sendAndConfirmTransaction(): A transaction with a deprecated confirmation strategy was supplied along with an `abortSignal`. Only transactions having `lastValidBlockHeight` or a combination of `nonceInfo` and `minNonceContextSlot` are abortable."),s=(await r.confirmTransaction(o,n&&n.commitment)).value;if(s.err)throw o!=null?new yn({action:"send",signature:o,transactionMessage:`Status: (${JSON.stringify(s)})`}):new Error(`Transaction ${o} failed (${JSON.stringify(s)})`);return o}function Rn(r){return new Promise(e=>setTimeout(e,r))}function Oe(r,e){let t=r.layout.span>=0?r.layout.span:zR(r,e),n=se.Buffer.alloc(t),i=Object.assign({instruction:r.index},e);return r.layout.encode(i,n),n}function Be(r,e){let t;try{t=r.layout.decode(e)}catch(n){throw new Error("invalid instruction; "+n)}if(t.instruction!==r.index)throw new Error(`invalid instruction; instruction index mismatch ${t.instruction} != ${r.index}`);return t}var HR=w.nu64("lamportsPerSignature"),KR=w.struct([w.u32("version"),w.u32("state"),Se("authorizedPubkey"),Se("nonce"),w.struct([HR],"feeCalculator")]),Hc=KR.span,rs=class r{constructor(e){this.authorizedPubkey=void 0,this.nonce=void 0,this.feeCalculator=void 0,this.authorizedPubkey=e.authorizedPubkey,this.nonce=e.nonce,this.feeCalculator=e.feeCalculator}static fromAccountData(e){let t=KR.decode(Ae(e),0);return new r({authorizedPubkey:new X(t.authorizedPubkey),nonce:new X(t.nonce).toString(),feeCalculator:t.feeCalculator})}};function zn(r){let e=MR.blob(8,r),t=e.decode.bind(e),n=e.encode.bind(e),i=e,o=UR.getU64Codec();return i.decode=(s,u)=>{let l=t(s,u);return o.decode(l)},i.encode=(s,u,l)=>{let p=o.encode(s);return n(p,u,l)},i}var Kc=class{constructor(){}static decodeInstructionType(e){this.checkProgramId(e.programId);let n=w.u32("instruction").decode(e.data),i;for(let[o,s]of Object.entries(He))if(s.index==n){i=o;break}if(!i)throw new Error("Instruction type incorrect; not a SystemInstruction");return i}static decodeCreateAccount(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,2);let{lamports:t,space:n,programId:i}=Be(He.Create,e.data);return{fromPubkey:e.keys[0].pubkey,newAccountPubkey:e.keys[1].pubkey,lamports:t,space:n,programId:new X(i)}}static decodeTransfer(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,2);let{lamports:t}=Be(He.Transfer,e.data);return{fromPubkey:e.keys[0].pubkey,toPubkey:e.keys[1].pubkey,lamports:t}}static decodeTransferWithSeed(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,3);let{lamports:t,seed:n,programId:i}=Be(He.TransferWithSeed,e.data);return{fromPubkey:e.keys[0].pubkey,basePubkey:e.keys[1].pubkey,toPubkey:e.keys[2].pubkey,lamports:t,seed:n,programId:new X(i)}}static decodeAllocate(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,1);let{space:t}=Be(He.Allocate,e.data);return{accountPubkey:e.keys[0].pubkey,space:t}}static decodeAllocateWithSeed(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,1);let{base:t,seed:n,space:i,programId:o}=Be(He.AllocateWithSeed,e.data);return{accountPubkey:e.keys[0].pubkey,basePubkey:new X(t),seed:n,space:i,programId:new X(o)}}static decodeAssign(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,1);let{programId:t}=Be(He.Assign,e.data);return{accountPubkey:e.keys[0].pubkey,programId:new X(t)}}static decodeAssignWithSeed(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,1);let{base:t,seed:n,programId:i}=Be(He.AssignWithSeed,e.data);return{accountPubkey:e.keys[0].pubkey,basePubkey:new X(t),seed:n,programId:new X(i)}}static decodeCreateWithSeed(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,2);let{base:t,seed:n,lamports:i,space:o,programId:s}=Be(He.CreateWithSeed,e.data);return{fromPubkey:e.keys[0].pubkey,newAccountPubkey:e.keys[1].pubkey,basePubkey:new X(t),seed:n,lamports:i,space:o,programId:new X(s)}}static decodeNonceInitialize(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,3);let{authorized:t}=Be(He.InitializeNonceAccount,e.data);return{noncePubkey:e.keys[0].pubkey,authorizedPubkey:new X(t)}}static decodeNonceAdvance(e){return this.checkProgramId(e.programId),this.checkKeyLength(e.keys,3),Be(He.AdvanceNonceAccount,e.data),{noncePubkey:e.keys[0].pubkey,authorizedPubkey:e.keys[2].pubkey}}static decodeNonceWithdraw(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,5);let{lamports:t}=Be(He.WithdrawNonceAccount,e.data);return{noncePubkey:e.keys[0].pubkey,toPubkey:e.keys[1].pubkey,authorizedPubkey:e.keys[4].pubkey,lamports:t}}static decodeNonceAuthorize(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,2);let{authorized:t}=Be(He.AuthorizeNonceAccount,e.data);return{noncePubkey:e.keys[0].pubkey,authorizedPubkey:e.keys[1].pubkey,newAuthorizedPubkey:new X(t)}}static checkProgramId(e){if(!e.equals(xt.programId))throw new Error("invalid instruction; programId is not SystemProgram")}static checkKeyLength(e,t){if(e.length<t)throw new Error(`invalid instruction; found ${e.length} keys, expected at least ${t}`)}},He=Object.freeze({Create:{index:0,layout:w.struct([w.u32("instruction"),w.ns64("lamports"),w.ns64("space"),Se("programId")])},Assign:{index:1,layout:w.struct([w.u32("instruction"),Se("programId")])},Transfer:{index:2,layout:w.struct([w.u32("instruction"),zn("lamports")])},CreateWithSeed:{index:3,layout:w.struct([w.u32("instruction"),Se("base"),En("seed"),w.ns64("lamports"),w.ns64("space"),Se("programId")])},AdvanceNonceAccount:{index:4,layout:w.struct([w.u32("instruction")])},WithdrawNonceAccount:{index:5,layout:w.struct([w.u32("instruction"),w.ns64("lamports")])},InitializeNonceAccount:{index:6,layout:w.struct([w.u32("instruction"),Se("authorized")])},AuthorizeNonceAccount:{index:7,layout:w.struct([w.u32("instruction"),Se("authorized")])},Allocate:{index:8,layout:w.struct([w.u32("instruction"),w.ns64("space")])},AllocateWithSeed:{index:9,layout:w.struct([w.u32("instruction"),Se("base"),En("seed"),w.ns64("space"),Se("programId")])},AssignWithSeed:{index:10,layout:w.struct([w.u32("instruction"),Se("base"),En("seed"),Se("programId")])},TransferWithSeed:{index:11,layout:w.struct([w.u32("instruction"),zn("lamports"),En("seed"),Se("programId")])},UpgradeNonceAccount:{index:12,layout:w.struct([w.u32("instruction")])}}),xt=class r{constructor(){}static createAccount(e){let t=He.Create,n=Oe(t,{lamports:e.lamports,space:e.space,programId:Ae(e.programId.toBuffer())});return new Me({keys:[{pubkey:e.fromPubkey,isSigner:!0,isWritable:!0},{pubkey:e.newAccountPubkey,isSigner:!0,isWritable:!0}],programId:this.programId,data:n})}static transfer(e){let t,n;if("basePubkey"in e){let i=He.TransferWithSeed;t=Oe(i,{lamports:BigInt(e.lamports),seed:e.seed,programId:Ae(e.programId.toBuffer())}),n=[{pubkey:e.fromPubkey,isSigner:!1,isWritable:!0},{pubkey:e.basePubkey,isSigner:!0,isWritable:!1},{pubkey:e.toPubkey,isSigner:!1,isWritable:!0}]}else{let i=He.Transfer;t=Oe(i,{lamports:BigInt(e.lamports)}),n=[{pubkey:e.fromPubkey,isSigner:!0,isWritable:!0},{pubkey:e.toPubkey,isSigner:!1,isWritable:!0}]}return new Me({keys:n,programId:this.programId,data:t})}static assign(e){let t,n;if("basePubkey"in e){let i=He.AssignWithSeed;t=Oe(i,{base:Ae(e.basePubkey.toBuffer()),seed:e.seed,programId:Ae(e.programId.toBuffer())}),n=[{pubkey:e.accountPubkey,isSigner:!1,isWritable:!0},{pubkey:e.basePubkey,isSigner:!0,isWritable:!1}]}else{let i=He.Assign;t=Oe(i,{programId:Ae(e.programId.toBuffer())}),n=[{pubkey:e.accountPubkey,isSigner:!0,isWritable:!0}]}return new Me({keys:n,programId:this.programId,data:t})}static createAccountWithSeed(e){let t=He.CreateWithSeed,n=Oe(t,{base:Ae(e.basePubkey.toBuffer()),seed:e.seed,lamports:e.lamports,space:e.space,programId:Ae(e.programId.toBuffer())}),i=[{pubkey:e.fromPubkey,isSigner:!0,isWritable:!0},{pubkey:e.newAccountPubkey,isSigner:!1,isWritable:!0}];return e.basePubkey.equals(e.fromPubkey)||i.push({pubkey:e.basePubkey,isSigner:!0,isWritable:!1}),new Me({keys:i,programId:this.programId,data:n})}static createNonceAccount(e){let t=new De;"basePubkey"in e&&"seed"in e?t.add(r.createAccountWithSeed({fromPubkey:e.fromPubkey,newAccountPubkey:e.noncePubkey,basePubkey:e.basePubkey,seed:e.seed,lamports:e.lamports,space:Hc,programId:this.programId})):t.add(r.createAccount({fromPubkey:e.fromPubkey,newAccountPubkey:e.noncePubkey,lamports:e.lamports,space:Hc,programId:this.programId}));let n={noncePubkey:e.noncePubkey,authorizedPubkey:e.authorizedPubkey};return t.add(this.nonceInitialize(n)),t}static nonceInitialize(e){let t=He.InitializeNonceAccount,n=Oe(t,{authorized:Ae(e.authorizedPubkey.toBuffer())}),i={keys:[{pubkey:e.noncePubkey,isSigner:!1,isWritable:!0},{pubkey:Jo,isSigner:!1,isWritable:!1},{pubkey:Fn,isSigner:!1,isWritable:!1}],programId:this.programId,data:n};return new Me(i)}static nonceAdvance(e){let t=He.AdvanceNonceAccount,n=Oe(t),i={keys:[{pubkey:e.noncePubkey,isSigner:!1,isWritable:!0},{pubkey:Jo,isSigner:!1,isWritable:!1},{pubkey:e.authorizedPubkey,isSigner:!0,isWritable:!1}],programId:this.programId,data:n};return new Me(i)}static nonceWithdraw(e){let t=He.WithdrawNonceAccount,n=Oe(t,{lamports:e.lamports});return new Me({keys:[{pubkey:e.noncePubkey,isSigner:!1,isWritable:!0},{pubkey:e.toPubkey,isSigner:!1,isWritable:!0},{pubkey:Jo,isSigner:!1,isWritable:!1},{pubkey:Fn,isSigner:!1,isWritable:!1},{pubkey:e.authorizedPubkey,isSigner:!0,isWritable:!1}],programId:this.programId,data:n})}static nonceAuthorize(e){let t=He.AuthorizeNonceAccount,n=Oe(t,{authorized:Ae(e.newAuthorizedPubkey.toBuffer())});return new Me({keys:[{pubkey:e.noncePubkey,isSigner:!1,isWritable:!0},{pubkey:e.authorizedPubkey,isSigner:!0,isWritable:!1}],programId:this.programId,data:n})}static allocate(e){let t,n;if("basePubkey"in e){let i=He.AllocateWithSeed;t=Oe(i,{base:Ae(e.basePubkey.toBuffer()),seed:e.seed,space:e.space,programId:Ae(e.programId.toBuffer())}),n=[{pubkey:e.accountPubkey,isSigner:!1,isWritable:!0},{pubkey:e.basePubkey,isSigner:!0,isWritable:!1}]}else{let i=He.Allocate;t=Oe(i,{space:e.space}),n=[{pubkey:e.accountPubkey,isSigner:!0,isWritable:!0}]}return new Me({keys:n,programId:this.programId,data:t})}};xt.programId=new X("11111111111111111111111111111111");var oS=tn-300,Vn=class r{constructor(){}static getMinNumSignatures(e){return 2*(Math.ceil(e/r.chunkSize)+1+1)}static async load(e,t,n,i,o){{let g=await e.getMinimumBalanceForRentExemption(o.length),v=await e.getAccountInfo(n.publicKey,"confirmed"),m=null;if(v!==null){if(v.executable)return console.error("Program load failed, account is already executable"),!1;v.data.length!==o.length&&(m=m||new De,m.add(xt.allocate({accountPubkey:n.publicKey,space:o.length}))),v.owner.equals(i)||(m=m||new De,m.add(xt.assign({accountPubkey:n.publicKey,programId:i}))),v.lamports<g&&(m=m||new De,m.add(xt.transfer({fromPubkey:t.publicKey,toPubkey:n.publicKey,lamports:g-v.lamports})))}else m=new De().add(xt.createAccount({fromPubkey:t.publicKey,newAccountPubkey:n.publicKey,lamports:g>0?g:1,space:o.length,programId:i}));m!==null&&await Gc(e,m,[t,n],{commitment:"confirmed"})}let s=w.struct([w.u32("instruction"),w.u32("offset"),w.u32("bytesLength"),w.u32("bytesLengthPadding"),w.seq(w.u8("byte"),w.offset(w.u32(),-8),"bytes")]),u=r.chunkSize,l=0,p=o,R=[];for(;p.length>0;){let g=p.slice(0,u),v=se.Buffer.alloc(u+16);s.encode({instruction:0,offset:l,bytes:g,bytesLength:0,bytesLengthPadding:0},v);let m=new De().add({keys:[{pubkey:n.publicKey,isSigner:!0,isWritable:!0}],programId:i,data:v});R.push(Gc(e,m,[t,n],{commitment:"confirmed"})),e._rpcEndpoint.includes("solana.com")&&await Rn(1e3/4),l+=u,p=p.slice(u)}await Promise.all(R);{let g=w.struct([w.u32("instruction")]),v=se.Buffer.alloc(g.span);g.encode({instruction:1},v);let m=new De().add({keys:[{pubkey:n.publicKey,isSigner:!0,isWritable:!0},{pubkey:Fn,isSigner:!1,isWritable:!1}],programId:i,data:v}),x="processed",I=await e.sendTransaction(m,[t,n],{preflightCommitment:x}),{context:O,value:D}=await e.confirmTransaction({signature:I,lastValidBlockHeight:m.lastValidBlockHeight,blockhash:m.recentBlockhash},x);if(D.err)throw new Error(`Transaction ${I} failed (${JSON.stringify(D)})`);for(;;){try{if(await e.getSlot({commitment:x})>O.slot)break}catch{}await new Promise(k=>setTimeout(k,Math.round(GR/2)))}}return!0}};Vn.chunkSize=oS;var sS=new X("BPFLoader2111111111111111111111111111111111"),qc=class{static getMinNumSignatures(e){return Vn.getMinNumSignatures(e)}static load(e,t,n,i,o){return Vn.load(e,t,n,o,i)}};function aS(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}var Lc,mR;function cS(){if(mR)return Lc;mR=1;var r=Object.prototype.toString,e=Object.keys||function(n){var i=[];for(var o in n)i.push(o);return i};function t(n,i){var o,s,u,l,p,R,g;if(n===!0)return"true";if(n===!1)return"false";switch(typeof n){case"object":if(n===null)return null;if(n.toJSON&&typeof n.toJSON=="function")return t(n.toJSON(),i);if(g=r.call(n),g==="[object Array]"){for(u="[",s=n.length-1,o=0;o<s;o++)u+=t(n[o],!0)+",";return s>-1&&(u+=t(n[o],!0)),u+"]"}else if(g==="[object Object]"){for(l=e(n).sort(),s=l.length,u="",o=0;o<s;)p=l[o],R=t(n[p],!1),R!==void 0&&(u&&(u+=","),u+=JSON.stringify(p)+":"+R),o++;return"{"+u+"}"}else return JSON.stringify(n);case"function":case"undefined":return i?null:void 0;case"string":return JSON.stringify(n);default:return isFinite(n)?n:null}}return Lc=function(n){var i=t(n,!1);if(i!==void 0)return""+i},Lc}var uS=cS(),SR=aS(uS),mi=32;function kc(r){let e=0;for(;r>1;)r/=2,e++;return e}function lS(r){return r===0?1:(r--,r|=r>>1,r|=r>>2,r|=r>>4,r|=r>>8,r|=r>>16,r|=r>>32,r+1)}var ns=class{constructor(e,t,n,i,o){this.slotsPerEpoch=void 0,this.leaderScheduleSlotOffset=void 0,this.warmup=void 0,this.firstNormalEpoch=void 0,this.firstNormalSlot=void 0,this.slotsPerEpoch=e,this.leaderScheduleSlotOffset=t,this.warmup=n,this.firstNormalEpoch=i,this.firstNormalSlot=o}getEpoch(e){return this.getEpochAndSlotIndex(e)[0]}getEpochAndSlotIndex(e){if(e<this.firstNormalSlot){let t=kc(lS(e+mi+1))-kc(mi)-1,n=this.getSlotsInEpoch(t),i=e-(n-mi);return[t,i]}else{let t=e-this.firstNormalSlot,n=Math.floor(t/this.slotsPerEpoch),i=this.firstNormalEpoch+n,o=t%this.slotsPerEpoch;return[i,o]}}getFirstSlotInEpoch(e){return e<=this.firstNormalEpoch?(Math.pow(2,e)-1)*mi:(e-this.firstNormalEpoch)*this.slotsPerEpoch+this.firstNormalSlot}getLastSlotInEpoch(e){return this.getFirstSlotInEpoch(e)+this.getSlotsInEpoch(e)-1}getSlotsInEpoch(e){return e<this.firstNormalEpoch?Math.pow(2,e+kc(mi)):this.slotsPerEpoch}},fS=globalThis.fetch,Wc=class extends pR.CommonClient{constructor(e,t,n){let i=o=>{let s=pR.WebSocket(o,{autoconnect:!0,max_reconnects:5,reconnect:!0,reconnect_interval:1e3,...t});return"socket"in s?this.underlyingSocket=s.socket:this.underlyingSocket=s,s};super(i,e,t,n),this.underlyingSocket=void 0}call(...e){let t=this.underlyingSocket?.readyState;return t===1?super.call(...e):Promise.reject(new Error("Tried to call a JSON-RPC method `"+e[0]+"` but the socket was not `CONNECTING` or `OPEN` (`readyState` was "+t+")"))}notify(...e){let t=this.underlyingSocket?.readyState;return t===1?super.notify(...e):Promise.reject(new Error("Tried to send a JSON-RPC notification `"+e[0]+"` but the socket was not `CONNECTING` or `OPEN` (`readyState` was "+t+")"))}};function dS(r,e){let t;try{t=r.layout.decode(e)}catch(n){throw new Error("invalid instruction; "+n)}if(t.typeIndex!==r.index)throw new Error(`invalid account data; account type mismatch ${t.typeIndex} != ${r.index}`);return t}var bR=56,Ni=class{constructor(e){this.key=void 0,this.state=void 0,this.key=e.key,this.state=e.state}isActive(){let e=BigInt("0xffffffffffffffff");return this.state.deactivationSlot===e}static deserialize(e){let t=dS(hS,e),n=e.length-bR;Te(n>=0,"lookup table is invalid"),Te(n%32===0,"lookup table is invalid");let i=n/32,{addresses:o}=w.struct([w.seq(Se(),i,"addresses")]).decode(e.slice(bR));return{deactivationSlot:t.deactivationSlot,lastExtendedSlot:t.lastExtendedSlot,lastExtendedSlotStartIndex:t.lastExtendedStartIndex,authority:t.authority.length!==0?new X(t.authority[0]):void 0,addresses:o.map(s=>new X(s))}}},hS={index:1,layout:w.struct([w.u32("typeIndex"),zn("deactivationSlot"),w.nu64("lastExtendedSlot"),w.u8("lastExtendedStartIndex"),w.u8(),w.seq(Se(),w.offset(w.u8(),-1),"authority")])},_S=/^[^:]+:\/\/([^:[]+|\[[^\]]+\])(:\d+)?(.*)/i;function pS(r){let e=r.match(_S);if(e==null)throw TypeError(`Failed to validate endpoint URL \`${r}\``);let[t,n,i,o]=e,s=r.startsWith("https:")?"wss:":"ws:",u=i==null?null:parseInt(i.slice(1),10),l=u==null?"":`:${u+1}`;return`${s}//${n}${l}${o}`}var et=c.coerce(c.instance(X),c.string(),r=>new X(r)),qR=c.tuple([c.string(),c.literal("base64")]),au=c.coerce(c.instance(se.Buffer),qR,r=>se.Buffer.from(r[0],"base64")),WR=30*1e3;function RS(r){if(/^https?:/.test(r)===!1)throw new TypeError("Endpoint URL must start with `http:` or `https:`.");return r}function Ge(r){let e,t;if(typeof r=="string")e=r;else if(r){let{commitment:n,...i}=r;e=n,t=i}return{commitment:e,config:t}}function OR(r){return r.map(e=>"memcmp"in e?{...e,memcmp:{...e.memcmp,encoding:e.memcmp.encoding??"base58"}}:e)}function $R(r){return c.union([c.type({jsonrpc:c.literal("2.0"),id:c.string(),result:r}),c.type({jsonrpc:c.literal("2.0"),id:c.string(),error:c.type({code:c.unknown(),message:c.string(),data:c.optional(c.any())})})])}var ES=$R(c.unknown());function ye(r){return c.coerce($R(r),ES,e=>"error"in e?e:{...e,result:c.create(e.result,r)})}function nt(r){return ye(c.type({context:c.type({slot:c.number()}),value:r}))}function fs(r){return c.type({context:c.type({slot:c.number()}),value:r})}function Bc(r,e){return r===0?new Pn({header:e.header,staticAccountKeys:e.accountKeys.map(t=>new X(t)),recentBlockhash:e.recentBlockhash,compiledInstructions:e.instructions.map(t=>({programIdIndex:t.programIdIndex,accountKeyIndexes:t.accounts,data:Ct.default.decode(t.data)})),addressTableLookups:e.addressTableLookups}):new Or(e)}var gS=c.type({foundation:c.number(),foundationTerm:c.number(),initial:c.number(),taper:c.number(),terminal:c.number()}),yS=ye(c.array(c.nullable(c.type({epoch:c.number(),effectiveSlot:c.number(),amount:c.number(),postBalance:c.number(),commission:c.optional(c.nullable(c.number()))})))),AS=c.array(c.type({slot:c.number(),prioritizationFee:c.number()})),mS=c.type({total:c.number(),validator:c.number(),foundation:c.number(),epoch:c.number()}),SS=c.type({epoch:c.number(),slotIndex:c.number(),slotsInEpoch:c.number(),absoluteSlot:c.number(),blockHeight:c.optional(c.number()),transactionCount:c.optional(c.number())}),bS=c.type({slotsPerEpoch:c.number(),leaderScheduleSlotOffset:c.number(),warmup:c.boolean(),firstNormalEpoch:c.number(),firstNormalSlot:c.number()}),OS=c.record(c.string(),c.array(c.number())),mn=c.nullable(c.union([c.type({}),c.string()])),wS=c.type({err:mn}),IS=c.literal("receivedSignature"),NS=c.type({"solana-core":c.string(),"feature-set":c.optional(c.number())}),TS=c.type({program:c.string(),programId:et,parsed:c.unknown()}),vS=c.type({programId:et,accounts:c.array(et),data:c.string()}),wR=nt(c.type({err:c.nullable(c.union([c.type({}),c.string()])),logs:c.nullable(c.array(c.string())),accounts:c.optional(c.nullable(c.array(c.nullable(c.type({executable:c.boolean(),owner:c.string(),lamports:c.number(),data:c.array(c.string()),rentEpoch:c.optional(c.number())}))))),unitsConsumed:c.optional(c.number()),returnData:c.optional(c.nullable(c.type({programId:c.string(),data:c.tuple([c.string(),c.literal("base64")])}))),innerInstructions:c.optional(c.nullable(c.array(c.type({index:c.number(),instructions:c.array(c.union([TS,vS]))}))))})),CS=nt(c.type({byIdentity:c.record(c.string(),c.array(c.number())),range:c.type({firstSlot:c.number(),lastSlot:c.number()})}));function xS(r,e,t,n,i,o){let s=t||fS,u;o!=null&&console.warn("You have supplied an `httpAgent` when creating a `Connection` in a browser environment.It has been ignored; `httpAgent` is only used in Node environments.");let l;return n&&(l=async(R,g)=>{let v=await new Promise((m,x)=>{try{n(R,g,(I,O)=>m([I,O]))}catch(I){x(I)}});return await s(...v)}),new Fm.default(async(R,g)=>{let v={method:"POST",body:R,agent:u,headers:Object.assign({"Content-Type":"application/json"},e||{},x1)};try{let m=5,x,I=500;for(;l?x=await l(r,v):x=await s(r,v),!(x.status!==429||i===!0||(m-=1,m===0));)console.error(`Server responded with ${x.status} ${x.statusText}.  Retrying after ${I}ms delay...`),await Rn(I),I*=2;let O=await x.text();x.ok?g(null,O):g(new Error(`${x.status} ${x.statusText}: ${O}`))}catch(m){m instanceof Error&&g(m)}},{})}function LS(r){return(e,t)=>new Promise((n,i)=>{r.request(e,t,(o,s)=>{if(o){i(o);return}n(s)})})}function kS(r){return e=>new Promise((t,n)=>{e.length===0&&t([]);let i=e.map(o=>r.request(o.methodName,o.args));r.request(i,(o,s)=>{if(o){n(o);return}t(s)})})}var BS=ye(gS),DS=ye(mS),MS=ye(AS),US=ye(SS),PS=ye(bS),FS=ye(OS),zS=ye(c.number()),VS=nt(c.type({total:c.number(),circulating:c.number(),nonCirculating:c.number(),nonCirculatingAccounts:c.array(et)})),$c=c.type({amount:c.string(),uiAmount:c.nullable(c.number()),decimals:c.number(),uiAmountString:c.optional(c.string())}),GS=nt(c.array(c.type({address:et,amount:c.string(),uiAmount:c.nullable(c.number()),decimals:c.number(),uiAmountString:c.optional(c.string())}))),HS=nt(c.array(c.type({pubkey:et,account:c.type({executable:c.boolean(),owner:et,lamports:c.number(),data:au,rentEpoch:c.number()})}))),Yc=c.type({program:c.string(),parsed:c.unknown(),space:c.number()}),KS=nt(c.array(c.type({pubkey:et,account:c.type({executable:c.boolean(),owner:et,lamports:c.number(),data:Yc,rentEpoch:c.number()})}))),qS=nt(c.array(c.type({lamports:c.number(),address:et}))),Ti=c.type({executable:c.boolean(),owner:et,lamports:c.number(),data:au,rentEpoch:c.number()}),WS=c.type({pubkey:et,account:Ti}),$S=c.coerce(c.union([c.instance(se.Buffer),Yc]),c.union([qR,Yc]),r=>Array.isArray(r)?c.create(r,au):r),jc=c.type({executable:c.boolean(),owner:et,lamports:c.number(),data:$S,rentEpoch:c.number()}),YS=c.type({pubkey:et,account:jc}),jS=c.type({state:c.union([c.literal("active"),c.literal("inactive"),c.literal("activating"),c.literal("deactivating")]),active:c.number(),inactive:c.number()}),XS=ye(c.array(c.type({signature:c.string(),slot:c.number(),err:mn,memo:c.nullable(c.string()),blockTime:c.optional(c.nullable(c.number()))}))),ZS=ye(c.array(c.type({signature:c.string(),slot:c.number(),err:mn,memo:c.nullable(c.string()),blockTime:c.optional(c.nullable(c.number()))}))),JS=c.type({subscription:c.number(),result:fs(Ti)}),QS=c.type({pubkey:et,account:Ti}),e1=c.type({subscription:c.number(),result:fs(QS)}),t1=c.type({parent:c.number(),slot:c.number(),root:c.number()}),r1=c.type({subscription:c.number(),result:t1}),n1=c.union([c.type({type:c.union([c.literal("firstShredReceived"),c.literal("completed"),c.literal("optimisticConfirmation"),c.literal("root")]),slot:c.number(),timestamp:c.number()}),c.type({type:c.literal("createdBank"),parent:c.number(),slot:c.number(),timestamp:c.number()}),c.type({type:c.literal("frozen"),slot:c.number(),timestamp:c.number(),stats:c.type({numTransactionEntries:c.number(),numSuccessfulTransactions:c.number(),numFailedTransactions:c.number(),maxTransactionsPerEntry:c.number()})}),c.type({type:c.literal("dead"),slot:c.number(),timestamp:c.number(),err:c.string()})]),i1=c.type({subscription:c.number(),result:n1}),o1=c.type({subscription:c.number(),result:fs(c.union([wS,IS]))}),s1=c.type({subscription:c.number(),result:c.number()}),a1=c.type({pubkey:c.string(),gossip:c.nullable(c.string()),tpu:c.nullable(c.string()),rpc:c.nullable(c.string()),version:c.nullable(c.string())}),IR=c.type({votePubkey:c.string(),nodePubkey:c.string(),activatedStake:c.number(),epochVoteAccount:c.boolean(),epochCredits:c.array(c.tuple([c.number(),c.number(),c.number()])),commission:c.number(),lastVote:c.number(),rootSlot:c.nullable(c.number())}),c1=ye(c.type({current:c.array(IR),delinquent:c.array(IR)})),u1=c.union([c.literal("processed"),c.literal("confirmed"),c.literal("finalized")]),l1=c.type({slot:c.number(),confirmations:c.nullable(c.number()),err:mn,confirmationStatus:c.optional(u1)}),f1=nt(c.array(c.nullable(l1))),d1=ye(c.number()),YR=c.type({accountKey:et,writableIndexes:c.array(c.number()),readonlyIndexes:c.array(c.number())}),cu=c.type({signatures:c.array(c.string()),message:c.type({accountKeys:c.array(c.string()),header:c.type({numRequiredSignatures:c.number(),numReadonlySignedAccounts:c.number(),numReadonlyUnsignedAccounts:c.number()}),instructions:c.array(c.type({accounts:c.array(c.number()),data:c.string(),programIdIndex:c.number()})),recentBlockhash:c.string(),addressTableLookups:c.optional(c.array(YR))})}),jR=c.type({pubkey:et,signer:c.boolean(),writable:c.boolean(),source:c.optional(c.union([c.literal("transaction"),c.literal("lookupTable")]))}),XR=c.type({accountKeys:c.array(jR),signatures:c.array(c.string())}),ZR=c.type({parsed:c.unknown(),program:c.string(),programId:et}),JR=c.type({accounts:c.array(et),data:c.string(),programId:et}),h1=c.union([JR,ZR]),_1=c.union([c.type({parsed:c.unknown(),program:c.string(),programId:c.string()}),c.type({accounts:c.array(c.string()),data:c.string(),programId:c.string()})]),QR=c.coerce(h1,_1,r=>"accounts"in r?c.create(r,JR):c.create(r,ZR)),eE=c.type({signatures:c.array(c.string()),message:c.type({accountKeys:c.array(jR),instructions:c.array(QR),recentBlockhash:c.string(),addressTableLookups:c.optional(c.nullable(c.array(YR)))})}),is=c.type({accountIndex:c.number(),mint:c.string(),owner:c.optional(c.string()),programId:c.optional(c.string()),uiTokenAmount:$c}),tE=c.type({writable:c.array(et),readonly:c.array(et)}),ds=c.type({err:mn,fee:c.number(),innerInstructions:c.optional(c.nullable(c.array(c.type({index:c.number(),instructions:c.array(c.type({accounts:c.array(c.number()),data:c.string(),programIdIndex:c.number()}))})))),preBalances:c.array(c.number()),postBalances:c.array(c.number()),logMessages:c.optional(c.nullable(c.array(c.string()))),preTokenBalances:c.optional(c.nullable(c.array(is))),postTokenBalances:c.optional(c.nullable(c.array(is))),loadedAddresses:c.optional(tE),computeUnitsConsumed:c.optional(c.number())}),uu=c.type({err:mn,fee:c.number(),innerInstructions:c.optional(c.nullable(c.array(c.type({index:c.number(),instructions:c.array(QR)})))),preBalances:c.array(c.number()),postBalances:c.array(c.number()),logMessages:c.optional(c.nullable(c.array(c.string()))),preTokenBalances:c.optional(c.nullable(c.array(is))),postTokenBalances:c.optional(c.nullable(c.array(is))),loadedAddresses:c.optional(tE),computeUnitsConsumed:c.optional(c.number())}),qn=c.union([c.literal(0),c.literal("legacy")]),Sn=c.type({pubkey:c.string(),lamports:c.number(),postBalance:c.nullable(c.number()),rewardType:c.nullable(c.string()),commission:c.optional(c.nullable(c.number()))}),p1=ye(c.nullable(c.type({blockhash:c.string(),previousBlockhash:c.string(),parentSlot:c.number(),transactions:c.array(c.type({transaction:cu,meta:c.nullable(ds),version:c.optional(qn)})),rewards:c.optional(c.array(Sn)),blockTime:c.nullable(c.number()),blockHeight:c.nullable(c.number())}))),R1=ye(c.nullable(c.type({blockhash:c.string(),previousBlockhash:c.string(),parentSlot:c.number(),rewards:c.optional(c.array(Sn)),blockTime:c.nullable(c.number()),blockHeight:c.nullable(c.number())}))),E1=ye(c.nullable(c.type({blockhash:c.string(),previousBlockhash:c.string(),parentSlot:c.number(),transactions:c.array(c.type({transaction:XR,meta:c.nullable(ds),version:c.optional(qn)})),rewards:c.optional(c.array(Sn)),blockTime:c.nullable(c.number()),blockHeight:c.nullable(c.number())}))),g1=ye(c.nullable(c.type({blockhash:c.string(),previousBlockhash:c.string(),parentSlot:c.number(),transactions:c.array(c.type({transaction:eE,meta:c.nullable(uu),version:c.optional(qn)})),rewards:c.optional(c.array(Sn)),blockTime:c.nullable(c.number()),blockHeight:c.nullable(c.number())}))),y1=ye(c.nullable(c.type({blockhash:c.string(),previousBlockhash:c.string(),parentSlot:c.number(),transactions:c.array(c.type({transaction:XR,meta:c.nullable(uu),version:c.optional(qn)})),rewards:c.optional(c.array(Sn)),blockTime:c.nullable(c.number()),blockHeight:c.nullable(c.number())}))),A1=ye(c.nullable(c.type({blockhash:c.string(),previousBlockhash:c.string(),parentSlot:c.number(),rewards:c.optional(c.array(Sn)),blockTime:c.nullable(c.number()),blockHeight:c.nullable(c.number())}))),m1=ye(c.nullable(c.type({blockhash:c.string(),previousBlockhash:c.string(),parentSlot:c.number(),transactions:c.array(c.type({transaction:cu,meta:c.nullable(ds)})),rewards:c.optional(c.array(Sn)),blockTime:c.nullable(c.number())}))),NR=ye(c.nullable(c.type({blockhash:c.string(),previousBlockhash:c.string(),parentSlot:c.number(),signatures:c.array(c.string()),blockTime:c.nullable(c.number())}))),Dc=ye(c.nullable(c.type({slot:c.number(),meta:c.nullable(ds),blockTime:c.optional(c.nullable(c.number())),transaction:cu,version:c.optional(qn)}))),Zo=ye(c.nullable(c.type({slot:c.number(),transaction:eE,meta:c.nullable(uu),blockTime:c.optional(c.nullable(c.number())),version:c.optional(qn)}))),S1=nt(c.type({blockhash:c.string(),lastValidBlockHeight:c.number()})),b1=nt(c.boolean()),O1=c.type({slot:c.number(),numTransactions:c.number(),numSlots:c.number(),samplePeriodSecs:c.number()}),w1=ye(c.array(O1)),I1=nt(c.nullable(c.type({feeCalculator:c.type({lamportsPerSignature:c.number()})}))),N1=ye(c.string()),T1=ye(c.string()),v1=c.type({err:mn,logs:c.array(c.string()),signature:c.string()}),C1=c.type({result:fs(v1),subscription:c.number()}),x1={"solana-client":"js/1.0.0-maintenance"},Xc=class{constructor(e,t){this._commitment=void 0,this._confirmTransactionInitialTimeout=void 0,this._rpcEndpoint=void 0,this._rpcWsEndpoint=void 0,this._rpcClient=void 0,this._rpcRequest=void 0,this._rpcBatchRequest=void 0,this._rpcWebSocket=void 0,this._rpcWebSocketConnected=!1,this._rpcWebSocketHeartbeat=null,this._rpcWebSocketIdleTimeout=null,this._rpcWebSocketGeneration=0,this._disableBlockhashCaching=!1,this._pollingBlockhash=!1,this._blockhashInfo={latestBlockhash:null,lastFetch:0,transactionSignatures:[],simulatedSignatures:[]},this._nextClientSubscriptionId=0,this._subscriptionDisposeFunctionsByClientSubscriptionId={},this._subscriptionHashByClientSubscriptionId={},this._subscriptionStateChangeCallbacksByHash={},this._subscriptionCallbacksByServerSubscriptionId={},this._subscriptionsByHash={},this._subscriptionsAutoDisposedByRpc=new Set,this.getBlockHeight=(()=>{let p={};return async R=>{let{commitment:g,config:v}=Ge(R),m=this._buildArgs([],g,void 0,v),x=SR(m);return p[x]=p[x]??(async()=>{try{let I=await this._rpcRequest("getBlockHeight",m),O=c.create(I,ye(c.number()));if("error"in O)throw new re(O.error,"failed to get block height information");return O.result}finally{delete p[x]}})(),await p[x]}})();let n,i,o,s,u,l;t&&typeof t=="string"?this._commitment=t:t&&(this._commitment=t.commitment,this._confirmTransactionInitialTimeout=t.confirmTransactionInitialTimeout,n=t.wsEndpoint,i=t.httpHeaders,o=t.fetch,s=t.fetchMiddleware,u=t.disableRetryOnRateLimit,l=t.httpAgent),this._rpcEndpoint=RS(e),this._rpcWsEndpoint=n||pS(e),this._rpcClient=xS(e,i,o,s,u,l),this._rpcRequest=LS(this._rpcClient),this._rpcBatchRequest=kS(this._rpcClient),this._rpcWebSocket=new Wc(this._rpcWsEndpoint,{autoconnect:!1,max_reconnects:1/0}),this._rpcWebSocket.on("open",this._wsOnOpen.bind(this)),this._rpcWebSocket.on("error",this._wsOnError.bind(this)),this._rpcWebSocket.on("close",this._wsOnClose.bind(this)),this._rpcWebSocket.on("accountNotification",this._wsOnAccountNotification.bind(this)),this._rpcWebSocket.on("programNotification",this._wsOnProgramAccountNotification.bind(this)),this._rpcWebSocket.on("slotNotification",this._wsOnSlotNotification.bind(this)),this._rpcWebSocket.on("slotsUpdatesNotification",this._wsOnSlotUpdatesNotification.bind(this)),this._rpcWebSocket.on("signatureNotification",this._wsOnSignatureNotification.bind(this)),this._rpcWebSocket.on("rootNotification",this._wsOnRootNotification.bind(this)),this._rpcWebSocket.on("logsNotification",this._wsOnLogsNotification.bind(this))}get commitment(){return this._commitment}get rpcEndpoint(){return this._rpcEndpoint}async getBalanceAndContext(e,t){let{commitment:n,config:i}=Ge(t),o=this._buildArgs([e.toBase58()],n,void 0,i),s=await this._rpcRequest("getBalance",o),u=c.create(s,nt(c.number()));if("error"in u)throw new re(u.error,`failed to get balance for ${e.toBase58()}`);return u.result}async getBalance(e,t){return await this.getBalanceAndContext(e,t).then(n=>n.value).catch(n=>{throw new Error("failed to get balance of account "+e.toBase58()+": "+n)})}async getBlockTime(e){let t=await this._rpcRequest("getBlockTime",[e]),n=c.create(t,ye(c.nullable(c.number())));if("error"in n)throw new re(n.error,`failed to get block time for slot ${e}`);return n.result}async getMinimumLedgerSlot(){let e=await this._rpcRequest("minimumLedgerSlot",[]),t=c.create(e,ye(c.number()));if("error"in t)throw new re(t.error,"failed to get minimum ledger slot");return t.result}async getFirstAvailableBlock(){let e=await this._rpcRequest("getFirstAvailableBlock",[]),t=c.create(e,zS);if("error"in t)throw new re(t.error,"failed to get first available block");return t.result}async getSupply(e){let t={};typeof e=="string"?t={commitment:e}:e?t={...e,commitment:e&&e.commitment||this.commitment}:t={commitment:this.commitment};let n=await this._rpcRequest("getSupply",[t]),i=c.create(n,VS);if("error"in i)throw new re(i.error,"failed to get supply");return i.result}async getTokenSupply(e,t){let n=this._buildArgs([e.toBase58()],t),i=await this._rpcRequest("getTokenSupply",n),o=c.create(i,nt($c));if("error"in o)throw new re(o.error,"failed to get token supply");return o.result}async getTokenAccountBalance(e,t){let n=this._buildArgs([e.toBase58()],t),i=await this._rpcRequest("getTokenAccountBalance",n),o=c.create(i,nt($c));if("error"in o)throw new re(o.error,"failed to get token account balance");return o.result}async getTokenAccountsByOwner(e,t,n){let{commitment:i,config:o}=Ge(n),s=[e.toBase58()];"mint"in t?s.push({mint:t.mint.toBase58()}):s.push({programId:t.programId.toBase58()});let u=this._buildArgs(s,i,"base64",o),l=await this._rpcRequest("getTokenAccountsByOwner",u),p=c.create(l,HS);if("error"in p)throw new re(p.error,`failed to get token accounts owned by account ${e.toBase58()}`);return p.result}async getParsedTokenAccountsByOwner(e,t,n){let i=[e.toBase58()];"mint"in t?i.push({mint:t.mint.toBase58()}):i.push({programId:t.programId.toBase58()});let o=this._buildArgs(i,n,"jsonParsed"),s=await this._rpcRequest("getTokenAccountsByOwner",o),u=c.create(s,KS);if("error"in u)throw new re(u.error,`failed to get token accounts owned by account ${e.toBase58()}`);return u.result}async getLargestAccounts(e){let t={...e,commitment:e&&e.commitment||this.commitment},n=t.filter||t.commitment?[t]:[],i=await this._rpcRequest("getLargestAccounts",n),o=c.create(i,qS);if("error"in o)throw new re(o.error,"failed to get largest accounts");return o.result}async getTokenLargestAccounts(e,t){let n=this._buildArgs([e.toBase58()],t),i=await this._rpcRequest("getTokenLargestAccounts",n),o=c.create(i,GS);if("error"in o)throw new re(o.error,"failed to get token largest accounts");return o.result}async getAccountInfoAndContext(e,t){let{commitment:n,config:i}=Ge(t),o=this._buildArgs([e.toBase58()],n,"base64",i),s=await this._rpcRequest("getAccountInfo",o),u=c.create(s,nt(c.nullable(Ti)));if("error"in u)throw new re(u.error,`failed to get info about account ${e.toBase58()}`);return u.result}async getParsedAccountInfo(e,t){let{commitment:n,config:i}=Ge(t),o=this._buildArgs([e.toBase58()],n,"jsonParsed",i),s=await this._rpcRequest("getAccountInfo",o),u=c.create(s,nt(c.nullable(jc)));if("error"in u)throw new re(u.error,`failed to get info about account ${e.toBase58()}`);return u.result}async getAccountInfo(e,t){try{return(await this.getAccountInfoAndContext(e,t)).value}catch(n){throw new Error("failed to get info about account "+e.toBase58()+": "+n)}}async getMultipleParsedAccounts(e,t){let{commitment:n,config:i}=Ge(t),o=e.map(p=>p.toBase58()),s=this._buildArgs([o],n,"jsonParsed",i),u=await this._rpcRequest("getMultipleAccounts",s),l=c.create(u,nt(c.array(c.nullable(jc))));if("error"in l)throw new re(l.error,`failed to get info for accounts ${o}`);return l.result}async getMultipleAccountsInfoAndContext(e,t){let{commitment:n,config:i}=Ge(t),o=e.map(p=>p.toBase58()),s=this._buildArgs([o],n,"base64",i),u=await this._rpcRequest("getMultipleAccounts",s),l=c.create(u,nt(c.array(c.nullable(Ti))));if("error"in l)throw new re(l.error,`failed to get info for accounts ${o}`);return l.result}async getMultipleAccountsInfo(e,t){return(await this.getMultipleAccountsInfoAndContext(e,t)).value}async getStakeActivation(e,t,n){let{commitment:i,config:o}=Ge(t),s=this._buildArgs([e.toBase58()],i,void 0,{...o,epoch:n??o?.epoch}),u=await this._rpcRequest("getStakeActivation",s),l=c.create(u,ye(jS));if("error"in l)throw new re(l.error,`failed to get Stake Activation ${e.toBase58()}`);return l.result}async getProgramAccounts(e,t){let{commitment:n,config:i}=Ge(t),{encoding:o,...s}=i||{},u=this._buildArgs([e.toBase58()],n,o||"base64",{...s,...s.filters?{filters:OR(s.filters)}:null}),l=await this._rpcRequest("getProgramAccounts",u),p=c.array(WS),R=s.withContext===!0?c.create(l,nt(p)):c.create(l,ye(p));if("error"in R)throw new re(R.error,`failed to get accounts owned by program ${e.toBase58()}`);return R.result}async getParsedProgramAccounts(e,t){let{commitment:n,config:i}=Ge(t),o=this._buildArgs([e.toBase58()],n,"jsonParsed",i),s=await this._rpcRequest("getProgramAccounts",o),u=c.create(s,ye(c.array(YS)));if("error"in u)throw new re(u.error,`failed to get accounts owned by program ${e.toBase58()}`);return u.result}async confirmTransaction(e,t){let n;if(typeof e=="string")n=e;else{let o=e;if(o.abortSignal?.aborted)return Promise.reject(o.abortSignal.reason);n=o.signature}let i;try{i=Ct.default.decode(n)}catch{throw new Error("signature must be base58 encoded: "+n)}return Te(i.length===64,"signature has invalid length"),typeof e=="string"?await this.confirmTransactionUsingLegacyTimeoutStrategy({commitment:t||this.commitment,signature:n}):"lastValidBlockHeight"in e?await this.confirmTransactionUsingBlockHeightExceedanceStrategy({commitment:t||this.commitment,strategy:e}):await this.confirmTransactionUsingDurableNonceStrategy({commitment:t||this.commitment,strategy:e})}getCancellationPromise(e){return new Promise((t,n)=>{e!=null&&(e.aborted?n(e.reason):e.addEventListener("abort",()=>{n(e.reason)}))})}getTransactionConfirmationPromise({commitment:e,signature:t}){let n,i,o=!1,s=new Promise((l,p)=>{try{n=this.onSignature(t,(g,v)=>{n=void 0;let m={context:v,value:g};l({__type:kr.PROCESSED,response:m})},e);let R=new Promise(g=>{n==null?g():i=this._onSubscriptionStateChange(n,v=>{v==="subscribed"&&g()})});(async()=>{if(await R,o)return;let g=await this.getSignatureStatus(t);if(o||g==null)return;let{context:v,value:m}=g;if(m!=null)if(m?.err)p(m.err);else{switch(e){case"confirmed":case"single":case"singleGossip":{if(m.confirmationStatus==="processed")return;break}case"finalized":case"max":case"root":{if(m.confirmationStatus==="processed"||m.confirmationStatus==="confirmed")return;break}case"processed":case"recent":}o=!0,l({__type:kr.PROCESSED,response:{context:v,value:m}})}})()}catch(R){p(R)}});return{abortConfirmation:()=>{i&&(i(),i=void 0),n!=null&&(this.removeSignatureListener(n),n=void 0)},confirmationPromise:s}}async confirmTransactionUsingBlockHeightExceedanceStrategy({commitment:e,strategy:{abortSignal:t,lastValidBlockHeight:n,signature:i}}){let o=!1,s=new Promise(g=>{let v=async()=>{try{return await this.getBlockHeight(e)}catch{return-1}};(async()=>{let m=await v();if(!o){for(;m<=n;)if(await Rn(1e3),o||(m=await v(),o))return;g({__type:kr.BLOCKHEIGHT_EXCEEDED})}})()}),{abortConfirmation:u,confirmationPromise:l}=this.getTransactionConfirmationPromise({commitment:e,signature:i}),p=this.getCancellationPromise(t),R;try{let g=await Promise.race([p,l,s]);if(g.__type===kr.PROCESSED)R=g.response;else throw new wi(i)}finally{o=!0,u()}return R}async confirmTransactionUsingDurableNonceStrategy({commitment:e,strategy:{abortSignal:t,minContextSlot:n,nonceAccountPubkey:i,nonceValue:o,signature:s}}){let u=!1,l=new Promise(m=>{let x=o,I=null,O=async()=>{try{let{context:D,value:k}=await this.getNonceAndContext(i,{commitment:e,minContextSlot:n});return I=D.slot,k?.nonce}catch{return x}};(async()=>{if(x=await O(),!u)for(;;){if(o!==x){m({__type:kr.NONCE_INVALID,slotInWhichNonceDidAdvance:I});return}if(await Rn(2e3),u||(x=await O(),u))return}})()}),{abortConfirmation:p,confirmationPromise:R}=this.getTransactionConfirmationPromise({commitment:e,signature:s}),g=this.getCancellationPromise(t),v;try{let m=await Promise.race([g,R,l]);if(m.__type===kr.PROCESSED)v=m.response;else{let x;for(;;){let I=await this.getSignatureStatus(s);if(I==null)break;if(I.context.slot<(m.slotInWhichNonceDidAdvance??n)){await Rn(400);continue}x=I;break}if(x?.value){let I=e||"finalized",{confirmationStatus:O}=x.value;switch(I){case"processed":case"recent":if(O!=="processed"&&O!=="confirmed"&&O!=="finalized")throw new en(s);break;case"confirmed":case"single":case"singleGossip":if(O!=="confirmed"&&O!=="finalized")throw new en(s);break;case"finalized":case"max":case"root":if(O!=="finalized")throw new en(s);break;default:}v={context:x.context,value:{err:x.value.err}}}else throw new en(s)}}finally{u=!0,p()}return v}async confirmTransactionUsingLegacyTimeoutStrategy({commitment:e,signature:t}){let n,i=new Promise(l=>{let p=this._confirmTransactionInitialTimeout||6e4;switch(e){case"processed":case"recent":case"single":case"confirmed":case"singleGossip":{p=this._confirmTransactionInitialTimeout||3e4;break}}n=setTimeout(()=>l({__type:kr.TIMED_OUT,timeoutMs:p}),p)}),{abortConfirmation:o,confirmationPromise:s}=this.getTransactionConfirmationPromise({commitment:e,signature:t}),u;try{let l=await Promise.race([s,i]);if(l.__type===kr.PROCESSED)u=l.response;else throw new Ii(t,l.timeoutMs/1e3)}finally{clearTimeout(n),o()}return u}async getClusterNodes(){let e=await this._rpcRequest("getClusterNodes",[]),t=c.create(e,ye(c.array(a1)));if("error"in t)throw new re(t.error,"failed to get cluster nodes");return t.result}async getVoteAccounts(e){let t=this._buildArgs([],e),n=await this._rpcRequest("getVoteAccounts",t),i=c.create(n,c1);if("error"in i)throw new re(i.error,"failed to get vote accounts");return i.result}async getSlot(e){let{commitment:t,config:n}=Ge(e),i=this._buildArgs([],t,void 0,n),o=await this._rpcRequest("getSlot",i),s=c.create(o,ye(c.number()));if("error"in s)throw new re(s.error,"failed to get slot");return s.result}async getSlotLeader(e){let{commitment:t,config:n}=Ge(e),i=this._buildArgs([],t,void 0,n),o=await this._rpcRequest("getSlotLeader",i),s=c.create(o,ye(c.string()));if("error"in s)throw new re(s.error,"failed to get slot leader");return s.result}async getSlotLeaders(e,t){let n=[e,t],i=await this._rpcRequest("getSlotLeaders",n),o=c.create(i,ye(c.array(et)));if("error"in o)throw new re(o.error,"failed to get slot leaders");return o.result}async getSignatureStatus(e,t){let{context:n,value:i}=await this.getSignatureStatuses([e],t);Te(i.length===1);let o=i[0];return{context:n,value:o}}async getSignatureStatuses(e,t){let n=[e];t&&n.push(t);let i=await this._rpcRequest("getSignatureStatuses",n),o=c.create(i,f1);if("error"in o)throw new re(o.error,"failed to get signature status");return o.result}async getTransactionCount(e){let{commitment:t,config:n}=Ge(e),i=this._buildArgs([],t,void 0,n),o=await this._rpcRequest("getTransactionCount",i),s=c.create(o,ye(c.number()));if("error"in s)throw new re(s.error,"failed to get transaction count");return s.result}async getTotalSupply(e){return(await this.getSupply({commitment:e,excludeNonCirculatingAccountsList:!0})).value.total}async getInflationGovernor(e){let t=this._buildArgs([],e),n=await this._rpcRequest("getInflationGovernor",t),i=c.create(n,BS);if("error"in i)throw new re(i.error,"failed to get inflation");return i.result}async getInflationReward(e,t,n){let{commitment:i,config:o}=Ge(n),s=this._buildArgs([e.map(p=>p.toBase58())],i,void 0,{...o,epoch:t??o?.epoch}),u=await this._rpcRequest("getInflationReward",s),l=c.create(u,yS);if("error"in l)throw new re(l.error,"failed to get inflation reward");return l.result}async getInflationRate(){let e=await this._rpcRequest("getInflationRate",[]),t=c.create(e,DS);if("error"in t)throw new re(t.error,"failed to get inflation rate");return t.result}async getEpochInfo(e){let{commitment:t,config:n}=Ge(e),i=this._buildArgs([],t,void 0,n),o=await this._rpcRequest("getEpochInfo",i),s=c.create(o,US);if("error"in s)throw new re(s.error,"failed to get epoch info");return s.result}async getEpochSchedule(){let e=await this._rpcRequest("getEpochSchedule",[]),t=c.create(e,PS);if("error"in t)throw new re(t.error,"failed to get epoch schedule");let n=t.result;return new ns(n.slotsPerEpoch,n.leaderScheduleSlotOffset,n.warmup,n.firstNormalEpoch,n.firstNormalSlot)}async getLeaderSchedule(){let e=await this._rpcRequest("getLeaderSchedule",[]),t=c.create(e,FS);if("error"in t)throw new re(t.error,"failed to get leader schedule");return t.result}async getMinimumBalanceForRentExemption(e,t){let n=this._buildArgs([e],t),i=await this._rpcRequest("getMinimumBalanceForRentExemption",n),o=c.create(i,d1);return"error"in o?(console.warn("Unable to fetch minimum balance for rent exemption"),0):o.result}async getRecentBlockhashAndContext(e){let{context:t,value:{blockhash:n}}=await this.getLatestBlockhashAndContext(e);return{context:t,value:{blockhash:n,feeCalculator:{get lamportsPerSignature(){throw new Error("The capability to fetch `lamportsPerSignature` using the `getRecentBlockhash` API is no longer offered by the network. Use the `getFeeForMessage` API to obtain the fee for a given message.")},toJSON(){return{}}}}}}async getRecentPerformanceSamples(e){let t=await this._rpcRequest("getRecentPerformanceSamples",e?[e]:[]),n=c.create(t,w1);if("error"in n)throw new re(n.error,"failed to get recent performance samples");return n.result}async getFeeCalculatorForBlockhash(e,t){let n=this._buildArgs([e],t),i=await this._rpcRequest("getFeeCalculatorForBlockhash",n),o=c.create(i,I1);if("error"in o)throw new re(o.error,"failed to get fee calculator");let{context:s,value:u}=o.result;return{context:s,value:u!==null?u.feeCalculator:null}}async getFeeForMessage(e,t){let n=Ae(e.serialize()).toString("base64"),i=this._buildArgs([n],t),o=await this._rpcRequest("getFeeForMessage",i),s=c.create(o,nt(c.nullable(c.number())));if("error"in s)throw new re(s.error,"failed to get fee for message");if(s.result===null)throw new Error("invalid blockhash");return s.result}async getRecentPrioritizationFees(e){let t=e?.lockedWritableAccounts?.map(s=>s.toBase58()),n=t?.length?[t]:[],i=await this._rpcRequest("getRecentPrioritizationFees",n),o=c.create(i,MS);if("error"in o)throw new re(o.error,"failed to get recent prioritization fees");return o.result}async getRecentBlockhash(e){try{return(await this.getRecentBlockhashAndContext(e)).value}catch(t){throw new Error("failed to get recent blockhash: "+t)}}async getLatestBlockhash(e){try{return(await this.getLatestBlockhashAndContext(e)).value}catch(t){throw new Error("failed to get recent blockhash: "+t)}}async getLatestBlockhashAndContext(e){let{commitment:t,config:n}=Ge(e),i=this._buildArgs([],t,void 0,n),o=await this._rpcRequest("getLatestBlockhash",i),s=c.create(o,S1);if("error"in s)throw new re(s.error,"failed to get latest blockhash");return s.result}async isBlockhashValid(e,t){let{commitment:n,config:i}=Ge(t),o=this._buildArgs([e],n,void 0,i),s=await this._rpcRequest("isBlockhashValid",o),u=c.create(s,b1);if("error"in u)throw new re(u.error,"failed to determine if the blockhash `"+e+"`is valid");return u.result}async getVersion(){let e=await this._rpcRequest("getVersion",[]),t=c.create(e,ye(NS));if("error"in t)throw new re(t.error,"failed to get version");return t.result}async getGenesisHash(){let e=await this._rpcRequest("getGenesisHash",[]),t=c.create(e,ye(c.string()));if("error"in t)throw new re(t.error,"failed to get genesis hash");return t.result}async getBlock(e,t){let{commitment:n,config:i}=Ge(t),o=this._buildArgsAtLeastConfirmed([e],n,void 0,i),s=await this._rpcRequest("getBlock",o);try{switch(i?.transactionDetails){case"accounts":{let u=c.create(s,E1);if("error"in u)throw u.error;return u.result}case"none":{let u=c.create(s,R1);if("error"in u)throw u.error;return u.result}default:{let u=c.create(s,p1);if("error"in u)throw u.error;let{result:l}=u;return l?{...l,transactions:l.transactions.map(({transaction:p,meta:R,version:g})=>({meta:R,transaction:{...p,message:Bc(g,p.message)},version:g}))}:null}}}catch(u){throw new re(u,"failed to get confirmed block")}}async getParsedBlock(e,t){let{commitment:n,config:i}=Ge(t),o=this._buildArgsAtLeastConfirmed([e],n,"jsonParsed",i),s=await this._rpcRequest("getBlock",o);try{switch(i?.transactionDetails){case"accounts":{let u=c.create(s,y1);if("error"in u)throw u.error;return u.result}case"none":{let u=c.create(s,A1);if("error"in u)throw u.error;return u.result}default:{let u=c.create(s,g1);if("error"in u)throw u.error;return u.result}}}catch(u){throw new re(u,"failed to get block")}}async getBlockProduction(e){let t,n;if(typeof e=="string")n=e;else if(e){let{commitment:u,...l}=e;n=u,t=l}let i=this._buildArgs([],n,"base64",t),o=await this._rpcRequest("getBlockProduction",i),s=c.create(o,CS);if("error"in s)throw new re(s.error,"failed to get block production information");return s.result}async getTransaction(e,t){let{commitment:n,config:i}=Ge(t),o=this._buildArgsAtLeastConfirmed([e],n,void 0,i),s=await this._rpcRequest("getTransaction",o),u=c.create(s,Dc);if("error"in u)throw new re(u.error,"failed to get transaction");let l=u.result;return l&&{...l,transaction:{...l.transaction,message:Bc(l.version,l.transaction.message)}}}async getParsedTransaction(e,t){let{commitment:n,config:i}=Ge(t),o=this._buildArgsAtLeastConfirmed([e],n,"jsonParsed",i),s=await this._rpcRequest("getTransaction",o),u=c.create(s,Zo);if("error"in u)throw new re(u.error,"failed to get transaction");return u.result}async getParsedTransactions(e,t){let{commitment:n,config:i}=Ge(t),o=e.map(l=>({methodName:"getTransaction",args:this._buildArgsAtLeastConfirmed([l],n,"jsonParsed",i)}));return(await this._rpcBatchRequest(o)).map(l=>{let p=c.create(l,Zo);if("error"in p)throw new re(p.error,"failed to get transactions");return p.result})}async getTransactions(e,t){let{commitment:n,config:i}=Ge(t),o=e.map(l=>({methodName:"getTransaction",args:this._buildArgsAtLeastConfirmed([l],n,void 0,i)}));return(await this._rpcBatchRequest(o)).map(l=>{let p=c.create(l,Dc);if("error"in p)throw new re(p.error,"failed to get transactions");let R=p.result;return R&&{...R,transaction:{...R.transaction,message:Bc(R.version,R.transaction.message)}}})}async getConfirmedBlock(e,t){let n=this._buildArgsAtLeastConfirmed([e],t),i=await this._rpcRequest("getBlock",n),o=c.create(i,m1);if("error"in o)throw new re(o.error,"failed to get confirmed block");let s=o.result;if(!s)throw new Error("Confirmed block "+e+" not found");let u={...s,transactions:s.transactions.map(({transaction:l,meta:p})=>{let R=new Or(l.message);return{meta:p,transaction:{...l,message:R}}})};return{...u,transactions:u.transactions.map(({transaction:l,meta:p})=>({meta:p,transaction:De.populate(l.message,l.signatures)}))}}async getBlocks(e,t,n){let i=this._buildArgsAtLeastConfirmed(t!==void 0?[e,t]:[e],n),o=await this._rpcRequest("getBlocks",i),s=c.create(o,ye(c.array(c.number())));if("error"in s)throw new re(s.error,"failed to get blocks");return s.result}async getBlockSignatures(e,t){let n=this._buildArgsAtLeastConfirmed([e],t,void 0,{transactionDetails:"signatures",rewards:!1}),i=await this._rpcRequest("getBlock",n),o=c.create(i,NR);if("error"in o)throw new re(o.error,"failed to get block");let s=o.result;if(!s)throw new Error("Block "+e+" not found");return s}async getConfirmedBlockSignatures(e,t){let n=this._buildArgsAtLeastConfirmed([e],t,void 0,{transactionDetails:"signatures",rewards:!1}),i=await this._rpcRequest("getBlock",n),o=c.create(i,NR);if("error"in o)throw new re(o.error,"failed to get confirmed block");let s=o.result;if(!s)throw new Error("Confirmed block "+e+" not found");return s}async getConfirmedTransaction(e,t){let n=this._buildArgsAtLeastConfirmed([e],t),i=await this._rpcRequest("getTransaction",n),o=c.create(i,Dc);if("error"in o)throw new re(o.error,"failed to get transaction");let s=o.result;if(!s)return s;let u=new Or(s.transaction.message),l=s.transaction.signatures;return{...s,transaction:De.populate(u,l)}}async getParsedConfirmedTransaction(e,t){let n=this._buildArgsAtLeastConfirmed([e],t,"jsonParsed"),i=await this._rpcRequest("getTransaction",n),o=c.create(i,Zo);if("error"in o)throw new re(o.error,"failed to get confirmed transaction");return o.result}async getParsedConfirmedTransactions(e,t){let n=e.map(s=>({methodName:"getTransaction",args:this._buildArgsAtLeastConfirmed([s],t,"jsonParsed")}));return(await this._rpcBatchRequest(n)).map(s=>{let u=c.create(s,Zo);if("error"in u)throw new re(u.error,"failed to get confirmed transactions");return u.result})}async getConfirmedSignaturesForAddress(e,t,n){let i={},o=await this.getFirstAvailableBlock();for(;!("until"in i)&&(t--,!(t<=0||t<o));)try{let l=await this.getConfirmedBlockSignatures(t,"finalized");l.signatures.length>0&&(i.until=l.signatures[l.signatures.length-1].toString())}catch(l){if(l instanceof Error&&l.message.includes("skipped"))continue;throw l}let s=await this.getSlot("finalized");for(;!("before"in i)&&(n++,!(n>s));)try{let l=await this.getConfirmedBlockSignatures(n);l.signatures.length>0&&(i.before=l.signatures[l.signatures.length-1].toString())}catch(l){if(l instanceof Error&&l.message.includes("skipped"))continue;throw l}return(await this.getConfirmedSignaturesForAddress2(e,i)).map(l=>l.signature)}async getConfirmedSignaturesForAddress2(e,t,n){let i=this._buildArgsAtLeastConfirmed([e.toBase58()],n,void 0,t),o=await this._rpcRequest("getConfirmedSignaturesForAddress2",i),s=c.create(o,XS);if("error"in s)throw new re(s.error,"failed to get confirmed signatures for address");return s.result}async getSignaturesForAddress(e,t,n){let i=this._buildArgsAtLeastConfirmed([e.toBase58()],n,void 0,t),o=await this._rpcRequest("getSignaturesForAddress",i),s=c.create(o,ZS);if("error"in s)throw new re(s.error,"failed to get signatures for address");return s.result}async getAddressLookupTable(e,t){let{context:n,value:i}=await this.getAccountInfoAndContext(e,t),o=null;return i!==null&&(o=new Ni({key:e,state:Ni.deserialize(i.data)})),{context:n,value:o}}async getNonceAndContext(e,t){let{context:n,value:i}=await this.getAccountInfoAndContext(e,t),o=null;return i!==null&&(o=rs.fromAccountData(i.data)),{context:n,value:o}}async getNonce(e,t){return await this.getNonceAndContext(e,t).then(n=>n.value).catch(n=>{throw new Error("failed to get nonce for account "+e.toBase58()+": "+n)})}async requestAirdrop(e,t){let n=await this._rpcRequest("requestAirdrop",[e.toBase58(),t]),i=c.create(n,N1);if("error"in i)throw new re(i.error,`airdrop to ${e.toBase58()} failed`);return i.result}async _blockhashWithExpiryBlockHeight(e){if(!e){for(;this._pollingBlockhash;)await Rn(100);let n=Date.now()-this._blockhashInfo.lastFetch>=WR;if(this._blockhashInfo.latestBlockhash!==null&&!n)return this._blockhashInfo.latestBlockhash}return await this._pollNewBlockhash()}async _pollNewBlockhash(){this._pollingBlockhash=!0;try{let e=Date.now(),t=this._blockhashInfo.latestBlockhash,n=t?t.blockhash:null;for(let i=0;i<50;i++){let o=await this.getLatestBlockhash("finalized");if(n!==o.blockhash)return this._blockhashInfo={latestBlockhash:o,lastFetch:Date.now(),transactionSignatures:[],simulatedSignatures:[]},o;await Rn(GR/2)}throw new Error(`Unable to obtain a new blockhash after ${Date.now()-e}ms`)}finally{this._pollingBlockhash=!1}}async getStakeMinimumDelegation(e){let{commitment:t,config:n}=Ge(e),i=this._buildArgs([],t,"base64",n),o=await this._rpcRequest("getStakeMinimumDelegation",i),s=c.create(o,nt(c.number()));if("error"in s)throw new re(s.error,"failed to get stake minimum delegation");return s.result}async simulateTransaction(e,t,n){if("message"in e){let I=e.serialize(),O=se.Buffer.from(I).toString("base64");if(Array.isArray(t)||n!==void 0)throw new Error("Invalid arguments");let D=t||{};D.encoding="base64","commitment"in D||(D.commitment=this.commitment),t&&typeof t=="object"&&"innerInstructions"in t&&(D.innerInstructions=t.innerInstructions);let k=[O,D],B=await this._rpcRequest("simulateTransaction",k),F=c.create(B,wR);if("error"in F)throw new Error("failed to simulate transaction: "+F.error.message);return F.result}let i;if(e instanceof De){let x=e;i=new De,i.feePayer=x.feePayer,i.instructions=e.instructions,i.nonceInfo=x.nonceInfo,i.signatures=x.signatures}else i=De.populate(e),i._message=i._json=void 0;if(t!==void 0&&!Array.isArray(t))throw new Error("Invalid arguments");let o=t;if(i.nonceInfo&&o)i.sign(...o);else{let x=this._disableBlockhashCaching;for(;;){let I=await this._blockhashWithExpiryBlockHeight(x);if(i.lastValidBlockHeight=I.lastValidBlockHeight,i.recentBlockhash=I.blockhash,!o)break;if(i.sign(...o),!i.signature)throw new Error("!signature");let O=i.signature.toString("base64");if(!this._blockhashInfo.simulatedSignatures.includes(O)&&!this._blockhashInfo.transactionSignatures.includes(O)){this._blockhashInfo.simulatedSignatures.push(O);break}else x=!0}}let s=i._compile(),u=s.serialize(),p=i._serialize(u).toString("base64"),R={encoding:"base64",commitment:this.commitment};if(n){let x=(Array.isArray(n)?n:s.nonProgramIds()).map(I=>I.toBase58());R.accounts={encoding:"base64",addresses:x}}o&&(R.sigVerify=!0),t&&typeof t=="object"&&"innerInstructions"in t&&(R.innerInstructions=t.innerInstructions);let g=[p,R],v=await this._rpcRequest("simulateTransaction",g),m=c.create(v,wR);if("error"in m){let x;if("data"in m.error&&(x=m.error.data.logs,x&&Array.isArray(x))){let I=`
    `,O=I+x.join(I);console.error(m.error.message,O)}throw new yn({action:"simulate",signature:"",transactionMessage:m.error.message,logs:x})}return m.result}async sendTransaction(e,t,n){if("version"in e){if(t&&Array.isArray(t))throw new Error("Invalid arguments");let s=e.serialize();return await this.sendRawTransaction(s,t)}if(t===void 0||!Array.isArray(t))throw new Error("Invalid arguments");let i=t;if(e.nonceInfo)e.sign(...i);else{let s=this._disableBlockhashCaching;for(;;){let u=await this._blockhashWithExpiryBlockHeight(s);if(e.lastValidBlockHeight=u.lastValidBlockHeight,e.recentBlockhash=u.blockhash,e.sign(...i),!e.signature)throw new Error("!signature");let l=e.signature.toString("base64");if(this._blockhashInfo.transactionSignatures.includes(l))s=!0;else{this._blockhashInfo.transactionSignatures.push(l);break}}}let o=e.serialize();return await this.sendRawTransaction(o,n)}async sendRawTransaction(e,t){let n=Ae(e).toString("base64");return await this.sendEncodedTransaction(n,t)}async sendEncodedTransaction(e,t){let n={encoding:"base64"},i=t&&t.skipPreflight,o=i===!0?"processed":t&&t.preflightCommitment||this.commitment;t&&t.maxRetries!=null&&(n.maxRetries=t.maxRetries),t&&t.minContextSlot!=null&&(n.minContextSlot=t.minContextSlot),i&&(n.skipPreflight=i),o&&(n.preflightCommitment=o);let s=[e,n],u=await this._rpcRequest("sendTransaction",s),l=c.create(u,T1);if("error"in l){let p;throw"data"in l.error&&(p=l.error.data.logs),new yn({action:i?"send":"simulate",signature:"",transactionMessage:l.error.message,logs:p})}return l.result}_wsOnOpen(){this._rpcWebSocketConnected=!0,this._rpcWebSocketHeartbeat=setInterval(()=>{(async()=>{try{await this._rpcWebSocket.notify("ping")}catch{}})()},5e3),this._updateSubscriptions()}_wsOnError(e){this._rpcWebSocketConnected=!1,console.error("ws error:",e.message)}_wsOnClose(e){if(this._rpcWebSocketConnected=!1,this._rpcWebSocketGeneration=(this._rpcWebSocketGeneration+1)%Number.MAX_SAFE_INTEGER,this._rpcWebSocketIdleTimeout&&(clearTimeout(this._rpcWebSocketIdleTimeout),this._rpcWebSocketIdleTimeout=null),this._rpcWebSocketHeartbeat&&(clearInterval(this._rpcWebSocketHeartbeat),this._rpcWebSocketHeartbeat=null),e===1e3){this._updateSubscriptions();return}this._subscriptionCallbacksByServerSubscriptionId={},Object.entries(this._subscriptionsByHash).forEach(([t,n])=>{this._setSubscription(t,{...n,state:"pending"})})}_setSubscription(e,t){let n=this._subscriptionsByHash[e]?.state;if(this._subscriptionsByHash[e]=t,n!==t.state){let i=this._subscriptionStateChangeCallbacksByHash[e];i&&i.forEach(o=>{try{o(t.state)}catch{}})}}_onSubscriptionStateChange(e,t){let n=this._subscriptionHashByClientSubscriptionId[e];if(n==null)return()=>{};let i=this._subscriptionStateChangeCallbacksByHash[n]||=new Set;return i.add(t),()=>{i.delete(t),i.size===0&&delete this._subscriptionStateChangeCallbacksByHash[n]}}async _updateSubscriptions(){if(Object.keys(this._subscriptionsByHash).length===0){this._rpcWebSocketConnected&&(this._rpcWebSocketConnected=!1,this._rpcWebSocketIdleTimeout=setTimeout(()=>{this._rpcWebSocketIdleTimeout=null;try{this._rpcWebSocket.close()}catch(n){n instanceof Error&&console.log(`Error when closing socket connection: ${n.message}`)}},500));return}if(this._rpcWebSocketIdleTimeout!==null&&(clearTimeout(this._rpcWebSocketIdleTimeout),this._rpcWebSocketIdleTimeout=null,this._rpcWebSocketConnected=!0),!this._rpcWebSocketConnected){this._rpcWebSocket.connect();return}let e=this._rpcWebSocketGeneration,t=()=>e===this._rpcWebSocketGeneration;await Promise.all(Object.keys(this._subscriptionsByHash).map(async n=>{let i=this._subscriptionsByHash[n];if(i!==void 0)switch(i.state){case"pending":case"unsubscribed":if(i.callbacks.size===0){delete this._subscriptionsByHash[n],i.state==="unsubscribed"&&delete this._subscriptionCallbacksByServerSubscriptionId[i.serverSubscriptionId],await this._updateSubscriptions();return}await(async()=>{let{args:o,method:s}=i;try{this._setSubscription(n,{...i,state:"subscribing"});let u=await this._rpcWebSocket.call(s,o);this._setSubscription(n,{...i,serverSubscriptionId:u,state:"subscribed"}),this._subscriptionCallbacksByServerSubscriptionId[u]=i.callbacks,await this._updateSubscriptions()}catch(u){if(console.error(`Received ${u instanceof Error?"":"JSON-RPC "}error calling \`${s}\``,{args:o,error:u}),!t())return;this._setSubscription(n,{...i,state:"pending"}),await this._updateSubscriptions()}})();break;case"subscribed":i.callbacks.size===0&&await(async()=>{let{serverSubscriptionId:o,unsubscribeMethod:s}=i;if(this._subscriptionsAutoDisposedByRpc.has(o))this._subscriptionsAutoDisposedByRpc.delete(o);else{this._setSubscription(n,{...i,state:"unsubscribing"}),this._setSubscription(n,{...i,state:"unsubscribing"});try{await this._rpcWebSocket.call(s,[o])}catch(u){if(u instanceof Error&&console.error(`${s} error:`,u.message),!t())return;this._setSubscription(n,{...i,state:"subscribed"}),await this._updateSubscriptions();return}}this._setSubscription(n,{...i,state:"unsubscribed"}),await this._updateSubscriptions()})();break}}))}_handleServerNotification(e,t){let n=this._subscriptionCallbacksByServerSubscriptionId[e];n!==void 0&&n.forEach(i=>{try{i(...t)}catch(o){console.error(o)}})}_wsOnAccountNotification(e){let{result:t,subscription:n}=c.create(e,JS);this._handleServerNotification(n,[t.value,t.context])}_makeSubscription(e,t){let n=this._nextClientSubscriptionId++,i=SR([e.method,t]),o=this._subscriptionsByHash[i];return o===void 0?this._subscriptionsByHash[i]={...e,args:t,callbacks:new Set([e.callback]),state:"pending"}:o.callbacks.add(e.callback),this._subscriptionHashByClientSubscriptionId[n]=i,this._subscriptionDisposeFunctionsByClientSubscriptionId[n]=async()=>{delete this._subscriptionDisposeFunctionsByClientSubscriptionId[n],delete this._subscriptionHashByClientSubscriptionId[n];let s=this._subscriptionsByHash[i];Te(s!==void 0,`Could not find a \`Subscription\` when tearing down client subscription #${n}`),s.callbacks.delete(e.callback),await this._updateSubscriptions()},this._updateSubscriptions(),n}onAccountChange(e,t,n){let{commitment:i,config:o}=Ge(n),s=this._buildArgs([e.toBase58()],i||this._commitment||"finalized","base64",o);return this._makeSubscription({callback:t,method:"accountSubscribe",unsubscribeMethod:"accountUnsubscribe"},s)}async removeAccountChangeListener(e){await this._unsubscribeClientSubscription(e,"account change")}_wsOnProgramAccountNotification(e){let{result:t,subscription:n}=c.create(e,e1);this._handleServerNotification(n,[{accountId:t.value.pubkey,accountInfo:t.value.account},t.context])}onProgramAccountChange(e,t,n,i){let{commitment:o,config:s}=Ge(n),u=this._buildArgs([e.toBase58()],o||this._commitment||"finalized","base64",s||(i?{filters:OR(i)}:void 0));return this._makeSubscription({callback:t,method:"programSubscribe",unsubscribeMethod:"programUnsubscribe"},u)}async removeProgramAccountChangeListener(e){await this._unsubscribeClientSubscription(e,"program account change")}onLogs(e,t,n){let i=this._buildArgs([typeof e=="object"?{mentions:[e.toString()]}:e],n||this._commitment||"finalized");return this._makeSubscription({callback:t,method:"logsSubscribe",unsubscribeMethod:"logsUnsubscribe"},i)}async removeOnLogsListener(e){await this._unsubscribeClientSubscription(e,"logs")}_wsOnLogsNotification(e){let{result:t,subscription:n}=c.create(e,C1);this._handleServerNotification(n,[t.value,t.context])}_wsOnSlotNotification(e){let{result:t,subscription:n}=c.create(e,r1);this._handleServerNotification(n,[t])}onSlotChange(e){return this._makeSubscription({callback:e,method:"slotSubscribe",unsubscribeMethod:"slotUnsubscribe"},[])}async removeSlotChangeListener(e){await this._unsubscribeClientSubscription(e,"slot change")}_wsOnSlotUpdatesNotification(e){let{result:t,subscription:n}=c.create(e,i1);this._handleServerNotification(n,[t])}onSlotUpdate(e){return this._makeSubscription({callback:e,method:"slotsUpdatesSubscribe",unsubscribeMethod:"slotsUpdatesUnsubscribe"},[])}async removeSlotUpdateListener(e){await this._unsubscribeClientSubscription(e,"slot update")}async _unsubscribeClientSubscription(e,t){let n=this._subscriptionDisposeFunctionsByClientSubscriptionId[e];n?await n():console.warn(`Ignored unsubscribe request because an active subscription with id \`${e}\` for '${t}' events could not be found.`)}_buildArgs(e,t,n,i){let o=t||this._commitment;if(o||n||i){let s={};n&&(s.encoding=n),o&&(s.commitment=o),i&&(s=Object.assign(s,i)),e.push(s)}return e}_buildArgsAtLeastConfirmed(e,t,n,i){let o=t||this._commitment;if(o&&!["confirmed","finalized"].includes(o))throw new Error("Using Connection with default commitment: `"+this._commitment+"`, but method requires at least `confirmed`");return this._buildArgs(e,t,n,i)}_wsOnSignatureNotification(e){let{result:t,subscription:n}=c.create(e,o1);t.value!=="receivedSignature"&&this._subscriptionsAutoDisposedByRpc.add(n),this._handleServerNotification(n,t.value==="receivedSignature"?[{type:"received"},t.context]:[{type:"status",result:t.value},t.context])}onSignature(e,t,n){let i=this._buildArgs([e],n||this._commitment||"finalized"),o=this._makeSubscription({callback:(s,u)=>{if(s.type==="status"){t(s.result,u);try{this.removeSignatureListener(o)}catch{}}},method:"signatureSubscribe",unsubscribeMethod:"signatureUnsubscribe"},i);return o}onSignatureWithOptions(e,t,n){let{commitment:i,...o}={...n,commitment:n&&n.commitment||this._commitment||"finalized"},s=this._buildArgs([e],i,void 0,o),u=this._makeSubscription({callback:(l,p)=>{t(l,p);try{this.removeSignatureListener(u)}catch{}},method:"signatureSubscribe",unsubscribeMethod:"signatureUnsubscribe"},s);return u}async removeSignatureListener(e){await this._unsubscribeClientSubscription(e,"signature result")}_wsOnRootNotification(e){let{result:t,subscription:n}=c.create(e,s1);this._handleServerNotification(n,[t])}onRootChange(e){return this._makeSubscription({callback:e,method:"rootSubscribe",unsubscribeMethod:"rootUnsubscribe"},[])}async removeRootChangeListener(e){await this._unsubscribeClientSubscription(e,"root change")}},os=class r{constructor(e){this._keypair=void 0,this._keypair=e??gR()}static generate(){return new r(gR())}static fromSecretKey(e,t){if(e.byteLength!==64)throw new Error("bad secret key size");let n=e.slice(32,64);if(!t||!t.skipValidation){let i=e.slice(0,32),o=es(i);for(let s=0;s<32;s++)if(n[s]!==o[s])throw new Error("provided secretKey is invalid")}return new r({publicKey:n,secretKey:e})}static fromSeed(e){let t=es(e),n=new Uint8Array(64);return n.set(e),n.set(t,32),new r({publicKey:t,secretKey:n})}get publicKey(){return new X(this._keypair.publicKey)}get secretKey(){return new Uint8Array(this._keypair.secretKey)}},Br=Object.freeze({CreateLookupTable:{index:0,layout:w.struct([w.u32("instruction"),zn("recentSlot"),w.u8("bumpSeed")])},FreezeLookupTable:{index:1,layout:w.struct([w.u32("instruction")])},ExtendLookupTable:{index:2,layout:w.struct([w.u32("instruction"),zn(),w.seq(Se(),w.offset(w.u32(),-8),"addresses")])},DeactivateLookupTable:{index:3,layout:w.struct([w.u32("instruction")])},CloseLookupTable:{index:4,layout:w.struct([w.u32("instruction")])}}),Zc=class{constructor(){}static decodeInstructionType(e){this.checkProgramId(e.programId);let n=w.u32("instruction").decode(e.data),i;for(let[o,s]of Object.entries(Br))if(s.index==n){i=o;break}if(!i)throw new Error("Invalid Instruction. Should be a LookupTable Instruction");return i}static decodeCreateLookupTable(e){this.checkProgramId(e.programId),this.checkKeysLength(e.keys,4);let{recentSlot:t}=Be(Br.CreateLookupTable,e.data);return{authority:e.keys[1].pubkey,payer:e.keys[2].pubkey,recentSlot:Number(t)}}static decodeExtendLookupTable(e){if(this.checkProgramId(e.programId),e.keys.length<2)throw new Error(`invalid instruction; found ${e.keys.length} keys, expected at least 2`);let{addresses:t}=Be(Br.ExtendLookupTable,e.data);return{lookupTable:e.keys[0].pubkey,authority:e.keys[1].pubkey,payer:e.keys.length>2?e.keys[2].pubkey:void 0,addresses:t.map(n=>new X(n))}}static decodeCloseLookupTable(e){return this.checkProgramId(e.programId),this.checkKeysLength(e.keys,3),{lookupTable:e.keys[0].pubkey,authority:e.keys[1].pubkey,recipient:e.keys[2].pubkey}}static decodeFreezeLookupTable(e){return this.checkProgramId(e.programId),this.checkKeysLength(e.keys,2),{lookupTable:e.keys[0].pubkey,authority:e.keys[1].pubkey}}static decodeDeactivateLookupTable(e){return this.checkProgramId(e.programId),this.checkKeysLength(e.keys,2),{lookupTable:e.keys[0].pubkey,authority:e.keys[1].pubkey}}static checkProgramId(e){if(!e.equals(vi.programId))throw new Error("invalid instruction; programId is not AddressLookupTable Program")}static checkKeysLength(e,t){if(e.length<t)throw new Error(`invalid instruction; found ${e.length} keys, expected at least ${t}`)}},vi=class{constructor(){}static createLookupTable(e){let[t,n]=X.findProgramAddressSync([e.authority.toBuffer(),UR.getU64Encoder().encode(e.recentSlot)],this.programId),i=Br.CreateLookupTable,o=Oe(i,{recentSlot:BigInt(e.recentSlot),bumpSeed:n}),s=[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:e.authority,isSigner:!0,isWritable:!1},{pubkey:e.payer,isSigner:!0,isWritable:!0},{pubkey:xt.programId,isSigner:!1,isWritable:!1}];return[new Me({programId:this.programId,keys:s,data:o}),t]}static freezeLookupTable(e){let t=Br.FreezeLookupTable,n=Oe(t),i=[{pubkey:e.lookupTable,isSigner:!1,isWritable:!0},{pubkey:e.authority,isSigner:!0,isWritable:!1}];return new Me({programId:this.programId,keys:i,data:n})}static extendLookupTable(e){let t=Br.ExtendLookupTable,n=Oe(t,{addresses:e.addresses.map(o=>o.toBytes())}),i=[{pubkey:e.lookupTable,isSigner:!1,isWritable:!0},{pubkey:e.authority,isSigner:!0,isWritable:!1}];return e.payer&&i.push({pubkey:e.payer,isSigner:!0,isWritable:!0},{pubkey:xt.programId,isSigner:!1,isWritable:!1}),new Me({programId:this.programId,keys:i,data:n})}static deactivateLookupTable(e){let t=Br.DeactivateLookupTable,n=Oe(t),i=[{pubkey:e.lookupTable,isSigner:!1,isWritable:!0},{pubkey:e.authority,isSigner:!0,isWritable:!1}];return new Me({programId:this.programId,keys:i,data:n})}static closeLookupTable(e){let t=Br.CloseLookupTable,n=Oe(t),i=[{pubkey:e.lookupTable,isSigner:!1,isWritable:!0},{pubkey:e.authority,isSigner:!0,isWritable:!1},{pubkey:e.recipient,isSigner:!1,isWritable:!0}];return new Me({programId:this.programId,keys:i,data:n})}};vi.programId=new X("AddressLookupTab1e1111111111111111111111111");var Jc=class{constructor(){}static decodeInstructionType(e){this.checkProgramId(e.programId);let n=w.u8("instruction").decode(e.data),i;for(let[o,s]of Object.entries(mr))if(s.index==n){i=o;break}if(!i)throw new Error("Instruction type incorrect; not a ComputeBudgetInstruction");return i}static decodeRequestUnits(e){this.checkProgramId(e.programId);let{units:t,additionalFee:n}=Be(mr.RequestUnits,e.data);return{units:t,additionalFee:n}}static decodeRequestHeapFrame(e){this.checkProgramId(e.programId);let{bytes:t}=Be(mr.RequestHeapFrame,e.data);return{bytes:t}}static decodeSetComputeUnitLimit(e){this.checkProgramId(e.programId);let{units:t}=Be(mr.SetComputeUnitLimit,e.data);return{units:t}}static decodeSetComputeUnitPrice(e){this.checkProgramId(e.programId);let{microLamports:t}=Be(mr.SetComputeUnitPrice,e.data);return{microLamports:t}}static checkProgramId(e){if(!e.equals(Ci.programId))throw new Error("invalid instruction; programId is not ComputeBudgetProgram")}},mr=Object.freeze({RequestUnits:{index:0,layout:w.struct([w.u8("instruction"),w.u32("units"),w.u32("additionalFee")])},RequestHeapFrame:{index:1,layout:w.struct([w.u8("instruction"),w.u32("bytes")])},SetComputeUnitLimit:{index:2,layout:w.struct([w.u8("instruction"),w.u32("units")])},SetComputeUnitPrice:{index:3,layout:w.struct([w.u8("instruction"),zn("microLamports")])}}),Ci=class{constructor(){}static requestUnits(e){let t=mr.RequestUnits,n=Oe(t,e);return new Me({keys:[],programId:this.programId,data:n})}static requestHeapFrame(e){let t=mr.RequestHeapFrame,n=Oe(t,e);return new Me({keys:[],programId:this.programId,data:n})}static setComputeUnitLimit(e){let t=mr.SetComputeUnitLimit,n=Oe(t,e);return new Me({keys:[],programId:this.programId,data:n})}static setComputeUnitPrice(e){let t=mr.SetComputeUnitPrice,n=Oe(t,{microLamports:BigInt(e.microLamports)});return new Me({keys:[],programId:this.programId,data:n})}};Ci.programId=new X("ComputeBudget111111111111111111111111111111");var TR=64,vR=32,CR=64,xR=w.struct([w.u8("numSignatures"),w.u8("padding"),w.u16("signatureOffset"),w.u16("signatureInstructionIndex"),w.u16("publicKeyOffset"),w.u16("publicKeyInstructionIndex"),w.u16("messageDataOffset"),w.u16("messageDataSize"),w.u16("messageInstructionIndex")]),ss=class r{constructor(){}static createInstructionWithPublicKey(e){let{publicKey:t,message:n,signature:i,instructionIndex:o}=e;Te(t.length===vR,`Public Key must be ${vR} bytes but received ${t.length} bytes`),Te(i.length===CR,`Signature must be ${CR} bytes but received ${i.length} bytes`);let s=xR.span,u=s+t.length,l=u+i.length,p=1,R=se.Buffer.alloc(l+n.length),g=o??65535;return xR.encode({numSignatures:p,padding:0,signatureOffset:u,signatureInstructionIndex:g,publicKeyOffset:s,publicKeyInstructionIndex:g,messageDataOffset:l,messageDataSize:n.length,messageInstructionIndex:g},R),R.fill(t,s),R.fill(i,u),R.fill(n,l),new Me({keys:[],programId:r.programId,data:R})}static createInstructionWithPrivateKey(e){let{privateKey:t,message:n,instructionIndex:i}=e;Te(t.length===TR,`Private key must be ${TR} bytes but received ${t.length} bytes`);try{let o=os.fromSecretKey(t),s=o.publicKey.toBytes(),u=ou(n,o.secretKey);return this.createInstructionWithPublicKey({publicKey:s,message:n,signature:u,instructionIndex:i})}catch(o){throw new Error(`Error creating instruction; ${o}`)}}};ss.programId=new X("Ed25519SigVerify111111111111111111111111111");var L1=(r,e)=>{let t=nu.secp256k1.sign(r,e);return[t.toCompactRawBytes(),t.recovery]};nu.secp256k1.utils.isValidPrivateKey;var k1=nu.secp256k1.getPublicKey,LR=32,Mc=20,kR=64,B1=11,Uc=w.struct([w.u8("numSignatures"),w.u16("signatureOffset"),w.u8("signatureInstructionIndex"),w.u16("ethAddressOffset"),w.u8("ethAddressInstructionIndex"),w.u16("messageDataOffset"),w.u16("messageDataSize"),w.u8("messageInstructionIndex"),w.blob(20,"ethAddress"),w.blob(64,"signature"),w.u8("recoveryId")]),as=class r{constructor(){}static publicKeyToEthAddress(e){Te(e.length===kR,`Public key must be ${kR} bytes but received ${e.length} bytes`);try{return se.Buffer.from(RR.keccak_256(Ae(e))).slice(-Mc)}catch(t){throw new Error(`Error constructing Ethereum address: ${t}`)}}static createInstructionWithPublicKey(e){let{publicKey:t,message:n,signature:i,recoveryId:o,instructionIndex:s}=e;return r.createInstructionWithEthAddress({ethAddress:r.publicKeyToEthAddress(t),message:n,signature:i,recoveryId:o,instructionIndex:s})}static createInstructionWithEthAddress(e){let{ethAddress:t,message:n,signature:i,recoveryId:o,instructionIndex:s=0}=e,u;typeof t=="string"?t.startsWith("0x")?u=se.Buffer.from(t.substr(2),"hex"):u=se.Buffer.from(t,"hex"):u=t,Te(u.length===Mc,`Address must be ${Mc} bytes but received ${u.length} bytes`);let l=1+B1,p=l,R=l+u.length,g=R+i.length+1,v=1,m=se.Buffer.alloc(Uc.span+n.length);return Uc.encode({numSignatures:v,signatureOffset:R,signatureInstructionIndex:s,ethAddressOffset:p,ethAddressInstructionIndex:s,messageDataOffset:g,messageDataSize:n.length,messageInstructionIndex:s,signature:Ae(i),ethAddress:Ae(u),recoveryId:o},m),m.fill(Ae(n),Uc.span),new Me({keys:[],programId:r.programId,data:m})}static createInstructionWithPrivateKey(e){let{privateKey:t,message:n,instructionIndex:i}=e;Te(t.length===LR,`Private key must be ${LR} bytes but received ${t.length} bytes`);try{let o=Ae(t),s=k1(o,!1).slice(1),u=se.Buffer.from(RR.keccak_256(Ae(n))),[l,p]=L1(u,o);return this.createInstructionWithPublicKey({publicKey:s,message:n,signature:l,recoveryId:p,instructionIndex:i})}catch(o){throw new Error(`Error creating instruction; ${o}`)}}};as.programId=new X("KeccakSecp256k11111111111111111111111111111");var rE,nE=new X("StakeConfig11111111111111111111111111111111"),cs=class{constructor(e,t){this.staker=void 0,this.withdrawer=void 0,this.staker=e,this.withdrawer=t}},An=class{constructor(e,t,n){this.unixTimestamp=void 0,this.epoch=void 0,this.custodian=void 0,this.unixTimestamp=e,this.epoch=t,this.custodian=n}};rE=An;An.default=new rE(0,0,X.default);var Qc=class{constructor(){}static decodeInstructionType(e){this.checkProgramId(e.programId);let n=w.u32("instruction").decode(e.data),i;for(let[o,s]of Object.entries(It))if(s.index==n){i=o;break}if(!i)throw new Error("Instruction type incorrect; not a StakeInstruction");return i}static decodeInitialize(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,2);let{authorized:t,lockup:n}=Be(It.Initialize,e.data);return{stakePubkey:e.keys[0].pubkey,authorized:new cs(new X(t.staker),new X(t.withdrawer)),lockup:new An(n.unixTimestamp,n.epoch,new X(n.custodian))}}static decodeDelegate(e){return this.checkProgramId(e.programId),this.checkKeyLength(e.keys,6),Be(It.Delegate,e.data),{stakePubkey:e.keys[0].pubkey,votePubkey:e.keys[1].pubkey,authorizedPubkey:e.keys[5].pubkey}}static decodeAuthorize(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,3);let{newAuthorized:t,stakeAuthorizationType:n}=Be(It.Authorize,e.data),i={stakePubkey:e.keys[0].pubkey,authorizedPubkey:e.keys[2].pubkey,newAuthorizedPubkey:new X(t),stakeAuthorizationType:{index:n}};return e.keys.length>3&&(i.custodianPubkey=e.keys[3].pubkey),i}static decodeAuthorizeWithSeed(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,2);let{newAuthorized:t,stakeAuthorizationType:n,authoritySeed:i,authorityOwner:o}=Be(It.AuthorizeWithSeed,e.data),s={stakePubkey:e.keys[0].pubkey,authorityBase:e.keys[1].pubkey,authoritySeed:i,authorityOwner:new X(o),newAuthorizedPubkey:new X(t),stakeAuthorizationType:{index:n}};return e.keys.length>3&&(s.custodianPubkey=e.keys[3].pubkey),s}static decodeSplit(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,3);let{lamports:t}=Be(It.Split,e.data);return{stakePubkey:e.keys[0].pubkey,splitStakePubkey:e.keys[1].pubkey,authorizedPubkey:e.keys[2].pubkey,lamports:t}}static decodeMerge(e){return this.checkProgramId(e.programId),this.checkKeyLength(e.keys,3),Be(It.Merge,e.data),{stakePubkey:e.keys[0].pubkey,sourceStakePubKey:e.keys[1].pubkey,authorizedPubkey:e.keys[4].pubkey}}static decodeWithdraw(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,5);let{lamports:t}=Be(It.Withdraw,e.data),n={stakePubkey:e.keys[0].pubkey,toPubkey:e.keys[1].pubkey,authorizedPubkey:e.keys[4].pubkey,lamports:t};return e.keys.length>5&&(n.custodianPubkey=e.keys[5].pubkey),n}static decodeDeactivate(e){return this.checkProgramId(e.programId),this.checkKeyLength(e.keys,3),Be(It.Deactivate,e.data),{stakePubkey:e.keys[0].pubkey,authorizedPubkey:e.keys[2].pubkey}}static checkProgramId(e){if(!e.equals(Gn.programId))throw new Error("invalid instruction; programId is not StakeProgram")}static checkKeyLength(e,t){if(e.length<t)throw new Error(`invalid instruction; found ${e.length} keys, expected at least ${t}`)}},It=Object.freeze({Initialize:{index:0,layout:w.struct([w.u32("instruction"),qm(),Wm()])},Authorize:{index:1,layout:w.struct([w.u32("instruction"),Se("newAuthorized"),w.u32("stakeAuthorizationType")])},Delegate:{index:2,layout:w.struct([w.u32("instruction")])},Split:{index:3,layout:w.struct([w.u32("instruction"),w.ns64("lamports")])},Withdraw:{index:4,layout:w.struct([w.u32("instruction"),w.ns64("lamports")])},Deactivate:{index:5,layout:w.struct([w.u32("instruction")])},Merge:{index:7,layout:w.struct([w.u32("instruction")])},AuthorizeWithSeed:{index:8,layout:w.struct([w.u32("instruction"),Se("newAuthorized"),w.u32("stakeAuthorizationType"),En("authoritySeed"),Se("authorityOwner")])}}),D1=Object.freeze({Staker:{index:0},Withdrawer:{index:1}}),Gn=class{constructor(){}static initialize(e){let{stakePubkey:t,authorized:n,lockup:i}=e,o=i||An.default,s=It.Initialize,u=Oe(s,{authorized:{staker:Ae(n.staker.toBuffer()),withdrawer:Ae(n.withdrawer.toBuffer())},lockup:{unixTimestamp:o.unixTimestamp,epoch:o.epoch,custodian:Ae(o.custodian.toBuffer())}}),l={keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:Fn,isSigner:!1,isWritable:!1}],programId:this.programId,data:u};return new Me(l)}static createAccountWithSeed(e){let t=new De;t.add(xt.createAccountWithSeed({fromPubkey:e.fromPubkey,newAccountPubkey:e.stakePubkey,basePubkey:e.basePubkey,seed:e.seed,lamports:e.lamports,space:this.space,programId:this.programId}));let{stakePubkey:n,authorized:i,lockup:o}=e;return t.add(this.initialize({stakePubkey:n,authorized:i,lockup:o}))}static createAccount(e){let t=new De;t.add(xt.createAccount({fromPubkey:e.fromPubkey,newAccountPubkey:e.stakePubkey,lamports:e.lamports,space:this.space,programId:this.programId}));let{stakePubkey:n,authorized:i,lockup:o}=e;return t.add(this.initialize({stakePubkey:n,authorized:i,lockup:o}))}static delegate(e){let{stakePubkey:t,authorizedPubkey:n,votePubkey:i}=e,o=It.Delegate,s=Oe(o);return new De().add({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:i,isSigner:!1,isWritable:!1},{pubkey:yr,isSigner:!1,isWritable:!1},{pubkey:Qo,isSigner:!1,isWritable:!1},{pubkey:nE,isSigner:!1,isWritable:!1},{pubkey:n,isSigner:!0,isWritable:!1}],programId:this.programId,data:s})}static authorize(e){let{stakePubkey:t,authorizedPubkey:n,newAuthorizedPubkey:i,stakeAuthorizationType:o,custodianPubkey:s}=e,u=It.Authorize,l=Oe(u,{newAuthorized:Ae(i.toBuffer()),stakeAuthorizationType:o.index}),p=[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:yr,isSigner:!1,isWritable:!0},{pubkey:n,isSigner:!0,isWritable:!1}];return s&&p.push({pubkey:s,isSigner:!0,isWritable:!1}),new De().add({keys:p,programId:this.programId,data:l})}static authorizeWithSeed(e){let{stakePubkey:t,authorityBase:n,authoritySeed:i,authorityOwner:o,newAuthorizedPubkey:s,stakeAuthorizationType:u,custodianPubkey:l}=e,p=It.AuthorizeWithSeed,R=Oe(p,{newAuthorized:Ae(s.toBuffer()),stakeAuthorizationType:u.index,authoritySeed:i,authorityOwner:Ae(o.toBuffer())}),g=[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:n,isSigner:!0,isWritable:!1},{pubkey:yr,isSigner:!1,isWritable:!1}];return l&&g.push({pubkey:l,isSigner:!0,isWritable:!1}),new De().add({keys:g,programId:this.programId,data:R})}static splitInstruction(e){let{stakePubkey:t,authorizedPubkey:n,splitStakePubkey:i,lamports:o}=e,s=It.Split,u=Oe(s,{lamports:o});return new Me({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:i,isSigner:!1,isWritable:!0},{pubkey:n,isSigner:!0,isWritable:!1}],programId:this.programId,data:u})}static split(e,t){let n=new De;return n.add(xt.createAccount({fromPubkey:e.authorizedPubkey,newAccountPubkey:e.splitStakePubkey,lamports:t,space:this.space,programId:this.programId})),n.add(this.splitInstruction(e))}static splitWithSeed(e,t){let{stakePubkey:n,authorizedPubkey:i,splitStakePubkey:o,basePubkey:s,seed:u,lamports:l}=e,p=new De;return p.add(xt.allocate({accountPubkey:o,basePubkey:s,seed:u,space:this.space,programId:this.programId})),t&&t>0&&p.add(xt.transfer({fromPubkey:e.authorizedPubkey,toPubkey:o,lamports:t})),p.add(this.splitInstruction({stakePubkey:n,authorizedPubkey:i,splitStakePubkey:o,lamports:l}))}static merge(e){let{stakePubkey:t,sourceStakePubKey:n,authorizedPubkey:i}=e,o=It.Merge,s=Oe(o);return new De().add({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:n,isSigner:!1,isWritable:!0},{pubkey:yr,isSigner:!1,isWritable:!1},{pubkey:Qo,isSigner:!1,isWritable:!1},{pubkey:i,isSigner:!0,isWritable:!1}],programId:this.programId,data:s})}static withdraw(e){let{stakePubkey:t,authorizedPubkey:n,toPubkey:i,lamports:o,custodianPubkey:s}=e,u=It.Withdraw,l=Oe(u,{lamports:o}),p=[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:i,isSigner:!1,isWritable:!0},{pubkey:yr,isSigner:!1,isWritable:!1},{pubkey:Qo,isSigner:!1,isWritable:!1},{pubkey:n,isSigner:!0,isWritable:!1}];return s&&p.push({pubkey:s,isSigner:!0,isWritable:!1}),new De().add({keys:p,programId:this.programId,data:l})}static deactivate(e){let{stakePubkey:t,authorizedPubkey:n}=e,i=It.Deactivate,o=Oe(i);return new De().add({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:yr,isSigner:!1,isWritable:!1},{pubkey:n,isSigner:!0,isWritable:!1}],programId:this.programId,data:o})}};Gn.programId=new X("Stake11111111111111111111111111111111111111");Gn.space=200;var us=class{constructor(e,t,n,i){this.nodePubkey=void 0,this.authorizedVoter=void 0,this.authorizedWithdrawer=void 0,this.commission=void 0,this.nodePubkey=e,this.authorizedVoter=t,this.authorizedWithdrawer=n,this.commission=i}},eu=class{constructor(){}static decodeInstructionType(e){this.checkProgramId(e.programId);let n=w.u32("instruction").decode(e.data),i;for(let[o,s]of Object.entries(Sr))if(s.index==n){i=o;break}if(!i)throw new Error("Instruction type incorrect; not a VoteInstruction");return i}static decodeInitializeAccount(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,4);let{voteInit:t}=Be(Sr.InitializeAccount,e.data);return{votePubkey:e.keys[0].pubkey,nodePubkey:e.keys[3].pubkey,voteInit:new us(new X(t.nodePubkey),new X(t.authorizedVoter),new X(t.authorizedWithdrawer),t.commission)}}static decodeAuthorize(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,3);let{newAuthorized:t,voteAuthorizationType:n}=Be(Sr.Authorize,e.data);return{votePubkey:e.keys[0].pubkey,authorizedPubkey:e.keys[2].pubkey,newAuthorizedPubkey:new X(t),voteAuthorizationType:{index:n}}}static decodeAuthorizeWithSeed(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,3);let{voteAuthorizeWithSeedArgs:{currentAuthorityDerivedKeyOwnerPubkey:t,currentAuthorityDerivedKeySeed:n,newAuthorized:i,voteAuthorizationType:o}}=Be(Sr.AuthorizeWithSeed,e.data);return{currentAuthorityDerivedKeyBasePubkey:e.keys[2].pubkey,currentAuthorityDerivedKeyOwnerPubkey:new X(t),currentAuthorityDerivedKeySeed:n,newAuthorizedPubkey:new X(i),voteAuthorizationType:{index:o},votePubkey:e.keys[0].pubkey}}static decodeWithdraw(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,3);let{lamports:t}=Be(Sr.Withdraw,e.data);return{votePubkey:e.keys[0].pubkey,authorizedWithdrawerPubkey:e.keys[2].pubkey,lamports:t,toPubkey:e.keys[1].pubkey}}static checkProgramId(e){if(!e.equals(Hn.programId))throw new Error("invalid instruction; programId is not VoteProgram")}static checkKeyLength(e,t){if(e.length<t)throw new Error(`invalid instruction; found ${e.length} keys, expected at least ${t}`)}},Sr=Object.freeze({InitializeAccount:{index:0,layout:w.struct([w.u32("instruction"),$m()])},Authorize:{index:1,layout:w.struct([w.u32("instruction"),Se("newAuthorized"),w.u32("voteAuthorizationType")])},Withdraw:{index:3,layout:w.struct([w.u32("instruction"),w.ns64("lamports")])},UpdateValidatorIdentity:{index:4,layout:w.struct([w.u32("instruction")])},AuthorizeWithSeed:{index:10,layout:w.struct([w.u32("instruction"),Ym()])}}),M1=Object.freeze({Voter:{index:0},Withdrawer:{index:1}}),Hn=class r{constructor(){}static initializeAccount(e){let{votePubkey:t,nodePubkey:n,voteInit:i}=e,o=Sr.InitializeAccount,s=Oe(o,{voteInit:{nodePubkey:Ae(i.nodePubkey.toBuffer()),authorizedVoter:Ae(i.authorizedVoter.toBuffer()),authorizedWithdrawer:Ae(i.authorizedWithdrawer.toBuffer()),commission:i.commission}}),u={keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:Fn,isSigner:!1,isWritable:!1},{pubkey:yr,isSigner:!1,isWritable:!1},{pubkey:n,isSigner:!0,isWritable:!1}],programId:this.programId,data:s};return new Me(u)}static createAccount(e){let t=new De;return t.add(xt.createAccount({fromPubkey:e.fromPubkey,newAccountPubkey:e.votePubkey,lamports:e.lamports,space:this.space,programId:this.programId})),t.add(this.initializeAccount({votePubkey:e.votePubkey,nodePubkey:e.voteInit.nodePubkey,voteInit:e.voteInit}))}static authorize(e){let{votePubkey:t,authorizedPubkey:n,newAuthorizedPubkey:i,voteAuthorizationType:o}=e,s=Sr.Authorize,u=Oe(s,{newAuthorized:Ae(i.toBuffer()),voteAuthorizationType:o.index}),l=[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:yr,isSigner:!1,isWritable:!1},{pubkey:n,isSigner:!0,isWritable:!1}];return new De().add({keys:l,programId:this.programId,data:u})}static authorizeWithSeed(e){let{currentAuthorityDerivedKeyBasePubkey:t,currentAuthorityDerivedKeyOwnerPubkey:n,currentAuthorityDerivedKeySeed:i,newAuthorizedPubkey:o,voteAuthorizationType:s,votePubkey:u}=e,l=Sr.AuthorizeWithSeed,p=Oe(l,{voteAuthorizeWithSeedArgs:{currentAuthorityDerivedKeyOwnerPubkey:Ae(n.toBuffer()),currentAuthorityDerivedKeySeed:i,newAuthorized:Ae(o.toBuffer()),voteAuthorizationType:s.index}}),R=[{pubkey:u,isSigner:!1,isWritable:!0},{pubkey:yr,isSigner:!1,isWritable:!1},{pubkey:t,isSigner:!0,isWritable:!1}];return new De().add({keys:R,programId:this.programId,data:p})}static withdraw(e){let{votePubkey:t,authorizedWithdrawerPubkey:n,lamports:i,toPubkey:o}=e,s=Sr.Withdraw,u=Oe(s,{lamports:i}),l=[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:o,isSigner:!1,isWritable:!0},{pubkey:n,isSigner:!0,isWritable:!1}];return new De().add({keys:l,programId:this.programId,data:u})}static safeWithdraw(e,t,n){if(e.lamports>t-n)throw new Error("Withdraw will leave vote account with insufficient funds.");return r.withdraw(e)}static updateValidatorIdentity(e){let{votePubkey:t,authorizedWithdrawerPubkey:n,nodePubkey:i}=e,o=Sr.UpdateValidatorIdentity,s=Oe(o),u=[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:i,isSigner:!0,isWritable:!1},{pubkey:n,isSigner:!0,isWritable:!1}];return new De().add({keys:u,programId:this.programId,data:s})}};Hn.programId=new X("Vote111111111111111111111111111111111111111");Hn.space=3762;var iE=new X("Va1idator1nfo111111111111111111111111111111"),U1=c.type({name:c.string(),website:c.optional(c.string()),details:c.optional(c.string()),iconUrl:c.optional(c.string()),keybaseUsername:c.optional(c.string())}),tu=class r{constructor(e,t){this.key=void 0,this.info=void 0,this.key=e,this.info=t}static fromConfigData(e){let t=[...e];if(Xt(t)!==2)return null;let i=[];for(let o=0;o<2;o++){let s=new X(Zt(t,0,br)),u=Ar(t)===1;i.push({publicKey:s,isSigner:u})}if(i[0].publicKey.equals(iE)&&i[1].isSigner){let o=En().decode(se.Buffer.from(t)),s=JSON.parse(o);return c.assert(s,U1),new r(i[1].publicKey,s)}return null}},P1=new X("Vote111111111111111111111111111111111111111"),F1=w.struct([Se("nodePubkey"),Se("authorizedWithdrawer"),w.u8("commission"),w.nu64(),w.seq(w.struct([w.nu64("slot"),w.u32("confirmationCount")]),w.offset(w.u32(),-8),"votes"),w.u8("rootSlotValid"),w.nu64("rootSlot"),w.nu64(),w.seq(w.struct([w.nu64("epoch"),Se("authorizedVoter")]),w.offset(w.u32(),-8),"authorizedVoters"),w.struct([w.seq(w.struct([Se("authorizedPubkey"),w.nu64("epochOfLastAuthorizedSwitch"),w.nu64("targetEpoch")]),32,"buf"),w.nu64("idx"),w.u8("isEmpty")],"priorVoters"),w.nu64(),w.seq(w.struct([w.nu64("epoch"),w.nu64("credits"),w.nu64("prevCredits")]),w.offset(w.u32(),-8),"epochCredits"),w.struct([w.nu64("slot"),w.nu64("timestamp")],"lastTimestamp")]),ru=class r{constructor(e){this.nodePubkey=void 0,this.authorizedWithdrawer=void 0,this.commission=void 0,this.rootSlot=void 0,this.votes=void 0,this.authorizedVoters=void 0,this.priorVoters=void 0,this.epochCredits=void 0,this.lastTimestamp=void 0,this.nodePubkey=e.nodePubkey,this.authorizedWithdrawer=e.authorizedWithdrawer,this.commission=e.commission,this.rootSlot=e.rootSlot,this.votes=e.votes,this.authorizedVoters=e.authorizedVoters,this.priorVoters=e.priorVoters,this.epochCredits=e.epochCredits,this.lastTimestamp=e.lastTimestamp}static fromAccountData(e){let n=F1.decode(Ae(e),4),i=n.rootSlot;return n.rootSlotValid||(i=null),new r({nodePubkey:new X(n.nodePubkey),authorizedWithdrawer:new X(n.authorizedWithdrawer),commission:n.commission,votes:n.votes,rootSlot:i,authorizedVoters:n.authorizedVoters.map(z1),priorVoters:V1(n.priorVoters),epochCredits:n.epochCredits,lastTimestamp:n.lastTimestamp})}};function z1({authorizedVoter:r,epoch:e}){return{epoch:e,authorizedVoter:new X(r)}}function BR({authorizedPubkey:r,epochOfLastAuthorizedSwitch:e,targetEpoch:t}){return{authorizedPubkey:new X(r),epochOfLastAuthorizedSwitch:e,targetEpoch:t}}function V1({buf:r,idx:e,isEmpty:t}){return t?[]:[...r.slice(e+1).map(BR),...r.slice(0,e).map(BR)]}var DR={http:{devnet:"http://api.devnet.solana.com",testnet:"http://api.testnet.solana.com","mainnet-beta":"http://api.mainnet-beta.solana.com/"},https:{devnet:"https://api.devnet.solana.com",testnet:"https://api.testnet.solana.com","mainnet-beta":"https://api.mainnet-beta.solana.com/"}};function G1(r,e){let t=e===!1?"http":"https";if(!r)return DR[t].devnet;let n=DR[t][r];if(!n)throw new Error(`Unknown ${t} cluster: ${r}`);return n}async function H1(r,e,t,n){let i,o;t&&Object.prototype.hasOwnProperty.call(t,"lastValidBlockHeight")||t&&Object.prototype.hasOwnProperty.call(t,"nonceValue")?(i=t,o=n):o=t;let s=o&&{skipPreflight:o.skipPreflight,preflightCommitment:o.preflightCommitment||o.commitment,minContextSlot:o.minContextSlot},u=await r.sendRawTransaction(e,s),l=o&&o.commitment,R=(await(i?r.confirmTransaction(i,l):r.confirmTransaction(u,l))).value;if(R.err)throw u!=null?new yn({action:s?.skipPreflight?"send":"simulate",signature:u,transactionMessage:`Status: (${JSON.stringify(R)})`}):new Error(`Raw transaction ${u} failed (${JSON.stringify(R)})`);return u}var K1=1e9;Z.Account=Fc;Z.AddressLookupTableAccount=Ni;Z.AddressLookupTableInstruction=Zc;Z.AddressLookupTableProgram=vi;Z.Authorized=cs;Z.BLOCKHASH_CACHE_TIMEOUT_MS=WR;Z.BPF_LOADER_DEPRECATED_PROGRAM_ID=Hm;Z.BPF_LOADER_PROGRAM_ID=sS;Z.BpfLoader=qc;Z.COMPUTE_BUDGET_INSTRUCTION_LAYOUTS=mr;Z.ComputeBudgetInstruction=Jc;Z.ComputeBudgetProgram=Ci;Z.Connection=Xc;Z.Ed25519Program=ss;Z.Enum=Pc;Z.EpochSchedule=ns;Z.FeeCalculatorLayout=HR;Z.Keypair=os;Z.LAMPORTS_PER_SOL=K1;Z.LOOKUP_TABLE_INSTRUCTION_LAYOUTS=Br;Z.Loader=Vn;Z.Lockup=An;Z.MAX_SEED_LENGTH=FR;Z.Message=Or;Z.MessageAccountKeys=gn;Z.MessageV0=Pn;Z.NONCE_ACCOUNT_LENGTH=Hc;Z.NonceAccount=rs;Z.PACKET_DATA_SIZE=tn;Z.PUBLIC_KEY_LENGTH=br;Z.PublicKey=X;Z.SIGNATURE_LENGTH_IN_BYTES=Oi;Z.SOLANA_SCHEMA=Si;Z.STAKE_CONFIG_ID=nE;Z.STAKE_INSTRUCTION_LAYOUTS=It;Z.SYSTEM_INSTRUCTION_LAYOUTS=He;Z.SYSVAR_CLOCK_PUBKEY=yr;Z.SYSVAR_EPOCH_SCHEDULE_PUBKEY=Qm;Z.SYSVAR_INSTRUCTIONS_PUBKEY=eS;Z.SYSVAR_RECENT_BLOCKHASHES_PUBKEY=Jo;Z.SYSVAR_RENT_PUBKEY=Fn;Z.SYSVAR_REWARDS_PUBKEY=tS;Z.SYSVAR_SLOT_HASHES_PUBKEY=rS;Z.SYSVAR_SLOT_HISTORY_PUBKEY=nS;Z.SYSVAR_STAKE_HISTORY_PUBKEY=Qo;Z.Secp256k1Program=as;Z.SendTransactionError=yn;Z.SolanaJSONRPCError=re;Z.SolanaJSONRPCErrorCode=iS;Z.StakeAuthorizationLayout=D1;Z.StakeInstruction=Qc;Z.StakeProgram=Gn;Z.Struct=bi;Z.SystemInstruction=Kc;Z.SystemProgram=xt;Z.Transaction=De;Z.TransactionExpiredBlockheightExceededError=wi;Z.TransactionExpiredNonceInvalidError=en;Z.TransactionExpiredTimeoutError=Ii;Z.TransactionInstruction=Me;Z.TransactionMessage=zc;Z.TransactionStatus=kr;Z.VALIDATOR_INFO_KEY=iE;Z.VERSION_PREFIX_MASK=ls;Z.VOTE_PROGRAM_ID=P1;Z.ValidatorInfo=tu;Z.VersionedMessage=su;Z.VersionedTransaction=Vc;Z.VoteAccount=ru;Z.VoteAuthorizationLayout=M1;Z.VoteInit=us;Z.VoteInstruction=eu;Z.VoteProgram=Hn;Z.clusterApiUrl=G1;Z.sendAndConfirmRawTransaction=H1;Z.sendAndConfirmTransaction=Gc});z();z();var cE=Au(oE());z();z();function q1(r){if(r.length>=255)throw new TypeError("Alphabet too long");let e=new Uint8Array(256);for(let p=0;p<e.length;p++)e[p]=255;for(let p=0;p<r.length;p++){let R=r.charAt(p),g=R.charCodeAt(0);if(e[g]!==255)throw new TypeError(R+" is ambiguous");e[g]=p}let t=r.length,n=r.charAt(0),i=Math.log(t)/Math.log(256),o=Math.log(256)/Math.log(t);function s(p){if(p instanceof Uint8Array||(ArrayBuffer.isView(p)?p=new Uint8Array(p.buffer,p.byteOffset,p.byteLength):Array.isArray(p)&&(p=Uint8Array.from(p))),!(p instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(p.length===0)return"";let R=0,g=0,v=0,m=p.length;for(;v!==m&&p[v]===0;)v++,R++;let x=(m-v)*o+1>>>0,I=new Uint8Array(x);for(;v!==m;){let k=p[v],B=0;for(let F=x-1;(k!==0||B<g)&&F!==-1;F--,B++)k+=256*I[F]>>>0,I[F]=k%t>>>0,k=k/t>>>0;if(k!==0)throw new Error("Non-zero carry");g=B,v++}let O=x-g;for(;O!==x&&I[O]===0;)O++;let D=n.repeat(R);for(;O<x;++O)D+=r.charAt(I[O]);return D}function u(p){if(typeof p!="string")throw new TypeError("Expected String");if(p.length===0)return new Uint8Array;let R=0,g=0,v=0;for(;p[R]===n;)g++,R++;let m=(p.length-R)*i+1>>>0,x=new Uint8Array(m);for(;R<p.length;){let k=p.charCodeAt(R);if(k>255)return;let B=e[k];if(B===255)return;let F=0;for(let $=m-1;(B!==0||F<v)&&$!==-1;$--,F++)B+=t*x[$]>>>0,x[$]=B%256>>>0,B=B/256>>>0;if(B!==0)throw new Error("Non-zero carry");v=F,R++}let I=m-v;for(;I!==m&&x[I]===0;)I++;let O=new Uint8Array(g+(m-I)),D=g;for(;I!==m;)O[D++]=x[I++];return O}function l(p){let R=u(p);if(R)return R;throw new Error("Non-base"+t+" character")}return{encode:s,decodeUnsafe:u,decode:l}}var sE=q1;var W1="**********************************************************",aE=sE(W1);var uE=async(r,e)=>{let t=cE.Keypair.generate(),n=aE.encode(t.secretKey),i=[];for(let s of e)i.length>0&&i.push({operator:"or"}),i.push({contractAddress:"",standardContractType:"",chain:"ethereum",method:"",parameters:[":currentActionIpfsId"],returnValueTest:{comparator:"=",value:s}});console.log("accessControlPkOnlyAuthorizedAction=>",JSON.stringify(i));let o=await r.encrypt(n,i);return{publicKey:t.publicKey.toBase58(),...o}};z();var lE=r=>{let e=Lit.Auth.authMethodContexts;return console.log(`Verifying caller: ${r}`),e.some(n=>{let i=n.userId;return i?i.toLowerCase()===r.toLowerCase():!1})?!0:(console.log(`Caller ${JSON.stringify(e)} is not authorized. Expected: ${r}`),!1)};z();z();z();z();var $1={address:"******************************************",chain:8453};z();var Y1={address:"******************************************",chain:8453};var lu=r=>{if(r==="dev")return"******************************************";if(r==="staging")return"******************************************";throw new Error(`No executor address found for environment: ${r}`)};var oI=[8453,10,1399811149,42161,43114,56,146,1,137];z();var hs=class{async encrypt(e,t){let i=new TextEncoder().encode(e);try{let{ciphertext:o,dataToEncryptHash:s}=await Lit.Actions.encrypt({accessControlConditions:t,to_encrypt:i});return{ciphertext:o,dataToEncryptHash:s}}catch(o){return console.log("error: ",o),JSON.stringify({message:o.message,note:"Error in encrypting the private key."})}}};var j1=async()=>{if(console.log("Creating orchestrator..."),!lE(lu(env)))throw new Error(`Caller is not authorized. Expected: ${lu(env)}`);let r=await uE(new hs,ipfsHashs);return Lit.Actions.setResponse({response:JSON.stringify(r)})};j1();})();
/*! Bundled license information:

ieee754/index.js:
  (*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> *)

buffer/index.js:
  (*!
   * The buffer module from node.js, for the browser.
   *
   * <AUTHOR> Aboukhadijeh <https://feross.org>
   * @license  MIT
   *)

@noble/hashes/utils.js:
  (*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/abstract/utils.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/abstract/modular.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/abstract/curve.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/abstract/edwards.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/abstract/montgomery.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/ed25519.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

safe-buffer/index.js:
  (*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> *)

@solana/buffer-layout/lib/Layout.js:
  (**
   * Support for translating between Uint8Array instances and JavaScript
   * native types.
   *
   * {@link module:Layout~Layout|Layout} is the basis of a class
   * hierarchy that associates property names with sequences of encoded
   * bytes.
   *
   * Layouts are supported for these scalar (numeric) types:
   * * {@link module:Layout~UInt|Unsigned integers in little-endian
   *   format} with {@link module:Layout.u8|8-bit}, {@link
   *   module:Layout.u16|16-bit}, {@link module:Layout.u24|24-bit},
   *   {@link module:Layout.u32|32-bit}, {@link
   *   module:Layout.u40|40-bit}, and {@link module:Layout.u48|48-bit}
   *   representation ranges;
   * * {@link module:Layout~UIntBE|Unsigned integers in big-endian
   *   format} with {@link module:Layout.u16be|16-bit}, {@link
   *   module:Layout.u24be|24-bit}, {@link module:Layout.u32be|32-bit},
   *   {@link module:Layout.u40be|40-bit}, and {@link
   *   module:Layout.u48be|48-bit} representation ranges;
   * * {@link module:Layout~Int|Signed integers in little-endian
   *   format} with {@link module:Layout.s8|8-bit}, {@link
   *   module:Layout.s16|16-bit}, {@link module:Layout.s24|24-bit},
   *   {@link module:Layout.s32|32-bit}, {@link
   *   module:Layout.s40|40-bit}, and {@link module:Layout.s48|48-bit}
   *   representation ranges;
   * * {@link module:Layout~IntBE|Signed integers in big-endian format}
   *   with {@link module:Layout.s16be|16-bit}, {@link
   *   module:Layout.s24be|24-bit}, {@link module:Layout.s32be|32-bit},
   *   {@link module:Layout.s40be|40-bit}, and {@link
   *   module:Layout.s48be|48-bit} representation ranges;
   * * 64-bit integral values that decode to an exact (if magnitude is
   *   less than 2^53) or nearby integral Number in {@link
   *   module:Layout.nu64|unsigned little-endian}, {@link
   *   module:Layout.nu64be|unsigned big-endian}, {@link
   *   module:Layout.ns64|signed little-endian}, and {@link
   *   module:Layout.ns64be|unsigned big-endian} encodings;
   * * 32-bit floating point values with {@link
   *   module:Layout.f32|little-endian} and {@link
   *   module:Layout.f32be|big-endian} representations;
   * * 64-bit floating point values with {@link
   *   module:Layout.f64|little-endian} and {@link
   *   module:Layout.f64be|big-endian} representations;
   * * {@link module:Layout.const|Constants} that take no space in the
   *   encoded expression.
   *
   * and for these aggregate types:
   * * {@link module:Layout.seq|Sequence}s of instances of a {@link
   *   module:Layout~Layout|Layout}, with JavaScript representation as
   *   an Array and constant or data-dependent {@link
   *   module:Layout~Sequence#count|length};
   * * {@link module:Layout.struct|Structure}s that aggregate a
   *   heterogeneous sequence of {@link module:Layout~Layout|Layout}
   *   instances, with JavaScript representation as an Object;
   * * {@link module:Layout.union|Union}s that support multiple {@link
   *   module:Layout~VariantLayout|variant layouts} over a fixed
   *   (padded) or variable (not padded) span of bytes, using an
   *   unsigned integer at the start of the data or a separate {@link
   *   module:Layout.unionLayoutDiscriminator|layout element} to
   *   determine which layout to use when interpreting the buffer
   *   contents;
   * * {@link module:Layout.bits|BitStructure}s that contain a sequence
   *   of individual {@link
   *   module:Layout~BitStructure#addField|BitField}s packed into an 8,
   *   16, 24, or 32-bit unsigned integer starting at the least- or
   *   most-significant bit;
   * * {@link module:Layout.cstr|C strings} of varying length;
   * * {@link module:Layout.blob|Blobs} of fixed- or variable-{@link
   *   module:Layout~Blob#length|length} raw data.
   *
   * All {@link module:Layout~Layout|Layout} instances are immutable
   * after construction, to prevent internal state from becoming
   * inconsistent.
   *
   * @local Layout
   * @local ExternalLayout
   * @local GreedyCount
   * @local OffsetLayout
   * @local UInt
   * @local UIntBE
   * @local Int
   * @local IntBE
   * @local NearUInt64
   * @local NearUInt64BE
   * @local NearInt64
   * @local NearInt64BE
   * @local Float
   * @local FloatBE
   * @local Double
   * @local DoubleBE
   * @local Sequence
   * @local Structure
   * @local UnionDiscriminator
   * @local UnionLayoutDiscriminator
   * @local Union
   * @local VariantLayout
   * @local BitStructure
   * @local BitField
   * @local Boolean
   * @local Blob
   * @local CString
   * @local Constant
   * @local bindConstructorLayout
   * @module Layout
   * @license MIT
   * <AUTHOR> A. Bigot
   * @see {@link https://github.com/pabigot/buffer-layout|buffer-layout on GitHub}
   *)

@noble/curves/abstract/weierstrass.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/_shortw_utils.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/secp256k1.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)
*/
