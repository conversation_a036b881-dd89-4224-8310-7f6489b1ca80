{"systemParams": "win32-x64-137", "modulesFolders": ["node_modules"], "flags": [], "linkedModules": [], "topLevelPatterns": ["@coral-xyz/anchor@^0.30.0", "@solana/spl-token@^0.4.6", "@types/bn.js@^5.1.0", "@types/chai@^4.3.0", "@types/mocha@^9.0.0", "axios@^1.7.2", "chai@^4.3.4", "commander@^12.1.0", "jito-ts@^4.1.0", "mocha@^9.0.3", "prettier@^2.6.2", "ts-mocha@^10.0.0", "typescript@^4.3.5"], "lockfileEntries": {"@babel/runtime@^7.12.5": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/runtime@^7.25.0": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@coral-xyz/anchor-errors@^0.30.1": "https://registry.npmjs.org/@coral-xyz/anchor-errors/-/anchor-errors-0.30.1.tgz", "@coral-xyz/anchor@^0.30.0": "https://registry.npmjs.org/@coral-xyz/anchor/-/anchor-0.30.1.tgz", "@coral-xyz/borsh@^0.30.1": "https://registry.npmjs.org/@coral-xyz/borsh/-/borsh-0.30.1.tgz", "@grpc/grpc-js@^1.8.13": "https://registry.npmjs.org/@grpc/grpc-js/-/grpc-js-1.10.10.tgz", "@grpc/proto-loader@^0.7.13": "https://registry.npmjs.org/@grpc/proto-loader/-/proto-loader-0.7.13.tgz", "@js-sdsl/ordered-map@^4.4.2": "https://registry.npmjs.org/@js-sdsl/ordered-map/-/ordered-map-4.4.2.tgz", "@noble/curves@^1.0.0": "https://registry.npmjs.org/@noble/curves/-/curves-1.9.4.tgz", "@noble/curves@^1.4.2": "https://registry.npmjs.org/@noble/curves/-/curves-1.9.4.tgz", "@noble/ed25519@^1.7.1": "https://registry.npmjs.org/@noble/ed25519/-/ed25519-1.7.3.tgz", "@noble/hashes@1.8.0": "https://registry.npmjs.org/@noble/hashes/-/hashes-1.8.0.tgz", "@noble/hashes@^1.3.0": "https://registry.npmjs.org/@noble/hashes/-/hashes-1.8.0.tgz", "@noble/hashes@^1.3.1": "https://registry.npmjs.org/@noble/hashes/-/hashes-1.8.0.tgz", "@noble/hashes@^1.4.0": "https://registry.npmjs.org/@noble/hashes/-/hashes-1.8.0.tgz", "@protobufjs/aspromise@^1.1.1": "https://registry.npmjs.org/@protobufjs/aspromise/-/aspromise-1.1.2.tgz", "@protobufjs/aspromise@^1.1.2": "https://registry.npmjs.org/@protobufjs/aspromise/-/aspromise-1.1.2.tgz", "@protobufjs/base64@^1.1.2": "https://registry.npmjs.org/@protobufjs/base64/-/base64-1.1.2.tgz", "@protobufjs/codegen@^2.0.4": "https://registry.npmjs.org/@protobufjs/codegen/-/codegen-2.0.4.tgz", "@protobufjs/eventemitter@^1.1.0": "https://registry.npmjs.org/@protobufjs/eventemitter/-/eventemitter-1.1.0.tgz", "@protobufjs/fetch@^1.1.0": "https://registry.npmjs.org/@protobufjs/fetch/-/fetch-1.1.0.tgz", "@protobufjs/float@^1.0.2": "https://registry.npmjs.org/@protobufjs/float/-/float-1.0.2.tgz", "@protobufjs/inquire@^1.1.0": "https://registry.npmjs.org/@protobufjs/inquire/-/inquire-1.1.0.tgz", "@protobufjs/path@^1.1.2": "https://registry.npmjs.org/@protobufjs/path/-/path-1.1.2.tgz", "@protobufjs/pool@^1.1.0": "https://registry.npmjs.org/@protobufjs/pool/-/pool-1.1.0.tgz", "@protobufjs/utf8@^1.1.0": "https://registry.npmjs.org/@protobufjs/utf8/-/utf8-1.1.0.tgz", "@solana/buffer-layout-utils@^0.2.0": "https://registry.npmjs.org/@solana/buffer-layout-utils/-/buffer-layout-utils-0.2.0.tgz", "@solana/buffer-layout@^4.0.0": "https://registry.npmjs.org/@solana/buffer-layout/-/buffer-layout-4.0.1.tgz", "@solana/buffer-layout@^4.0.1": "https://registry.npmjs.org/@solana/buffer-layout/-/buffer-layout-4.0.1.tgz", "@solana/codecs-core@2.0.0-preview.2": "https://registry.npmjs.org/@solana/codecs-core/-/codecs-core-2.0.0-preview.2.tgz", "@solana/codecs-core@2.3.0": "https://registry.npmjs.org/@solana/codecs-core/-/codecs-core-2.3.0.tgz", "@solana/codecs-data-structures@2.0.0-preview.2": "https://registry.npmjs.org/@solana/codecs-data-structures/-/codecs-data-structures-2.0.0-preview.2.tgz", "@solana/codecs-numbers@2.0.0-preview.2": "https://registry.npmjs.org/@solana/codecs-numbers/-/codecs-numbers-2.0.0-preview.2.tgz", "@solana/codecs-numbers@^2.1.0": "https://registry.npmjs.org/@solana/codecs-numbers/-/codecs-numbers-2.3.0.tgz", "@solana/codecs-strings@2.0.0-preview.2": "https://registry.npmjs.org/@solana/codecs-strings/-/codecs-strings-2.0.0-preview.2.tgz", "@solana/codecs@2.0.0-preview.2": "https://registry.npmjs.org/@solana/codecs/-/codecs-2.0.0-preview.2.tgz", "@solana/errors@2.0.0-preview.2": "https://registry.npmjs.org/@solana/errors/-/errors-2.0.0-preview.2.tgz", "@solana/errors@2.3.0": "https://registry.npmjs.org/@solana/errors/-/errors-2.3.0.tgz", "@solana/options@2.0.0-preview.2": "https://registry.npmjs.org/@solana/options/-/options-2.0.0-preview.2.tgz", "@solana/spl-token-group@^0.0.4": "https://registry.npmjs.org/@solana/spl-token-group/-/spl-token-group-0.0.4.tgz", "@solana/spl-token-metadata@^0.1.4": "https://registry.npmjs.org/@solana/spl-token-metadata/-/spl-token-metadata-0.1.4.tgz", "@solana/spl-token@^0.4.6": "https://registry.npmjs.org/@solana/spl-token/-/spl-token-0.4.6.tgz", "@solana/spl-type-length-value@0.1.0": "https://registry.npmjs.org/@solana/spl-type-length-value/-/spl-type-length-value-0.1.0.tgz", "@solana/web3.js@^1.32.0": "https://registry.npmjs.org/@solana/web3.js/-/web3.js-1.98.2.tgz", "@solana/web3.js@^1.68.0": "https://registry.npmjs.org/@solana/web3.js/-/web3.js-1.98.2.tgz", "@solana/web3.js@~1.77.3": "https://registry.npmjs.org/@solana/web3.js/-/web3.js-1.77.4.tgz", "@swc/helpers@^0.5.11": "https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.17.tgz", "@types/bn.js@^5.1.0": "https://registry.npmjs.org/@types/bn.js/-/bn.js-5.1.5.tgz", "@types/bs58@^4.0.4": "https://registry.npmjs.org/@types/bs58/-/bs58-4.0.4.tgz", "@types/chai@^4.3.0": "https://registry.npmjs.org/@types/chai/-/chai-4.3.16.tgz", "@types/connect@^3.4.33": "https://registry.npmjs.org/@types/connect/-/connect-3.4.38.tgz", "@types/json5@^0.0.29": "https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz", "@types/mocha@^9.0.0": "https://registry.npmjs.org/@types/mocha/-/mocha-9.1.1.tgz", "@types/node@*": "https://registry.npmjs.org/@types/node/-/node-20.12.12.tgz", "@types/node@>=13.7.0": "https://registry.npmjs.org/@types/node/-/node-20.12.12.tgz", "@types/node@^12.12.54": "https://registry.npmjs.org/@types/node/-/node-12.20.55.tgz", "@types/uuid@^8.3.4": "https://registry.npmjs.org/@types/uuid/-/uuid-8.3.4.tgz", "@types/ws@^7.4.4": "https://registry.npmjs.org/@types/ws/-/ws-7.4.7.tgz", "@types/ws@^8.2.2": "https://registry.npmjs.org/@types/ws/-/ws-8.18.1.tgz", "@ungap/promise-all-settled@1.1.2": "https://registry.npmjs.org/@ungap/promise-all-settled/-/promise-all-settled-1.1.2.tgz", "agentkeepalive@^4.2.1": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.5.0.tgz", "agentkeepalive@^4.3.0": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.5.0.tgz", "agentkeepalive@^4.5.0": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.5.0.tgz", "ansi-colors@4.1.1": "https://registry.npmjs.org/ansi-colors/-/ansi-colors-4.1.1.tgz", "ansi-regex@^5.0.1": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "ansi-styles@^4.0.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "ansi-styles@^4.1.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "anymatch@~3.1.2": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz", "argparse@^2.0.1": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz", "arrify@^1.0.0": "https://registry.npmjs.org/arrify/-/arrify-1.0.1.tgz", "assertion-error@^1.1.0": "https://registry.npmjs.org/assertion-error/-/assertion-error-1.1.0.tgz", "asynckit@^0.4.0": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "axios@^1.7.2": "https://registry.npmjs.org/axios/-/axios-1.7.2.tgz", "balanced-match@^1.0.0": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "base-x@^3.0.2": "https://registry.npmjs.org/base-x/-/base-x-3.0.9.tgz", "base-x@^3.0.6": "https://registry.npmjs.org/base-x/-/base-x-3.0.9.tgz", "base-x@^5.0.0": "https://registry.npmjs.org/base-x/-/base-x-5.0.0.tgz", "base64-js@^1.3.1": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "bigint-buffer@^1.1.5": "https://registry.npmjs.org/bigint-buffer/-/bigint-buffer-1.1.5.tgz", "bignumber.js@^9.0.1": "https://registry.npmjs.org/bignumber.js/-/bignumber.js-9.1.2.tgz", "binary-extensions@^2.0.0": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz", "bindings@^1.3.0": "https://registry.npmjs.org/bindings/-/bindings-1.5.0.tgz", "bn.js@^5.0.0": "https://registry.npmjs.org/bn.js/-/bn.js-5.2.1.tgz", "bn.js@^5.1.2": "https://registry.npmjs.org/bn.js/-/bn.js-5.2.1.tgz", "bn.js@^5.2.0": "https://registry.npmjs.org/bn.js/-/bn.js-5.2.1.tgz", "bn.js@^5.2.1": "https://registry.npmjs.org/bn.js/-/bn.js-5.2.1.tgz", "borsh@^0.7.0": "https://registry.npmjs.org/borsh/-/borsh-0.7.0.tgz", "brace-expansion@^1.1.7": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "braces@~3.0.2": "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz", "browser-stdout@1.3.1": "https://registry.npmjs.org/browser-stdout/-/browser-stdout-1.3.1.tgz", "bs58@^4.0.0": "https://registry.npmjs.org/bs58/-/bs58-4.0.1.tgz", "bs58@^4.0.1": "https://registry.npmjs.org/bs58/-/bs58-4.0.1.tgz", "bs58@^6.0.0": "https://registry.npmjs.org/bs58/-/bs58-6.0.0.tgz", "buffer-from@^1.0.0": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "buffer-from@^1.1.0": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "buffer-layout@^1.2.0": "https://registry.npmjs.org/buffer-layout/-/buffer-layout-1.2.2.tgz", "buffer-layout@^1.2.2": "https://registry.npmjs.org/buffer-layout/-/buffer-layout-1.2.2.tgz", "buffer@6.0.3": "https://registry.npmjs.org/buffer/-/buffer-6.0.3.tgz", "buffer@^6.0.3": "https://registry.npmjs.org/buffer/-/buffer-6.0.3.tgz", "buffer@~6.0.3": "https://registry.npmjs.org/buffer/-/buffer-6.0.3.tgz", "bufferutil@^4.0.1": "https://registry.npmjs.org/bufferutil/-/bufferutil-4.0.8.tgz", "camelcase@^6.0.0": "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz", "camelcase@^6.3.0": "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz", "chai@^4.3.4": "https://registry.npmjs.org/chai/-/chai-4.4.1.tgz", "chalk@^4.1.0": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "chalk@^5.3.0": "https://registry.npmjs.org/chalk/-/chalk-5.4.1.tgz", "chalk@^5.4.1": "https://registry.npmjs.org/chalk/-/chalk-5.4.1.tgz", "check-error@^1.0.3": "https://registry.npmjs.org/check-error/-/check-error-1.0.3.tgz", "chokidar@3.5.3": "https://registry.npmjs.org/chokidar/-/chokidar-3.5.3.tgz", "cliui@^7.0.2": "https://registry.npmjs.org/cliui/-/cliui-7.0.4.tgz", "cliui@^8.0.1": "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz", "color-convert@^2.0.1": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "color-name@~1.1.4": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "combined-stream@^1.0.8": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "commander@^12.0.0": "https://registry.npmjs.org/commander/-/commander-12.1.0.tgz", "commander@^12.1.0": "https://registry.npmjs.org/commander/-/commander-12.1.0.tgz", "commander@^14.0.0": "https://registry.npmjs.org/commander/-/commander-14.0.0.tgz", "commander@^2.20.3": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "concat-map@0.0.1": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "cross-fetch@^3.1.5": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.8.tgz", "crypto-hash@^1.3.0": "https://registry.npmjs.org/crypto-hash/-/crypto-hash-1.3.0.tgz", "debug@4.3.3": "https://registry.npmjs.org/debug/-/debug-4.3.3.tgz", "decamelize@^4.0.0": "https://registry.npmjs.org/decamelize/-/decamelize-4.0.0.tgz", "deep-eql@^4.1.3": "https://registry.npmjs.org/deep-eql/-/deep-eql-4.1.3.tgz", "delay@^5.0.0": "https://registry.npmjs.org/delay/-/delay-5.0.0.tgz", "delayed-stream@~1.0.0": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "diff@5.0.0": "https://registry.npmjs.org/diff/-/diff-5.0.0.tgz", "diff@^3.1.0": "https://registry.npmjs.org/diff/-/diff-3.5.0.tgz", "dot-case@^3.0.4": "https://registry.npmjs.org/dot-case/-/dot-case-3.0.4.tgz", "dotenv@^16.0.3": "https://registry.npmjs.org/dotenv/-/dotenv-16.4.5.tgz", "emoji-regex@^8.0.0": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "es6-promise@^4.0.3": "https://registry.npmjs.org/es6-promise/-/es6-promise-4.2.8.tgz", "es6-promisify@^5.0.0": "https://registry.npmjs.org/es6-promisify/-/es6-promisify-5.0.0.tgz", "escalade@^3.1.1": "https://registry.npmjs.org/escalade/-/escalade-3.1.2.tgz", "escape-string-regexp@4.0.0": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "eventemitter3@^4.0.7": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz", "eventemitter3@^5.0.1": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-5.0.1.tgz", "eyes@^0.1.8": "https://registry.npmjs.org/eyes/-/eyes-0.1.8.tgz", "fast-stable-stringify@^1.0.0": "https://registry.npmjs.org/fast-stable-stringify/-/fast-stable-stringify-1.0.0.tgz", "file-uri-to-path@1.0.0": "https://registry.npmjs.org/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz", "fill-range@^7.0.1": "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz", "find-up@5.0.0": "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz", "flat@^5.0.2": "https://registry.npmjs.org/flat/-/flat-5.0.2.tgz", "follow-redirects@^1.15.6": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.6.tgz", "form-data@^4.0.0": "https://registry.npmjs.org/form-data/-/form-data-4.0.0.tgz", "fs.realpath@^1.0.0": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "fsevents@~2.3.2": "https://registry.yarnpkg.com/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6", "get-caller-file@^2.0.5": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "get-func-name@^2.0.1": "https://registry.npmjs.org/get-func-name/-/get-func-name-2.0.2.tgz", "get-func-name@^2.0.2": "https://registry.npmjs.org/get-func-name/-/get-func-name-2.0.2.tgz", "glob-parent@~5.1.2": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "glob@7.2.0": "https://registry.npmjs.org/glob/-/glob-7.2.0.tgz", "growl@1.10.5": "https://registry.npmjs.org/growl/-/growl-1.10.5.tgz", "has-flag@^4.0.0": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "he@1.2.0": "https://registry.npmjs.org/he/-/he-1.2.0.tgz", "humanize-ms@^1.2.1": "https://registry.npmjs.org/humanize-ms/-/humanize-ms-1.2.1.tgz", "ieee754@^1.2.1": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "inflight@^1.0.4": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "inherits@2": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "is-binary-path@~2.1.0": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz", "is-extglob@^2.1.1": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "is-fullwidth-code-point@^3.0.0": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "is-glob@^4.0.1": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-glob@~4.0.1": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-number@^7.0.0": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz", "is-plain-obj@^2.1.0": "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-2.1.0.tgz", "is-unicode-supported@^0.1.0": "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz", "isexe@^2.0.0": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "isomorphic-ws@^4.0.1": "https://registry.npmjs.org/isomorphic-ws/-/isomorphic-ws-4.0.1.tgz", "jayson@^4.0.0": "https://registry.npmjs.org/jayson/-/jayson-4.2.0.tgz", "jayson@^4.1.0": "https://registry.npmjs.org/jayson/-/jayson-4.2.0.tgz", "jayson@^4.1.1": "https://registry.npmjs.org/jayson/-/jayson-4.2.0.tgz", "jito-ts@^4.1.0": "https://registry.npmjs.org/jito-ts/-/jito-ts-4.1.0.tgz", "js-yaml@4.1.0": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz", "json-stringify-safe@^5.0.1": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "json5@^1.0.2": "https://registry.npmjs.org/json5/-/json5-1.0.2.tgz", "locate-path@^6.0.0": "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz", "lodash.camelcase@^4.3.0": "https://registry.npmjs.org/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz", "log-symbols@4.1.0": "https://registry.npmjs.org/log-symbols/-/log-symbols-4.1.0.tgz", "long@^5.0.0": "https://registry.npmjs.org/long/-/long-5.2.3.tgz", "loupe@^2.3.6": "https://registry.npmjs.org/loupe/-/loupe-2.3.7.tgz", "lower-case@^2.0.2": "https://registry.npmjs.org/lower-case/-/lower-case-2.0.2.tgz", "make-error@^1.1.1": "https://registry.npmjs.org/make-error/-/make-error-1.3.6.tgz", "mime-db@1.52.0": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "mime-types@^2.1.12": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "minimatch@4.2.1": "https://registry.npmjs.org/minimatch/-/minimatch-4.2.1.tgz", "minimatch@^3.0.4": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "minimist@^1.2.0": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "minimist@^1.2.6": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "mkdirp@^0.5.1": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz", "mocha@^9.0.3": "https://registry.npmjs.org/mocha/-/mocha-9.2.2.tgz", "ms@2.1.2": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "ms@2.1.3": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "ms@^2.0.0": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "nanoid@3.3.1": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.1.tgz", "no-case@^3.0.4": "https://registry.npmjs.org/no-case/-/no-case-3.0.4.tgz", "node-fetch@^2.6.12": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz", "node-fetch@^2.6.7": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz", "node-fetch@^2.7.0": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz", "node-gyp-build@^4.3.0": "https://registry.npmjs.org/node-gyp-build/-/node-gyp-build-4.8.1.tgz", "normalize-path@^3.0.0": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "normalize-path@~3.0.0": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "once@^1.3.0": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "p-limit@^3.0.2": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz", "p-locate@^5.0.0": "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz", "pako@^2.0.3": "https://registry.npmjs.org/pako/-/pako-2.1.0.tgz", "path-exists@^4.0.0": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz", "path-is-absolute@^1.0.0": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "pathval@^1.1.1": "https://registry.npmjs.org/pathval/-/pathval-1.1.1.tgz", "picomatch@^2.0.4": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.2.1": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "prettier@^2.6.2": "https://registry.npmjs.org/prettier/-/prettier-2.8.8.tgz", "protobufjs@^7.2.5": "https://registry.npmjs.org/protobufjs/-/protobufjs-7.3.2.tgz", "proxy-from-env@^1.1.0": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "randombytes@^2.1.0": "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz", "readdirp@~3.6.0": "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz", "require-directory@^2.1.1": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "rpc-websockets@^7.5.1": "https://registry.npmjs.org/rpc-websockets/-/rpc-websockets-7.11.2.tgz", "rpc-websockets@^9.0.2": "https://registry.npmjs.org/rpc-websockets/-/rpc-websockets-9.1.1.tgz", "safe-buffer@^5.0.1": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@^5.1.0": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "serialize-javascript@6.0.0": "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-6.0.0.tgz", "snake-case@^3.0.4": "https://registry.npmjs.org/snake-case/-/snake-case-3.0.4.tgz", "source-map-support@^0.5.6": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz", "source-map@^0.6.0": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "stream-chain@^2.2.5": "https://registry.npmjs.org/stream-chain/-/stream-chain-2.2.5.tgz", "stream-json@^1.9.1": "https://registry.npmjs.org/stream-json/-/stream-json-1.9.1.tgz", "string-width@^4.1.0": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^4.2.0": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^4.2.3": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "strip-ansi@^6.0.0": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^6.0.1": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-bom@^3.0.0": "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz", "strip-json-comments@3.1.1": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "superstruct@^0.14.2": "https://registry.npmjs.org/superstruct/-/superstruct-0.14.2.tgz", "superstruct@^0.15.4": "https://registry.npmjs.org/superstruct/-/superstruct-0.15.5.tgz", "superstruct@^1.0.3": "https://registry.npmjs.org/superstruct/-/superstruct-1.0.4.tgz", "superstruct@^2.0.2": "https://registry.npmjs.org/superstruct/-/superstruct-2.0.2.tgz", "supports-color@8.1.1": "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz", "supports-color@^7.1.0": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "text-encoding-utf-8@^1.0.2": "https://registry.npmjs.org/text-encoding-utf-8/-/text-encoding-utf-8-1.0.2.tgz", "to-regex-range@^5.0.1": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "toml@^3.0.0": "https://registry.npmjs.org/toml/-/toml-3.0.0.tgz", "tr46@~0.0.3": "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz", "ts-mocha@^10.0.0": "https://registry.npmjs.org/ts-mocha/-/ts-mocha-10.0.0.tgz", "ts-node@7.0.1": "https://registry.npmjs.org/ts-node/-/ts-node-7.0.1.tgz", "tsconfig-paths@^3.5.0": "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-3.15.0.tgz", "tslib@^2.0.3": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "tslib@^2.8.0": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "type-detect@^4.0.0": "https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz", "type-detect@^4.0.8": "https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz", "typescript@^4.3.5": "https://registry.npmjs.org/typescript/-/typescript-4.9.5.tgz", "undici-types@~5.26.4": "https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz", "utf-8-validate@^5.0.2": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-5.0.10.tgz", "uuid@^8.3.2": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "webidl-conversions@^3.0.0": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "whatwg-url@^5.0.0": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz", "which@2.0.2": "https://registry.npmjs.org/which/-/which-2.0.2.tgz", "workerpool@6.2.0": "https://registry.npmjs.org/workerpool/-/workerpool-6.2.0.tgz", "wrap-ansi@^7.0.0": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "wrappy@1": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "ws@^7.5.10": "https://registry.npmjs.org/ws/-/ws-7.5.10.tgz", "ws@^8.5.0": "https://registry.npmjs.org/ws/-/ws-8.17.0.tgz", "y18n@^5.0.5": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz", "yargs-parser@20.2.4": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.4.tgz", "yargs-parser@^20.2.2": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.4.tgz", "yargs-parser@^21.1.1": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz", "yargs-unparser@2.0.0": "https://registry.npmjs.org/yargs-unparser/-/yargs-unparser-2.0.0.tgz", "yargs@16.2.0": "https://registry.npmjs.org/yargs/-/yargs-16.2.0.tgz", "yargs@^17.7.2": "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz", "yn@^2.0.0": "https://registry.npmjs.org/yn/-/yn-2.0.0.tgz", "yocto-queue@^0.1.0": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz"}, "files": [], "artifacts": {"bigint-buffer@1.1.5": ["build"]}}