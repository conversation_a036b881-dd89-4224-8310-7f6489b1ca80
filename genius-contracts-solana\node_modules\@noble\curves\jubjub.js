"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.groupHash = exports.findGroupHash = exports.jubjub = void 0;
/**
 * @deprecated
 * @module
 */
const misc_ts_1 = require("./misc.js");
/** @deprecated use `import { jubjub } from '@noble/curves/misc.js';` */
exports.jubjub = misc_ts_1.jubjub;
/** @deprecated use `import { jubjub_findGroupHash } from '@noble/curves/misc.js';` */
exports.findGroupHash = misc_ts_1.jubjub_findGroupHash;
/** @deprecated use `import { jubjub_groupHash } from '@noble/curves/misc.js';` */
exports.groupHash = misc_ts_1.jubjub_groupHash;
//# sourceMappingURL=jubjub.js.map