/**
 * @deprecated
 * @module
 */
import { jubjub_findGroupHash, jubjub_groupHash, jubjub as jubjubn } from './misc.ts';
/** @deprecated use `import { jubjub } from '@noble/curves/misc.js';` */
export declare const jubjub: typeof jubjubn;
/** @deprecated use `import { jubjub_findGroupHash } from '@noble/curves/misc.js';` */
export declare const findGroupHash: typeof jubjub_findGroupHash;
/** @deprecated use `import { jubjub_groupHash } from '@noble/curves/misc.js';` */
export declare const groupHash: typeof jubjub_groupHash;
//# sourceMappingURL=jubjub.d.ts.map