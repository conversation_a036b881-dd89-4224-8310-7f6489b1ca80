{"version": 3, "file": "state.js", "sourceRoot": "", "sources": ["../../../../src/extensions/metadataPointer/state.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,SAAS,EAAE,MAAM,6BAA6B,CAAC;AACxD,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAE5C,OAAO,EAAE,aAAa,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAUtE,oEAAoE;AACpE,MAAM,CAAC,MAAM,qBAAqB,GAAG,MAAM,CAAuD;IAC9F,SAAS,CAAC,WAAW,CAAC;IACtB,SAAS,CAAC,iBAAiB,CAAC;CAC/B,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,qBAAqB,GAAG,qBAAqB,CAAC,IAAI,CAAC;AAEhE,MAAM,UAAU,uBAAuB,CAAC,IAAU;IAC9C,MAAM,aAAa,GAAG,gBAAgB,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IACpF,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;QACzB,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,qBAAqB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAEnF,wCAAwC;QACxC,OAAO;YACH,SAAS,EAAE,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;YACjE,eAAe,EAAE,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe;SACtF,CAAC;IACN,CAAC;SAAM,CAAC;QACJ,OAAO,IAAI,CAAC;IAChB,CAAC;AACL,CAAC"}