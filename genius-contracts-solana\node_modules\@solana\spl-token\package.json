{"name": "@solana/spl-token", "description": "SPL Token Program JS API", "version": "0.4.6", "author": "Solana Labs Maintainers <<EMAIL>>", "repository": "https://github.com/solana-labs/solana-program-library", "license": "Apache-2.0", "type": "module", "sideEffects": false, "engines": {"node": ">=16"}, "files": ["lib", "src", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "main": "./lib/cjs/index.js", "module": "./lib/esm/index.js", "types": "./lib/types/index.d.ts", "exports": {"types": "./lib/types/index.d.ts", "require": "./lib/cjs/index.js", "import": "./lib/esm/index.js"}, "scripts": {"nuke": "shx rm -rf node_modules package-lock.json || true", "reinstall": "npm run nuke && npm install", "clean": "shx rm -rf lib **/*.tsbuildinfo || true", "build": "tsc --build --verbose tsconfig.all.json", "postbuild": "shx echo '{ \"type\": \"commonjs\" }' > lib/cjs/package.json", "build:program": "cargo build-sbf --manifest-path=../program/Cargo.toml && cargo build-sbf --manifest-path=../program-2022/Cargo.toml && cargo build-sbf --manifest-path=../../associated-token-account/program/Cargo.toml && cargo build-sbf --manifest-path=../transfer-hook/example/Cargo.toml", "watch": "tsc --build --verbose --watch tsconfig.all.json", "release": "npm run clean && npm run build", "fmt": "prettier --write '{*,**/*}.{ts,tsx,js,jsx,json}'", "lint": "prettier --check '{*,**/*}.{ts,tsx,js,jsx,json}' && eslint --max-warnings 0 .", "lint:fix": "npm run fmt && eslint --fix .", "example": "node --experimental-specifier-resolution=node --loader ts-node/esm examples/createMintAndTransferTokens.ts", "test": "npm run test:unit && npm run test:e2e-built && npm run test:e2e-native && npm run test:e2e-2022", "test:unit": "mocha test/unit", "test:e2e-built": "start-server-and-test 'solana-test-validator --bpf-program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA ../../target/deploy/spl_token.so --reset --quiet' http://127.0.0.1:8899/health 'mocha test/e2e'", "test:e2e-2022": "TEST_PROGRAM_ID=TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb start-server-and-test 'solana-test-validator --bpf-program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL ../../target/deploy/spl_associated_token_account.so --bpf-program TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb ../../target/deploy/spl_token_2022.so --bpf-program TokenHookExampLe8smaVNrxTBezWTRbEwxwb1Zykrb ../../target/deploy/spl_transfer_hook_example.so --reset --quiet' http://127.0.0.1:8899/health 'mocha test/e2e*'", "test:e2e-native": "start-server-and-test 'solana-test-validator --reset --quiet' http://127.0.0.1:8899/health 'mocha test/e2e'", "test:build-programs": "cargo build-sbf --manifest-path ../program/Cargo.toml && cargo build-sbf --manifest-path ../program-2022/Cargo.toml && cargo build-sbf --manifest-path ../../associated-token-account/program/Cargo.toml", "deploy": "npm run deploy:docs", "docs": "shx rm -rf docs && typedoc && shx cp .nojekyll docs/", "deploy:docs": "npm run docs && gh-pages --dest token/js --dist docs --dotfiles"}, "peerDependencies": {"@solana/web3.js": "^1.91.6"}, "dependencies": {"@solana/buffer-layout": "^4.0.0", "@solana/buffer-layout-utils": "^0.2.0", "@solana/spl-token-group": "^0.0.4", "@solana/spl-token-metadata": "^0.1.4", "buffer": "^6.0.3"}, "devDependencies": {"@solana/codecs-strings": "2.0.0-preview.2", "@solana/spl-memo": "0.2.4", "@solana/web3.js": "^1.91.6", "@types/chai-as-promised": "^7.1.4", "@types/chai": "^4.3.14", "@types/mocha": "^10.0.6", "@types/node": "^20.12.7", "@types/node-fetch": "^2.6.11", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "chai": "^5.1.0", "chai-as-promised": "^7.1.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-require-extensions": "^0.1.1", "gh-pages": "^6.1.1", "mocha": "^10.4.0", "prettier": "^3.2.5", "process": "^0.11.10", "shx": "^0.3.4", "start-server-and-test": "^2.0.3", "ts-node": "^10.9.2", "typedoc": "^0.25.13", "typescript": "^5.4.5"}}