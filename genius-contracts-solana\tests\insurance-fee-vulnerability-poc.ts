import * as anchor from "@coral-xyz/anchor";
import { Program, BorshCoder, SystemCoder, EventParser, web3 } from "@coral-xyz/anchor";
import { Genius } from "../target/types/genius";
import { createMint, getOrCreateAssociatedTokenAccount, mintTo } from "@solana/spl-token";
import { ComputeBudgetProgram, Keypair } from "@solana/web3.js";
import { assert } from "chai";
import bs58 from "bs58";

/**
 * PROOF OF CONCEPT: Insurance Fee Vulnerability
 * 
 * This test demonstrates that insurance fees accumulate indefinitely without any mechanism
 * to reclaim them, causing available liquidity to permanently decrease over time.
 * 
 * The vulnerability exists because:
 * 1. getAvailableLiquidity() excludes unclaimed_insurance_fee from available liquidity
 * 2. unclaimed_insurance_fee is incremented on every order but never decremented
 * 3. The claim_fees instruction only handles Base, LP, and Protocol fees - NOT Insurance fees
 * 4. There's no FeeType::Insurance variant in the enum
 */

describe("Insurance Fee Vulnerability POC", () => {
  // Configure the client to use the local cluster
  anchor.setProvider(anchor.AnchorProvider.env());

  const program = anchor.workspace.Genius as Program<Genius>;
  const provider = anchor.AnchorProvider.env();
  const wallet = anchor.AnchorProvider.env().wallet;

  // Test accounts
  const admin = Keypair.generate();
  const orchestrator = Keypair.generate();
  const user1 = Keypair.generate();
  const user2 = Keypair.generate();
  const payer = Keypair.generate();

  let usdcMint: anchor.web3.PublicKey;
  let tokenOutMint: anchor.web3.PublicKey;
  let vault: anchor.web3.PublicKey;
  let vaultAta: any;

  const stringToUint8Array = (hexString: string) => {
    if (hexString.startsWith("0x")) {
      hexString = hexString.slice(2);
    }
    if (hexString.length < 64) {
      hexString = hexString.padStart(64, "0");
    }
    const buffer = Buffer.from(hexString, "hex");
    const res = new Uint8Array(32);
    res.set(buffer);
    return res;
  };

  before(async () => {
    // Airdrop SOL to test accounts
    await provider.connection.requestAirdrop(admin.publicKey, 10 * anchor.web3.LAMPORTS_PER_SOL);
    await provider.connection.requestAirdrop(orchestrator.publicKey, 10 * anchor.web3.LAMPORTS_PER_SOL);
    await provider.connection.requestAirdrop(user1.publicKey, 10 * anchor.web3.LAMPORTS_PER_SOL);
    await provider.connection.requestAirdrop(user2.publicKey, 10 * anchor.web3.LAMPORTS_PER_SOL);
    await provider.connection.requestAirdrop(payer.publicKey, 10 * anchor.web3.LAMPORTS_PER_SOL);

    // Wait for airdrops
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Create USDC mint
    usdcMint = await createMint(
      provider.connection,
      payer,
      payer.publicKey,
      null,
      6
    );

    // Create token out mint
    tokenOutMint = await createMint(
      provider.connection,
      payer,
      payer.publicKey,
      null,
      18
    );

    // Get vault PDA
    [vault] = await anchor.web3.PublicKey.findProgramAddress(
      [Buffer.from("vault-seed")],
      program.programId
    );

    // Create vault ATA
    vaultAta = await getOrCreateAssociatedTokenAccount(
      provider.connection,
      payer,
      usdcMint,
      vault,
      true
    );

    // Create user ATAs
    await getOrCreateAssociatedTokenAccount(
      provider.connection,
      payer,
      usdcMint,
      user1.publicKey
    );

    await getOrCreateAssociatedTokenAccount(
      provider.connection,
      payer,
      usdcMint,
      user2.publicKey
    );

    // Mint initial USDC to vault for liquidity
    await mintTo(provider.connection, payer, usdcMint, vaultAta.address, payer, **********); // 1000 USDC
  });

  it("Initialize the program", async () => {
    await program.methods
      .initialize()
      .accounts({
        admin: admin.publicKey,
        usdcMint,
      })
      .signers([admin])
      .rpc();
  });

  it("Setup orchestrator and fee tiers", async () => {
    // Add orchestrator
    await program.methods
      .addOrchestrator(true, true, true, true, true, true)
      .accounts({
        admin: admin.publicKey,
        orchestrator: orchestrator.publicKey,
      })
      .signers([admin])
      .rpc();

    // Set fee tiers (40 bps = 0.4%)
    await program.methods
      .setFeeTiers(
        [new anchor.BN(0)],
        [new anchor.BN(40)]
      )
      .accounts({
        admin: admin.publicKey,
      })
      .signers([admin])
      .rpc();

    // Set insurance fee tiers (10 bps = 0.1%)
    await program.methods
      .setInsuranceFeeTiers(
        [new anchor.BN(0)],
        [new anchor.BN(10)]
      )
      .accounts({
        admin: admin.publicKey,
      })
      .signers([admin])
      .rpc();

    // Set protocol fee fraction (10% of base fee)
    await program.methods
      .setProtocolFeeFraction(new anchor.BN(1), new anchor.BN(10))
      .accounts({
        admin: admin.publicKey,
      })
      .signers([admin])
      .rpc();

    // Set target chain min fee
    await program.methods
      .setTargetChainMinFee(2, new anchor.BN(5))
      .accounts({
        admin: admin.publicKey,
        usdcMint,
      })
      .signers([admin])
      .rpc();
  });

  it("POC: Insurance fees accumulate without reclaim mechanism", async () => {
    // Get initial asset state
    const [assetPda] = await anchor.web3.PublicKey.findProgramAddress(
      [Buffer.from("asset-seed")],
      program.programId
    );

    const initialAsset = await program.account.asset.fetch(assetPda);
    console.log("Initial unclaimed insurance fee:", initialAsset.unclaimedInsuranceFee.toNumber());

    // Create multiple orders to accumulate insurance fees
    const orderAmount = 100000; // 100 USDC (6 decimals)
    const orderFee = 50; // Sufficient fee to cover all costs
    const numOrders = 5;

    for (let i = 0; i < numOrders; i++) {
      const seed = stringToUint8Array(`0x${i.toString().padStart(64, '0')}`);
      
      await program.methods
        .createOrder(
          new anchor.BN(orderAmount),
          [...seed],
          [...seed],
          Array.from(user1.publicKey.toBytes()),
          1, // src chain
          2, // dest chain
          Array.from(usdcMint.toBytes()),
          new anchor.BN(orderFee),
          "**********000000000", // min amount out
          Array.from(tokenOutMint.toBytes())
        )
        .accounts({
          trader: orchestrator.publicKey,
          usdcMint: usdcMint,
        })
        .signers([orchestrator])
        .rpc();
    }

    // Check asset state after orders
    const assetAfterOrders = await program.account.asset.fetch(assetPda);
    console.log("Unclaimed insurance fee after orders:", assetAfterOrders.unclaimedInsuranceFee.toNumber());
    console.log("Unclaimed base fee:", assetAfterOrders.unclaimedBaseFee.toNumber());
    console.log("Unclaimed LP fee:", assetAfterOrders.unclaimedLpFee.toNumber());
    console.log("Unclaimed protocol fee:", assetAfterOrders.unclaimedProtocolFee.toNumber());

    // Verify insurance fees have accumulated
    assert.isTrue(assetAfterOrders.unclaimedInsuranceFee.toNumber() > 0, "Insurance fees should have accumulated");

    // Try to claim base fees (this should work)
    const baseFeeToClaim = Math.min(10, assetAfterOrders.unclaimedBaseFee.toNumber());
    if (baseFeeToClaim > 0) {
      await program.methods
        .claimFees(new anchor.BN(baseFeeToClaim), { base: {} })
        .accounts({
          orchestrator: orchestrator.publicKey,
          usdcMint: usdcMint,
        })
        .signers([orchestrator])
        .rpc();
      console.log("Successfully claimed base fees:", baseFeeToClaim);
    }

    // Try to claim LP fees (this should work)
    const lpFeeToClaim = Math.min(10, assetAfterOrders.unclaimedLpFee.toNumber());
    if (lpFeeToClaim > 0) {
      await program.methods
        .claimFees(new anchor.BN(lpFeeToClaim), { lp: {} })
        .accounts({
          orchestrator: orchestrator.publicKey,
          usdcMint: usdcMint,
        })
        .signers([orchestrator])
        .rpc();
      console.log("Successfully claimed LP fees:", lpFeeToClaim);
    }

    // Try to claim protocol fees (this should work)
    const protocolFeeToClaim = Math.min(5, assetAfterOrders.unclaimedProtocolFee.toNumber());
    if (protocolFeeToClaim > 0) {
      await program.methods
        .claimFees(new anchor.BN(protocolFeeToClaim), { protocol: {} })
        .accounts({
          orchestrator: orchestrator.publicKey,
          usdcMint: usdcMint,
        })
        .signers([orchestrator])
        .rpc();
      console.log("Successfully claimed protocol fees:", protocolFeeToClaim);
    }

    // Check final asset state
    const finalAsset = await program.account.asset.fetch(assetPda);
    console.log("Final unclaimed insurance fee:", finalAsset.unclaimedInsuranceFee.toNumber());
    console.log("Final unclaimed base fee:", finalAsset.unclaimedBaseFee.toNumber());
    console.log("Final unclaimed LP fee:", finalAsset.unclaimedLpFee.toNumber());
    console.log("Final unclaimed protocol fee:", finalAsset.unclaimedProtocolFee.toNumber());

    // VULNERABILITY DEMONSTRATION:
    // 1. Insurance fees have accumulated but cannot be claimed
    assert.isTrue(finalAsset.unclaimedInsuranceFee.toNumber() > 0, 
      "Insurance fees remain unclaimed - this is the vulnerability!");

    // 2. Other fees have been reduced through claiming
    assert.isTrue(finalAsset.unclaimedBaseFee.toNumber() < assetAfterOrders.unclaimedBaseFee.toNumber() ||
                  finalAsset.unclaimedLpFee.toNumber() < assetAfterOrders.unclaimedLpFee.toNumber() ||
                  finalAsset.unclaimedProtocolFee.toNumber() < assetAfterOrders.unclaimedProtocolFee.toNumber(),
      "Other fees should be claimable and reduced");

    // 3. Available liquidity calculation excludes insurance fees permanently
    console.log("\n=== VULNERABILITY IMPACT ===");
    console.log("Insurance fees that cannot be reclaimed:", finalAsset.unclaimedInsuranceFee.toNumber());
    console.log("This amount is permanently excluded from available liquidity calculations");
    console.log("Over time, this will cause available liquidity to decrease indefinitely");
    console.log("Potential for griefing attacks by creating many small orders to accumulate fees");

    // NOTE: We cannot test claiming insurance fees because there's no FeeType::Insurance variant
    // This proves the vulnerability - there's literally no way to claim insurance fees!
  });
});
